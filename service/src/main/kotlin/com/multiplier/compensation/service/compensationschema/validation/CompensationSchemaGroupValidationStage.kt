package com.multiplier.compensation.service.compensationschema.validation

import com.multiplier.compensation.service.common.validationpipeline.common.stages.PrimaryGroupValidationStage
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaDraft
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaValidationContext
import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaCountryCodeGroupValidator
import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaNameGroupValidator
import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaTagsGroupValidator
import org.springframework.stereotype.Component

@Component
class CompensationSchemaGroupValidationStage(
    schemaNameGroupValidator: SchemaNameGroupValidator,
    schemaTagsGroupValidator: SchemaTagsGroupValidator,
    schemaCountryCodeGroupValidator: SchemaCountryCodeGroupValidator,
) : PrimaryGroupValidationStage<CompensationSchemaValidationContext, CompensationSchemaDraft>() {
    init {
        setValidators(
            schemaNameGroupValidator,
            schemaCountryCodeGroupValidator,
            schemaTagsGroupValidator,
        )
    }
}
