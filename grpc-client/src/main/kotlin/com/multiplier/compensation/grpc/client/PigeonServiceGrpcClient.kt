package com.multiplier.compensation.grpc.client

import com.multiplier.compensation.domain.common.exception.DownstreamServiceException
import com.multiplier.compensation.domain.common.exception.config.DownstreamServiceErrorCode
import com.multiplier.compensation.domain.notification.PigeonEmailNotificationRequest
import com.multiplier.compensation.grpc.client.adapter.PigeonServiceAdapter
import com.multiplier.compensation.grpc.client.mapper.toEmailAttachments
import com.multiplier.compensation.grpc.client.mapper.toEmailBody
import com.multiplier.compensation.grpc.client.mapper.toStringWithEmailsMasked
import com.multiplier.pigeonservice.PigeonNotificationClient
import com.multiplier.pigeonservice.dto.Attachment
import com.multiplier.pigeonservice.dto.PigeonEmailNotificationDTO
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
@Primary
@Profile("!(test|local)")
class PigeonServiceGrpcClient(
    private val pigeonNotificationClient: PigeonNotificationClient,
) : PigeonServiceAdapter {
    override suspend fun send(emailRequest: PigeonEmailNotificationRequest): String = try {
        log.info { "Sending email ${emailRequest.toStringWithEmailsMasked()}" }

        val notificationBody = emailRequest.toEmailBody()
        val attachmentList: List<Attachment> = emailRequest.toEmailAttachments()

        val pigeonNotificationDTO = PigeonEmailNotificationDTO(
            notificationBody,
            // Metadata for attachments
            emptyMap(),
            attachmentList,
        )

        // Returns an ID that can be used to track status updates.
        pigeonNotificationClient.send(pigeonNotificationDTO)
    } catch (exception: Exception) {
        throw DownstreamServiceException(
            errorCode = DownstreamServiceErrorCode.PigeonServiceFailed,
            message = "Error while sending email notification",
            exception = exception,
            context = mapOf(
                "emailType" to emailRequest.type.name,
                "subject" to emailRequest.subject,
            ),
        )
    }
}

@Service
@Profile("test|local")
class MockPigeonServiceGrpcClient : PigeonServiceAdapter {
    override suspend fun send(emailRequest: PigeonEmailNotificationRequest): String {
        log.warn { "Mocking send $emailRequest. This should never happen outside of local/test environments." }
        return "mock-notification-id"
    }
}
