package com.multiplier.compensation.service.compensation.dto

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class UpsertCompensationByMemberDtoTest {
    @Test
    fun `UpsertCompensationByMemberRequest should be created with valid data`() {
        // Given
        val contractId = 556851L
        val keyValueInputs = listOf(
            KeyValuePair("COMPONENT_NAME", "Test Component"),
            KeyValuePair("BILLING_RATE_TYPE", "Value"),
            KeyValuePair("BILLING_RATE", "100.0"),
            Key<PERSON><PERSON>uePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
            KeyValuePair("COUNTRY", "CAN"),
        )

        // When
        val request = UpsertCompensationByMemberRequest(
            contractId = contractId,
            keyValueInputs = keyValueInputs,
        )

        // Then
        assertNotNull(request)
        assertEquals(contractId, request.contractId)
        assertEquals(5, request.keyValueInputs.size)
        assertEquals("Test Component", request.keyValueInputs.find { it.key == "COMPONENT_NAME" }?.value)
        assertEquals("Value", request.keyValueInputs.find { it.key == "BILLING_RATE_TYPE" }?.value)
        assertEquals("100.0", request.keyValueInputs.find { it.key == "BILLING_RATE" }?.value)
        assertEquals("EMPLOYEE_CONTRIBUTION", request.keyValueInputs.find { it.key == "CATEGORY" }?.value)
        assertEquals("CAN", request.keyValueInputs.find { it.key == "COUNTRY" }?.value)
    }

    @Test
    fun `UpsertCompensationByMemberRequest should handle empty key value inputs`() {
        // Given
        val contractId = 556851L
        val keyValueInputs = emptyList<KeyValuePair>()

        // When
        val request = UpsertCompensationByMemberRequest(
            contractId = contractId,
            keyValueInputs = keyValueInputs,
        )

        // Then
        assertNotNull(request)
        assertEquals(contractId, request.contractId)
        assertTrue(request.keyValueInputs.isEmpty())
    }

    @Test
    fun `UpsertCompensationByMemberRequest should handle null values in key value inputs`() {
        // Given
        val contractId = 556851L
        val keyValueInputs = listOf(
            KeyValuePair("COMPONENT_NAME", null),
            KeyValuePair("BILLING_RATE_TYPE", "Value"),
            KeyValuePair("BILLING_RATE", null),
        )

        // When
        val request = UpsertCompensationByMemberRequest(
            contractId = contractId,
            keyValueInputs = keyValueInputs,
        )

        // Then
        assertNotNull(request)
        assertEquals(contractId, request.contractId)
        assertEquals(3, request.keyValueInputs.size)
        assertEquals(null, request.keyValueInputs.find { it.key == "COMPONENT_NAME" }?.value)
        assertEquals("Value", request.keyValueInputs.find { it.key == "BILLING_RATE_TYPE" }?.value)
        assertEquals(null, request.keyValueInputs.find { it.key == "BILLING_RATE" }?.value)
    }

    @Test
    fun `UpsertCompensationByMemberResponse should be created with success status`() {
        // Given
        val contractId = 556851L
        val status = OperationStatus.SUCCESS
        val validationResults = emptyList<CellValidationResult>()

        // When
        val response = UpsertCompensationByMemberResponse(
            contractId = contractId,
            status = status,
            componentValidationResults = validationResults,
        )

        // Then
        assertNotNull(response)
        assertEquals(contractId, response.contractId)
        assertEquals(OperationStatus.SUCCESS, response.status)
        assertTrue(response.componentValidationResults.isEmpty())
    }

    @Test
    fun `UpsertCompensationByMemberResponse should be created with failure status and validation errors`() {
        // Given
        val contractId = 556851L
        val status = OperationStatus.FAILURE
        val validationResults = listOf(
            CellValidationResult(
                field = KeyValuePair("COMPONENT_NAME", ""),
                type = ValidationResultType.ERROR,
                message = "Field [COMPONENT_NAME] is required",
            ),
            CellValidationResult(
                field = KeyValuePair("CATEGORY", "INVALID_CATEGORY"),
                type = ValidationResultType.ERROR,
                message = "Only EMPLOYEE_CONTRIBUTION category is allowed",
            ),
        )

        // When
        val response = UpsertCompensationByMemberResponse(
            contractId = contractId,
            status = status,
            componentValidationResults = validationResults,
        )

        // Then
        assertNotNull(response)
        assertEquals(contractId, response.contractId)
        assertEquals(OperationStatus.FAILURE, response.status)
        assertEquals(2, response.componentValidationResults.size)

        val firstError = response.componentValidationResults[0]
        assertEquals("COMPONENT_NAME", firstError.field.key)
        assertEquals("", firstError.field.value)
        assertEquals(ValidationResultType.ERROR, firstError.type)
        assertEquals("Field [COMPONENT_NAME] is required", firstError.message)

        val secondError = response.componentValidationResults[1]
        assertEquals("CATEGORY", secondError.field.key)
        assertEquals("INVALID_CATEGORY", secondError.field.value)
        assertEquals(ValidationResultType.ERROR, secondError.type)
        assertEquals("Only EMPLOYEE_CONTRIBUTION category is allowed", secondError.message)
    }

    @Test
    fun `UpsertCompensationByMemberResponse should handle partial success status`() {
        // Given
        val contractId = 556851L
        val status = OperationStatus.PARTIAL_SUCCESS
        val validationResults = listOf(
            CellValidationResult(
                field = KeyValuePair("BILLING_RATE", "100.0"),
                type = ValidationResultType.WARN,
                message = "Rate is higher than recommended",
            ),
        )

        // When
        val response = UpsertCompensationByMemberResponse(
            contractId = contractId,
            status = status,
            componentValidationResults = validationResults,
        )

        // Then
        assertNotNull(response)
        assertEquals(contractId, response.contractId)
        assertEquals(OperationStatus.PARTIAL_SUCCESS, response.status)
        assertEquals(1, response.componentValidationResults.size)

        val warning = response.componentValidationResults[0]
        assertEquals("BILLING_RATE", warning.field.key)
        assertEquals("100.0", warning.field.value)
        assertEquals(ValidationResultType.WARN, warning.type)
        assertEquals("Rate is higher than recommended", warning.message)
    }

    @Test
    fun `UpsertCompensationByMemberResponse should handle mixed validation result types`() {
        // Given
        val contractId = 556851L
        val status = OperationStatus.PARTIAL_SUCCESS
        val validationResults = listOf(
            CellValidationResult(
                field = KeyValuePair("COMPONENT_NAME", "Test Component"),
                type = ValidationResultType.INFO,
                message = "Component created successfully",
            ),
            CellValidationResult(
                field = KeyValuePair("BILLING_RATE", "100.0"),
                type = ValidationResultType.WARN,
                message = "Rate is higher than recommended",
            ),
            CellValidationResult(
                field = KeyValuePair("END_DATE", ""),
                type = ValidationResultType.ERROR,
                message = "End date format is invalid",
            ),
        )

        // When
        val response = UpsertCompensationByMemberResponse(
            contractId = contractId,
            status = status,
            componentValidationResults = validationResults,
        )

        // Then
        assertNotNull(response)
        assertEquals(contractId, response.contractId)
        assertEquals(OperationStatus.PARTIAL_SUCCESS, response.status)
        assertEquals(3, response.componentValidationResults.size)

        // Verify each validation result type
        val infoResult = response.componentValidationResults.find { it.type == ValidationResultType.INFO }
        assertNotNull(infoResult)
        assertEquals("Component created successfully", infoResult.message)

        val warningResult = response.componentValidationResults.find { it.type == ValidationResultType.WARN }
        assertNotNull(warningResult)
        assertEquals("Rate is higher than recommended", warningResult.message)

        val errorResult = response.componentValidationResults.find { it.type == ValidationResultType.ERROR }
        assertNotNull(errorResult)
        assertEquals("End date format is invalid", errorResult.message)
    }

    @Test
    fun `data classes should support equality and toString`() {
        // Given
        val keyValueInputs = listOf(
            KeyValuePair("COMPONENT_NAME", "Test Component"),
            KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
        )

        val request1 = UpsertCompensationByMemberRequest(556851L, keyValueInputs)
        val request2 = UpsertCompensationByMemberRequest(556851L, keyValueInputs)
        val request3 = UpsertCompensationByMemberRequest(556852L, keyValueInputs)

        val response1 = UpsertCompensationByMemberResponse(556851L, OperationStatus.SUCCESS, emptyList())
        val response2 = UpsertCompensationByMemberResponse(556851L, OperationStatus.SUCCESS, emptyList())
        val response3 = UpsertCompensationByMemberResponse(556851L, OperationStatus.FAILURE, emptyList())

        // Then
        // Test equality
        assertEquals(request1, request2)
        assertEquals(request1.hashCode(), request2.hashCode())
        assert(request1 != request3)

        assertEquals(response1, response2)
        assertEquals(response1.hashCode(), response2.hashCode())
        assert(response1 != response3)

        // Test toString (should not throw exceptions)
        assertNotNull(request1.toString())
        assertNotNull(response1.toString())
        assertTrue(request1.toString().contains("556851"))
        assertTrue(response1.toString().contains("SUCCESS"))
    }
}
