package com.multiplier.compensation.service.compensationitem.amountcalculation

import com.multiplier.common.jobs.annotations.ScheduledJob
import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.transaction.spring.scheduled
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CompensationItemAmountCalculationJob(
    private var compensationItemRepository: CompensationItemRepository,
    private val compensationItemAmountCalculationService: CompensationItemAmountCalculationService,
    @Value("\${core.scheduler.compensation-item-amount-calculation.delay-in-minutes}")
    private val delayInMinutes: Long,
    @Value("\${core.scheduler.compensation-item-amount-calculation.batch-size}")
    private val batchSize: Int,
    @Value("\${core.scheduler.compensation-item-amount-calculation.job-name}")
    private val jobName: String,
    @Value("\${core.scheduler.compensation-item-amount-calculation.frequency}")
    private val cronExp: String,
) {
    // ToDo: Use config for job name as well as platform team provides support for it
    @ScheduledJob(
        jobName = "CompensationItemAmountCalculation",
        cronExp = "\${core.scheduler.compensation-item-amount-calculation.frequency}",
    )
    fun calculateCompensationItemAmount() = scheduled(name = "CompensationItemAmountCalculation") {
        log.info { "Starting execution of [$jobName] job with cron expression [$cronExp]." }
        var compensationItems: List<CompensationItem>
        var batchNumber = 1
        do {
            compensationItems =
                compensationItemRepository.fetchCompensationItemsForAmountCalculation(delayInMinutes, batchSize)
            if (compensationItems.isNotEmpty()) {
                log.info { "Processing compensation item amount calculation batch [$batchNumber]." }
                compensationItems.forEach {
                    compensationItemAmountCalculationService.updateCalculatedAmount(it)
                }
                batchNumber++
            }
        } while (compensationItems.isNotEmpty())
        log.info { "Finished execution of the [$jobName] job." }
    }
}
