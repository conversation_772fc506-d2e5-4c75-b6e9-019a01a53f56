package com.multiplier.compensation.service.payschedule

import com.multiplier.compensation.database.repository.payschedule.PayScheduleItemRepository
import com.multiplier.compensation.database.repository.payschedule.PayScheduleRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.common.PageRequest
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.PayScheduleItem
import com.multiplier.compensation.domain.payschedule.PayScheduleRequest
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.payschedule.strategy.CompensationPayScheduleSelectionStrategyFactory
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.github.oshai.kotlinlogging.KotlinLogging
import org.jooq.exception.IntegrityConstraintViolationException
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.UUID

private val log = KotlinLogging.logger {}

/**
 * Service responsible for managing pay schedules and their item generation.
 *
 * @property payScheduleItemsGenerator Generator for creating pay schedule items
 * @property payScheduleItemRepository Repository for pay schedule item persistence
 * @property payScheduleRepository Repository for pay schedule persistence
 * @property transactional Transaction manager for database operations
 */
@Service
class PayScheduleService(
    private val payScheduleItemsGenerator: PayScheduleItemsGenerator,
    private val payScheduleItemRepository: PayScheduleItemRepository,
    private val payScheduleRepository: PayScheduleRepository,
    private val transactional: Transactional,
    private val compensationPayScheduleSelectionStrategyFactory: CompensationPayScheduleSelectionStrategyFactory,
) {
    private fun getPaySchedules(
        entityId: Long,
        excludeInactiveSchedules: Boolean,
    ): List<PaySchedule> {
        log.info {
            "Find all pay schedules for entityId [$entityId] and excludeInactiveSchedules [$excludeInactiveSchedules]"
        }

        return payScheduleRepository.findAllByEntityId(
            entityId,
            excludeInactiveSchedules,
        ).toList()
    }

    fun getPaySchedulesByIds(ids: Set<UUID>): Set<PaySchedule> {
        log.info {
            "Find all pay schedules for ids [$ids]"
        }

        return payScheduleRepository.findAllByIds(ids).toSet()
    }

    fun getPaySchedules(
        entityId: Long,
        offeringType: OfferingType,
        countryCode: CountryCode?,
        excludeInactiveSchedules: Boolean,
    ): Collection<PaySchedule> {
        log.info {
            "Find all pay schedules for countryCode [$countryCode] and entityId [$entityId] " +
                "for offeringType [$offeringType] and excludeInactiveSchedules [$excludeInactiveSchedules]"
        }
        requireCondition(
            condition = isValid(offeringType, countryCode),
            errorCode = ValidationErrorCode.InvalidPayScheduleScope,
            message = "countryCode is required when offeringType is EOR",
            context = mapOf("offeringType" to offeringType, "countryCode" to countryCode),
        )
        val paySchedules = mutableListOf<PaySchedule>()
        if (offeringType == OfferingType.EOR) {
            paySchedules.addAll(
                payScheduleRepository.findAllByCountryCodeAndConfigurationScope(
                    countryCode,
                    ConfigurationScope.COUNTRY,
                    excludeInactiveSchedules,
                ),
            )
        } else {
            paySchedules.addAll(
                getPaySchedules(
                    entityId = entityId,
                    excludeInactiveSchedules = true,
                ),
            )
        }

        return paySchedules
    }

    /**
     * Retrieves pay schedules for multiple entities within a date range.
     * The pay schedules dates are adjusted based on the latest pay schedule item for non-installment pay schedules.
     * For installment pay schedules, the pay schedule dates are returned as is.
     * @param entityIds List of entity IDs
     * @param referenceStartDate Start date for reference
     * @param referenceEndDate End date for reference
     * @return Map of entity IDs to their pay schedules
     */
    fun getPaySchedules(
        entityIds: List<Long>,
        referenceStartDate: LocalDate,
        referenceEndDate: LocalDate,
    ): Map<Long, List<PaySchedule>> {
        log.info {
            "Find pay schedule for entityIds $entityIds " +
                "and date range between [$referenceStartDate] to [$referenceEndDate] "
        }

        val allPaySchedules = payScheduleRepository.findAllByEntityIds(entityIds)

        val nonInstallmentPaySchedulesWithLatestItem =
            payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(
                payScheduleIds = allPaySchedules.filter { !it.isInstallment }.map { it.id },
                from = referenceStartDate,
                to = referenceEndDate,
            )
                .groupBy { it.payScheduleId }
                .map { it.key to it.value.last() }.toMap()

        return allPaySchedules.map { paySchedule ->
            if (paySchedule.isInstallment) {
                paySchedule
            } else {
                paySchedule.copy(
                    startDateReference = nonInstallmentPaySchedulesWithLatestItem[paySchedule.id]?.startDate
                        ?: paySchedule.startDateReference,
                    endDateReference = nonInstallmentPaySchedulesWithLatestItem[paySchedule.id]?.endDate
                        ?: paySchedule.endDateReference,
                )
            }
        }.groupBy { it.entityId }
    }

    /**
     * Retrieves pay schedules for multiple entities and/or companies with pagination.
     * The pay schedules dates are adjusted based on the latest pay schedule item for non-installment pay schedules.
     * For installment pay schedules, the pay schedule dates are returned as is.
     * @param payScheduleRequest Pay schedule request
     * @param pageRequest Page request
     * @return Pair of pay schedules grouped by entity ID and total records
     */
    fun getPaySchedulesWithPagination(
        payScheduleRequest: PayScheduleRequest,
        pageRequest: PageRequest,
    ): Pair<Map<Long, List<PaySchedule>>, Long> {
        log.info { "Finding the pay schedules for request $payScheduleRequest with pagination $pageRequest" }

        validatePayScheduleRequest(payScheduleRequest)
        val (allPaySchedules, totalRecords) = payScheduleRepository.findAllByPayScheduleRequestWithPagination(
            payScheduleRequest = payScheduleRequest,
            pageRequest = pageRequest,
        )

        val referenceStartDate = payScheduleRequest.referenceStartDate
        val referenceEndDate = payScheduleRequest.referenceEndDate

        val nonInstallmentPaySchedulesWithLatestItem =
            if (referenceStartDate != null && referenceEndDate != null) {
                payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(
                    payScheduleIds = allPaySchedules.filter { !it.isInstallment }.map { it.id },
                    from = referenceStartDate,
                    to = referenceEndDate,
                )
                    .groupBy { it.payScheduleId }
                    .map { it.key to it.value.last() }.toMap()
            } else {
                emptyMap()
            }

        val paySchedules = allPaySchedules.map { paySchedule ->
            if (paySchedule.isInstallment) {
                paySchedule
            } else {
                paySchedule.copy(
                    startDateReference = nonInstallmentPaySchedulesWithLatestItem[paySchedule.id]?.startDate
                        ?: paySchedule.startDateReference,
                    endDateReference = nonInstallmentPaySchedulesWithLatestItem[paySchedule.id]?.endDate
                        ?: paySchedule.endDateReference,
                )
            }
        }.groupBy { it.entityId }

        return paySchedules to totalRecords
    }

    /**
     * Returns pay schedule items for a specific pay schedule, within a date range.
     * Pay schedule items will also be persisted for non installment pay schedules.
     *
     * @param id Identifier of the pay schedule
     * @param from
     * * Generate items overlapping this date for non installments schedules.
     * * Generate items starting this date for installments schedules or if pay date reference type is
     *      compensation start date.(Assuming from date is the compensation start date in that case).
     * @param to Generate items until this date. The end date of the last pay schedule item will be either same or after this date.
     * @return List of pay schedule items
     * @throws InvalidArgumentException if the pay schedule with the given ID is not found or is not a non-installment pay schedule.
     */
    fun getPayScheduleItems(
        id: UUID,
        from: LocalDate,
        to: LocalDate,
    ): Collection<PayScheduleItem> {
        log.info {
            "Find all pay schedule items for pay schedule id [$id] and date range between [$from] and [$to]"
        }

        val paySchedule = payScheduleRepository.findById(id)
            ?: throw InvalidArgumentException(
                errorCode = ValidationErrorCode.PayScheduleNotFound,
                message = "Pay schedule with id [$id] not found",
                context = mapOf("payScheduleId" to id),
            )

        validateRequest(
            from = from,
            to = to,
            paySchedule = paySchedule,
        )

        return when {
            paySchedule.frequency == PayScheduleFrequency.ONE_TIME ->
                payScheduleItemsGenerator.generatePayScheduleItemsFrom(
                    startDate = from,
                    until = to,
                    paySchedule = paySchedule,
                )

            paySchedule.isInstallment ||
                paySchedule.payDateReferenceType == PayDateReference.COMPENSATION_START_DATE
            -> getPayScheduleItemsStartingFrom(
                nextCompensationItemStartDate = from,
                to = to,
                paySchedule = paySchedule,
            )

            else -> getOverlappingPayScheduleItems(
                from = from,
                to = to,
                paySchedule = paySchedule,
            )
        }
    }

    /**
     * Generates and returns pay schedule items for a specific (non-installment) pay schedule overlapping with the requested range.
     * The new pay schedule items generated are also persisted if they do not exist already in the database to optimize performance.
     *
     * @param paySchedule PaySchedule
     * @param from Generate items from this date. The start date of the first pay schedule item will be either same or before this date.
     * @param to Generate items until this date. The end date of the last pay schedule item will be either same or after this date.
     * @return List of pay schedule items
     * @throws InvalidArgumentException if the pay schedule with the given ID is not found or is not a non-installment pay schedule.
     */
    private fun getOverlappingPayScheduleItems(
        from: LocalDate,
        to: LocalDate,
        paySchedule: PaySchedule,
    ): Collection<PayScheduleItem> {
        log.info {
            "Find pay schedule items for pay schedule [${paySchedule.id}] between [$from] and [$to]"
        }

        val existingPayScheduleItems =
            payScheduleItemRepository.findAllByPayScheduleIdAndDateRangeBetween(
                payScheduleId = paySchedule.id,
                from = from,
                to = to,
            )

        val newPayScheduleItems = payScheduleItemsGenerator.generatePayScheduleItemsWithinRange(
            from = from,
            to = to,
            paySchedule = paySchedule,
            existingPayScheduleItems = existingPayScheduleItems.toList(),
        )

        persistPayScheduleItems(
            payScheduleItems = newPayScheduleItems,
            id = paySchedule.id,
        )

        return existingPayScheduleItems.plus(newPayScheduleItems).sortedBy { it.startDate }.filter {
            it.endDate >= from && it.startDate <= to
        }
    }

    /**
     * Returns the pay schedule items for specific pay schedule matching the date range. The pay schedule
     * items are generated exactly from the next compensation item start date and until the to date.
     *
     * @param paySchedule PaySchedule
     * @param nextCompensationItemStartDate The start date of the next compensation item. Important to note that the
     * first pay schedule item returned would always have the same start date as nextCompensationItemStartDate.
     * @param to Generate items until this date. The end date of the last pay schedule item will be either same or after this date.
     * @return A collection of [PayScheduleItem] objects that fall within the specified date range.
     * @throws InvalidArgumentException if the pay schedule with the given ID is not found or is not an installment pay schedule.
     */
    private fun getPayScheduleItemsStartingFrom(
        nextCompensationItemStartDate: LocalDate,
        to: LocalDate,
        paySchedule: PaySchedule,
    ): Collection<PayScheduleItem> {
        log.info {
            "Generate pay schedule items for schedule [${paySchedule.id}] starting [$nextCompensationItemStartDate] " +
                "till [$to]"
        }

        return payScheduleItemsGenerator.generatePayScheduleItemsFrom(
            startDate = nextCompensationItemStartDate,
            until = to,
            paySchedule = paySchedule,
        ).filter { it.endDate >= nextCompensationItemStartDate && it.startDate <= to }
    }

    fun getEligiblePaySchedules(request: GetEligiblePayScheduleRequest): Map<Long, List<PaySchedule>> {
        log.info { "Finding the eligible pay schedules for request $request" }
        val strategy = compensationPayScheduleSelectionStrategyFactory.getStrategy(request.offeringCode)
        return strategy.getEligiblePaySchedules(request).groupBy { it.entityId }
    }

    fun getEligibleBillingFrequencies(request: GetEligibleBillingFrequenciesRequest): List<BillingFrequency> {
        log.info { "Finding the eligible billing frequencies for request $request" }
        val strategy = compensationPayScheduleSelectionStrategyFactory.getStrategy(request.offeringCode)
        return strategy.getEligibleBillingFrequencies(request)
    }

    private fun validateRequest(
        from: LocalDate,
        to: LocalDate,
        paySchedule: PaySchedule,
    ) {
        requireCondition(
            condition = from.isBefore(to) || from.isEqual(to),
            errorCode = ValidationErrorCode.InvalidPayScheduleDateRange,
            message = "Invalid pay schedule date range requested from [$from] to [$to]",
            context = mapOf(
                "payScheduleId" to paySchedule.id,
                "entityId" to paySchedule.entityId,
            ),
        )
    }

    private fun validatePayScheduleRequest(payScheduleRequest: PayScheduleRequest) {
        requireCondition(
            condition = payScheduleRequest.companyIds != null || payScheduleRequest.entityIds != null,
            errorCode = ValidationErrorCode.InvalidPayScheduleRequest,
            message = "Either companyIds or entityIds must be provided",
            context = mapOf(
                "companyIds" to payScheduleRequest.companyIds,
                "entityIds" to payScheduleRequest.entityIds,
            ),
        )
        if (payScheduleRequest.referenceStartDate != null && payScheduleRequest.referenceEndDate != null) {
            requireCondition(
                condition = payScheduleRequest.referenceEndDate?.isAfter(payScheduleRequest.referenceStartDate)
                    ?: false,
                errorCode = ValidationErrorCode.InvalidPayScheduleDateRange,
                message = "Reference start date must be before or equal to reference end date",
                context = mapOf(
                    "startDate" to payScheduleRequest.referenceStartDate,
                    "endDate" to payScheduleRequest.referenceEndDate,
                ),
            )
        }
        if (payScheduleRequest.offeringType == OfferingCode.EOR) {
            requireCondition(
                condition = payScheduleRequest.country != null,
                errorCode = ValidationErrorCode.InvalidPayScheduleScope,
                message = "Country code is required when offering type is EOR",
                context = mapOf("offeringType" to payScheduleRequest.offeringType),
            )
        }
    }

    private fun persistPayScheduleItems(
        payScheduleItems: List<PayScheduleItem>,
        id: UUID,
    ) {
        try {
            transactional {
                payScheduleItemRepository.saveAll(
                    payScheduleItems = payScheduleItems,
                    transactionContext = this,
                )
            }
        } catch (exception: Exception) {
            if (exception.cause is IntegrityConstraintViolationException) {
                log.info(exception) {
                    "Failed to persist generated pay schedule items for pay schedule id [$id] ${
                        payScheduleItems.takeIf { it.isNotEmpty() }?.let {
                            "and date range [${it.first().startDate}] till [${it.last().endDate}]"
                        }.orEmpty()
                    } This may be expected due to concurrent calls from item generation module. "
                }
            } else {
                log.error(exception) {
                    "Unexpected error while persisting pay schedule items for id [$id] ${
                        payScheduleItems.takeIf { it.isNotEmpty() }?.let {
                            "and date range [${it.first().startDate}] till [${it.last().endDate}]"
                        }.orEmpty()
                    }"
                }
            }
        }
    }

    private fun isValid(
        offeringType: OfferingType?,
        countryCode: CountryCode?,
    ): Boolean = when (offeringType) {
        OfferingType.EOR -> countryCode != null
        else -> true
    }
}
