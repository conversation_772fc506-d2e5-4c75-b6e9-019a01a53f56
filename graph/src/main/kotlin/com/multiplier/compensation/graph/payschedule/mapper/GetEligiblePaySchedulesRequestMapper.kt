package com.multiplier.compensation.graph.payschedule.mapper

import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.graph.common.mapper.toDomain
import com.multiplier.graph.types.GetEligiblePaySchedulesRequest as GraphGetEligiblePaySchedulesRequest

fun GraphGetEligiblePaySchedulesRequest.toDomain(): GetEligiblePayScheduleRequest = GetEligiblePayScheduleRequest(
    entityId = this.entityId,
    countryCode = this.countryCode.toDomain(),
    offeringCode = this.offeringCode.toDomain(),
    state = this.state,
    grossSalaryPayScheduleFrequency = this.grossSalaryPayFrequency?.toDomain(),
    billingFrequency = this.billingFrequency?.toDomain(),
    compensationCategory = this.compensationCategory?.toDomain(),
)
