package com.multiplier.compensation.database.repository.payschedule

import com.multiplier.compensation.database.mapper.common.toDatabase
import com.multiplier.compensation.database.mapper.payschedule.applyAudit
import com.multiplier.compensation.database.mapper.payschedule.common.toDatabase
import com.multiplier.compensation.database.mapper.payschedule.toAudit
import com.multiplier.compensation.database.mapper.payschedule.toDatabase
import com.multiplier.compensation.database.mapper.payschedule.toDomain
import com.multiplier.compensation.database.tables.records.PayScheduleRecord
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import com.multiplier.compensation.database.util.OrderFieldsBuilder
import com.multiplier.compensation.database.util.PayScheduleConditionBuilder.buildPayScheduleCondition
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.ConfigurationScopeConstants.SCOPE_COUNTRY_DUMMY_ENTITY_ID
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.PageRequest
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.PayScheduleRequest
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.OrderField
import org.jooq.Result
import org.jooq.SelectConditionStep
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class PayScheduleRepository(
    private val dsl: DSLContext,
) {
    fun findById(payScheduleId: UUID): PaySchedule? = dsl
        .selectFrom(PAY_SCHEDULE)
        .where(PAY_SCHEDULE.ID.eq(payScheduleId))
        .fetchSingle().toDomain()

    fun findAllByIds(payScheduleIds: Collection<UUID>): Collection<PaySchedule> = dsl
        .selectFrom(PAY_SCHEDULE)
        .where(PAY_SCHEDULE.ID.`in`(payScheduleIds))
        .fetch { it.toDomain() }

    fun findAllByCountryCodeAndConfigurationScope(
        countryCode: CountryCode?,
        configurationScope: ConfigurationScope,
        excludeInactiveSchedules: Boolean,
    ): Collection<PaySchedule> {
        var query = dsl.selectFrom(PAY_SCHEDULE).where(
            PAY_SCHEDULE.CONFIGURATION_SCOPE.eq(configurationScope.toDatabase()),
        )
        countryCode?.let {
            query = query.and(PAY_SCHEDULE.COUNTRY.eq(countryCode.toDatabase()))
        }
        if (excludeInactiveSchedules) {
            query = query.and(PAY_SCHEDULE.IS_ACTIVE.eq(true))
        }
        return query.fetch().map { it.toDomain() }
    }

    fun findAllByEntityId(
        entityId: Long,
        excludeInactiveSchedules: Boolean,
    ): Collection<PaySchedule> {
        var query = dsl
            .selectFrom(PAY_SCHEDULE)
            .where(PAY_SCHEDULE.ENTITY_ID.eq(entityId))

        if (excludeInactiveSchedules) {
            query = query.and(PAY_SCHEDULE.IS_ACTIVE.eq(true))
        }

        return query.fetch().map { it.toDomain() }
    }

    fun findAllByEntityIds(entityIds: List<Long>): Collection<PaySchedule> = dsl
        .selectFrom(PAY_SCHEDULE)
        .where(PAY_SCHEDULE.ENTITY_ID.`in`(entityIds))
        .query.fetch()
        .map { it.toDomain() }

    fun findAllByPayScheduleRequestWithPagination(
        payScheduleRequest: PayScheduleRequest,
        pageRequest: PageRequest?,
    ): Pair<Collection<PaySchedule>, Long> {
        val conditions = buildPayScheduleCondition(payScheduleRequest)
        val query = buildPayScheduleQuery(conditions)

        val totalRecords = dsl.fetchCount(query).toLong()
        val orderFields = pageRequest?.let { OrderFieldsBuilder.createOrderFields(pageRequest.sort) }.orEmpty()

        val paySchedules = executeQuery(query, orderFields, pageRequest)

        return Pair(paySchedules.map { it.toDomain() }, totalRecords)
    }

    fun findByEntityAndName(
        entityId: Long,
        payScheduleName: String,
    ): PaySchedule? = dsl.selectFrom(PAY_SCHEDULE)
        .where(PAY_SCHEDULE.ENTITY_ID.eq(entityId))
        .and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.eq(payScheduleName))
        .fetchOne()?.toDomain()

    fun findSchedulesCountByEntityAndNameAndCountry(
        entityId: Long,
        nameToCountry: Map<String, CountryCode?>,
        transactionContext: TransactionContext? = null,
        // In case this call is expected to happen within another transaction
    ): Map<Pair<String, CountryCode?>, Int> {
        val dslContextToUse = transactionContext?.let { transactionContext.trx }
            ?: dsl
        return dslContextToUse.selectFrom(PAY_SCHEDULE)
            .where(PAY_SCHEDULE.ENTITY_ID.eq(entityId))
            .and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.`in`(nameToCountry.keys))
            .and(PAY_SCHEDULE.COUNTRY.`in`(nameToCountry.values.mapNotNull { it?.toDatabase() }))
            .fetch()
            .mapNotNull { it.toDomain() }
            .groupBy { Pair(it.name, it.country) }
            .filter { it.key.second != null }
            .mapValues { it.value.size }
    }

    fun findEntityLevelSchedulesCountByNameAndCountry(
        nameToCountry: Map<String, CountryCode?>,
        transactionContext: TransactionContext? = null,
        // In case this call is expected to happen within another transaction
    ): Map<Pair<String, CountryCode?>, Int> {
        val dslContextToUse = transactionContext?.let { transactionContext.trx }
            ?: dsl
        return dslContextToUse.selectFrom(PAY_SCHEDULE)
            .where(PAY_SCHEDULE.ENTITY_ID.notEqual(SCOPE_COUNTRY_DUMMY_ENTITY_ID))
            .and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.`in`(nameToCountry.keys))
            .and(PAY_SCHEDULE.COUNTRY.`in`(nameToCountry.values.mapNotNull { it?.toDatabase() }))
            .fetch()
            .mapNotNull { it.toDomain() }
            .groupBy { Pair(it.name, it.country) }
            .filter { it.key.second != null }
            .mapValues { it.value.size }
    }

    fun findAllEligiblePaySchedules(
        request: GetEligiblePayScheduleRequest,
        scope: ConfigurationScope,
        frequencies: List<PayScheduleFrequency>,
        isInstallment: Boolean?,
        excludeInactiveSchedules: Boolean,
    ): Collection<PaySchedule> {
        val conditions = mutableListOf<Condition>(
            PAY_SCHEDULE.CONFIGURATION_SCOPE.eq(scope.toDatabase()),
            PAY_SCHEDULE.FREQUENCY.`in`(frequencies.map { it.toDatabase() }),
        )

        request.countryCode?.let {
            conditions.add(PAY_SCHEDULE.COUNTRY.eq(it.toDatabase()))
        }

        if (excludeInactiveSchedules) {
            conditions.add(PAY_SCHEDULE.IS_ACTIVE.eq(true))
        }

        isInstallment?.let {
            conditions.add(PAY_SCHEDULE.IS_INSTALLMENT.eq(it))
        }

        return dsl.selectFrom(PAY_SCHEDULE)
            .where(conditions)
            .fetch()
            .map { it.toDomain() }
    }

    fun findAllByEntityAndScopeAndFrequencies(
        entityId: Long,
        scope: ConfigurationScope,
        frequencies: List<PayScheduleFrequency>,
        isInstallment: Boolean?,
    ): Collection<PaySchedule> {
        val conditions = mutableListOf(
            PAY_SCHEDULE.ENTITY_ID.eq(entityId),
            PAY_SCHEDULE.CONFIGURATION_SCOPE.eq(scope.toDatabase()),
            PAY_SCHEDULE.FREQUENCY.`in`(frequencies.map { it.toDatabase() }),
        )

        isInstallment?.let {
            conditions += PAY_SCHEDULE.IS_INSTALLMENT.eq(it)
        }

        return dsl.selectFrom(PAY_SCHEDULE)
            .where(conditions)
            .fetch()
            .map { it.toDomain() }
    }

    /**
     * This method should be used for persisting new pay schedules.
     *
     * This method enforces the below constraints -
     * 1. DATABASE CONSTRAINT -
     *      1.a. Entity + name + country should be unique.
     * 2. BUSINESS CONSTRAINTS -
     *      2.a. Name for default schedule should not be used by any company schema (For same name + country + tag).
     *      2.b. Name for entity schedule should not be used by any default schema (For same name + country + tag).
     */
    fun saveAllEnforcingConstraints(
        paySchedules: Collection<PaySchedule>,
        transactionContext: TransactionContext,
    ) {
        val validInputSchedules = validSchedulesSatisfyingConstraints(paySchedules, transactionContext)
        val records = validInputSchedules.map { it.toDatabase().applyAudit(transactionContext.audit) }
        transactionContext.trx.batchInsert(records).execute()
        records.audit(transactionContext)
    }

    private fun validSchedulesSatisfyingConstraints(
        paySchedules: Collection<PaySchedule>,
        transactionContext: TransactionContext,
    ): List<PaySchedule> {
        val existingConflictingSchedules = findConflictingSchedules(paySchedules, transactionContext)

        val inputConflictingSchedules = paySchedules.filter { schedule ->
            existingConflictingSchedules[Pair(schedule.name, schedule.country)]?.let { it > 0 } ?: false
        }.toSet()

        if (inputConflictingSchedules.isNotEmpty()) {
            val conflictingScheduleNames = inputConflictingSchedules.map { it.name to it.country }
            throw InvalidArgumentException(
                errorCode = ValidationErrorCode.ConstraintViolationException,
                message = "Pay schedule name and country combination already exists for schedules " +
                    "$conflictingScheduleNames",
                context = mapOf("conflicts" to inputConflictingSchedules),
            )
        }

        val validInputSchedules = paySchedules - inputConflictingSchedules
        return validInputSchedules
    }

    private fun findConflictingSchedules(
        paySchedules: Collection<PaySchedule>,
        transactionContext: TransactionContext,
    ): Map<Pair<String, CountryCode?>, Int> {
        val allCountryDefaultSchedules = paySchedules.all { it.entityId == -1L }
        val allEntitySchedules = paySchedules.all { it.entityId != -1L }

        val existingConflictingSchedules = when {
            allCountryDefaultSchedules -> {
                findEntityLevelSchedulesCountByNameAndCountry(
                    nameToCountry = paySchedules.associate { Pair(it.name, it.country) },
                    transactionContext = transactionContext,
                )
            }
            allEntitySchedules -> {
                findSchedulesCountByEntityAndNameAndCountry(
                    entityId = SCOPE_COUNTRY_DUMMY_ENTITY_ID,
                    nameToCountry = paySchedules.associate { Pair(it.name, it.country) },
                    transactionContext = transactionContext,
                )
            }
            else -> {
                throw InvalidArgumentException(
                    errorCode = ValidationErrorCode.InvalidPayScheduleRequest,
                    message = "All pay schedules must either be default or entity schedules",
                )
            }
        }
        return existingConflictingSchedules
    }

    private fun Collection<PayScheduleRecord>.audit(transactionContext: TransactionContext) =
        transactionContext.trx.batchInsert(this.map { it.toAudit(transactionContext.audit) }).execute()

    private fun buildPayScheduleQuery(conditions: List<Condition>): SelectConditionStep<PayScheduleRecord> = dsl
        .selectFrom(PAY_SCHEDULE)
        .where(conditions)

    private fun executeQuery(
        query: SelectConditionStep<PayScheduleRecord>,
        orderFields: List<OrderField<*>>,
        pageRequest: PageRequest?,
    ): Result<PayScheduleRecord> {
        val modifiedQuery = query
            .let { if (orderFields.isNotEmpty()) it.orderBy(orderFields) else it }
            .let { q ->
                val pageNumber = pageRequest?.pageNumber
                val pageSize = pageRequest?.pageSize
                if (pageNumber != null && pageSize != null) {
                    val offset = (pageNumber) * pageSize
                    q.offset(offset).limit(pageSize)
                } else {
                    q
                }
            }

        return modifiedQuery.fetch()
    }
}
