package com.multiplier.compensation.service.compensationitem.generation

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.domain.payschedule.PayScheduleItem
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationSchemaItemBuilder
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.payschedule.PayScheduleService
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("CompensationItemGenerationManager")
class CompensationItemGenerationManagerTest {
    private lateinit var payScheduleService: PayScheduleService
    private lateinit var compensationSchemaService: CompensationSchemaService
    private lateinit var compensationItemGenerationManager: CompensationItemGenerationManager

    @BeforeEach
    fun setUp() {
        payScheduleService = mockk()
        compensationSchemaService = mockk()
        compensationItemGenerationManager = CompensationItemGenerationManager(
            payScheduleService = payScheduleService,
            compensationSchemaService = compensationSchemaService,
        )
        every { compensationSchemaService.getSchemaItemById(any()) } returns CompensationSchemaItem(
            id = UUID.randomUUID(),
            schemaId = UUID.randomUUID(),
            componentName = "Basic Pay",
            category = "Base",
            isTaxable = true,
            isProrated = false,
            isFixed = true,
            isActive = true,
            isMandatory = true,
            isPartOfBasePay = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1L,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1L,
            label = "Basic Pay",
            itemType = ItemType.INPUT,
            validation = null,
            calculation = null,
            billingRateType = BillingRateType.VALUE,
            isOvertimeEligible = false,
            description = "Test component description",
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = null,
            currency = "USD",
            isPartOfCtc = true,
        )
    }

    @ParameterizedTest(name = "{index} => {3}")
    @MethodSource("provideInvalidGenerateItemsTestCases")
    fun `test generate items with invalid requests`(
        compensation: Compensation,
        toDate: LocalDate,
        description: String,
    ) {
        // when
        val generatedItems = compensationItemGenerationManager.generateItems(
            compensation,
            toDate,
        )

        // then
        assertEquals(0, generatedItems.size, description)
    }

    @ParameterizedTest(name = "{index} => {4}")
    @MethodSource("provideValidGenerateItemsTestCases")
    fun `test generate items with valid requests`(
        compensation: Compensation,
        toDate: LocalDate,
        expectedItemCount: Int,
        payScheduleItems: List<PayScheduleItem>,
        description: String,
    ) {
        every { payScheduleService.getPayScheduleItems(any(), any(), any()) } returns payScheduleItems

        // when
        val generatedItems = compensationItemGenerationManager.generateItems(
            compensation,
            toDate,
        )

        // then
        assertEquals(expectedItemCount, generatedItems.size, description)
        for ((index, payScheduleItem) in payScheduleItems.withIndex()) {
            val generatedItem = generatedItems[index]
            assertEquals(
                payScheduleItem.startDate,
                generatedItem.startDate,
                "Mismatch in startDate for item at index $index",
            )
            assertEquals(payScheduleItem.endDate, generatedItem.endDate, "Mismatch in endDate for item at index $index")
            assertEquals(
                compensation.billingRate,
                generatedItem.billingRate,
                "Mismatch in billingRate for item at index $index",
            )

            if (compensation.isInstallment) {
                assertEquals(
                    index + 1,
                    generatedItem.currentInstallment,
                    "Mismatch in currentInstallment for item at index $index",
                )
            }
        }
    }

    @ParameterizedTest(name = "{index} => {4}")
    @MethodSource("provideGenerateTruncatedItemTestCases")
    fun `test generate truncated item`(
        updatedCompensation: Compensation,
        affectedItem: CompensationItem,
        schemaItem: CompensationSchemaItem,
        expectedStartDate: LocalDate,
        expectedEndDate: LocalDate?,
        description: String,
    ) {
        // when
        val truncatedItem = compensationItemGenerationManager.generateTruncatedItem(
            updatedCompensation,
            affectedItem,
            schemaItem,
        )

        // then
        assertEquals(expectedStartDate, truncatedItem.startDate, description)
        assertEquals(expectedEndDate, truncatedItem.endDate, description)
        assertNull(truncatedItem.calculatedAmount)
        assertNull(truncatedItem.cutOffDate)
        assertEquals(CompensationItemStatus.NEW, affectedItem.status)
    }

    @Test
    fun `test generate truncated item with mismatch between item and compensation`() {
        val compensation = TestCompensationBuilder()
            .build()
        val affectedItem = TestCompensationItemBuilder()
            .build()
        val schemaItem = TestCompensationSchemaItemBuilder().build()

        val exception = assertThrows<InvalidArgumentException> {
            compensationItemGenerationManager.generateTruncatedItem(compensation, affectedItem, schemaItem)
        }

        assertEquals("Affected item does not correspond to the compensation.", exception.message)
        assertEquals(ValidationErrorCode.InvalidItemAdjustmentRequest, exception.errorCode)
    }

    @Test
    fun `test generate truncated item when no adjustment is required`() {
        val compensation = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withEndDate(LocalDate.of(2025, 1, 31))
            .build()
        val updatedCompensation = compensation.copy(
            endDate = null,
        )
        val schemaItem = TestCompensationSchemaItemBuilder().build()
        val affectedItem = TestCompensationItemBuilder()
            .withCompensation(compensation)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withEndDate(LocalDate.of(2025, 1, 31))
            .build()

        val exception = assertThrows<InvalidArgumentException> {
            compensationItemGenerationManager.generateTruncatedItem(updatedCompensation, affectedItem, schemaItem)
        }

        assertEquals("No truncation required.", exception.message)
        assertEquals(ValidationErrorCode.InvalidItemAdjustmentRequest, exception.errorCode)
    }

    @Test
    fun `test generate end truncated dependent item when no adjustment is required`() {
        val parentCompensation = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withEndDate(null)
            .build()
        val updatedParentCompensation = parentCompensation.copy(
            endDate = LocalDate.of(2025, 1, 31),
        )
        val schemaItem = TestCompensationSchemaItemBuilder().build()
        val affectedItem = TestCompensationItemBuilder()
            .withCompensation(parentCompensation)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withEndDate(LocalDate.of(2025, 1, 31))
            .build()

        val exception = assertThrows<InvalidArgumentException> {
            compensationItemGenerationManager.generateTruncatedItem(updatedParentCompensation, affectedItem, schemaItem)
        }

        assertEquals("No truncation required.", exception.message)
        assertEquals(ValidationErrorCode.InvalidItemAdjustmentRequest, exception.errorCode)
    }

    @ParameterizedTest(name = "{index} => {4}")
    @MethodSource("provideAdjustedDatesTestCases")
    fun `test generate items with adjusted dates`(
        compensation: Compensation,
        toDate: LocalDate,
        expectedAdjustedFromDate: LocalDate,
        expectedAdjustedToDate: LocalDate,
        payScheduleItems: List<PayScheduleItem>,
        description: String,
    ) {
        every { payScheduleService.getPayScheduleItems(any(), any(), any()) } returns payScheduleItems

        // when
        val generatedItems = compensationItemGenerationManager.generateItems(compensation, toDate)

        // then
        assertEquals(payScheduleItems.size, generatedItems.size, "Mismatch in item count for $description")

        generatedItems.forEach { item ->
            assertTrue(
                item.startDate >= expectedAdjustedFromDate,
                "Generated item startDate ${item.startDate} is before adjustedFromDate$expectedAdjustedFromDate" +
                    "for $description",
            )
            assertTrue(
                item.endDate <= expectedAdjustedToDate,
                "Generated item endDate ${item.endDate} is after adjustedToDate $expectedAdjustedToDate" +
                    "for $description",
            )
        }
    }

    @ParameterizedTest(name = "{index} => {4}")
    @MethodSource("provideGenerateItemsTestCases")
    fun `test generate items with pay schedule filtering and adjustment`(
        compensation: Compensation,
        toDate: LocalDate,
        payScheduleItems: List<PayScheduleItem>,
        expectedItemProperties: List<Pair<LocalDate, LocalDate>>,
        description: String,
    ) {
        every { payScheduleService.getPayScheduleItems(any(), any(), any()) } returns payScheduleItems

        // when
        val generatedItems = compensationItemGenerationManager.generateItems(compensation, toDate)

        // then
        assertEquals(
            expectedItemProperties.size,
            generatedItems.size,
            "Mismatch in item count for $description",
        )

        for ((index, generatedItem) in generatedItems.withIndex()) {
            val (expectedStartDate, expectedEndDate) = expectedItemProperties[index]
            assertEquals(expectedStartDate, generatedItem.startDate, "Mismatch in startDate for item at index $index")
            assertEquals(expectedEndDate, generatedItem.endDate, "Mismatch in endDate for item at index $index")
        }
    }

    fun provideInvalidGenerateItemsTestCases(): List<Arguments> = listOf(
        createAbortedCompensationTestCase(),
        createDeletedCompensationTestCase(),
        createCompensationStartsAfterToDateTestCase(),
        createItemGenerationAlreadyCompleteTestCase(),
        createNextItemStartsAfterToDateTestCase(),
    )

    companion object {
        @JvmStatic
        fun provideValidGenerateItemsTestCases(): List<Arguments> = listOf(
            createSinglePayScheduleItemTestCase(),
            createMultiplePayScheduleItemsTestCase(),
            createPartialOverlapPayScheduleTestCase(),
            createInstallmentBasedCompensationTestCase(),
            createCompensationEndDateBeforeToDateTestCase(),
        )

        private fun createSinglePayScheduleItemTestCase(): Arguments {
            val payScheduleItem = createBasePayScheduleItem().copy(
                name = "Test Schedule",
                startDate = LocalDate.of(2025, 6, 1),
                endDate = LocalDate.of(2025, 6, 30),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .withProcessedUntilDate(LocalDate.of(2025, 5, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 7, 31),
                1,
                listOf(payScheduleItem),
                "Single PayScheduleItem within range",
            )
        }

        private fun createMultiplePayScheduleItemsTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    name = "Schedule 1",
                    startDate = LocalDate.of(2025, 6, 1),
                    endDate = LocalDate.of(2025, 6, 30),
                ),
                createBasePayScheduleItem().copy(
                    name = "Schedule 2",
                    startDate = LocalDate.of(2025, 7, 1),
                    endDate = LocalDate.of(2025, 7, 31),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .withProcessedUntilDate(LocalDate.of(2025, 5, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 7, 31),
                2,
                payScheduleItems,
                "Multiple PayScheduleItems within range",
            )
        }

        private fun createPartialOverlapPayScheduleTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    name = "Partial Overlap Schedule",
                    startDate = LocalDate.of(2025, 7, 1),
                    endDate = LocalDate.of(2025, 7, 31),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .withProcessedUntilDate(LocalDate.of(2025, 6, 15))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 7, 15),
                1,
                payScheduleItems,
                "PayScheduleItem partially overlaps range",
            )
        }

        private fun createInstallmentBasedCompensationTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 6, 1),
                    endDate = LocalDate.of(2025, 6, 30),
                ),
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 7, 1),
                    endDate = LocalDate.of(2025, 7, 31),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .withIsInstallment(true)
                .withNoOfInstallments(10)
                .withProcessedUntilDate(LocalDate.of(2025, 5, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 7, 31),
                2,
                payScheduleItems,
                "Installment-based compensation with one schedule",
            )
        }

        private fun createCompensationEndDateBeforeToDateTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 6, 1),
                    endDate = LocalDate.of(2025, 6, 30),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 6, 30))
                .withProcessedUntilDate(LocalDate.of(2025, 5, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 7, 31),
                1,
                payScheduleItems,
                "Compensation end date before toDate",
            )
        }

        private fun createBasePayScheduleItem(): PayScheduleItem = PayScheduleItem(
            id = UUID.randomUUID(),
            payScheduleId = UUID.randomUUID(),
            entityId = 1L,
            companyId = 1L,
            name = "Default Schedule",
            frequency = PayScheduleFrequency.MONTHLY,
            startDate = LocalDate.of(2025, 6, 1),
            endDate = LocalDate.of(2025, 6, 30),
            payDate = LocalDate.of(2025, 6, 15),
            isActive = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1L,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1L,
        )

        @JvmStatic
        fun provideGenerateTruncatedItemTestCases(): List<Arguments> = listOf(
            createTruncationAtCompStartTestCase(),
            createTruncationAtCompEndTestCase(),
            createTruncationAtBothEndsTestCase(),
        )

        private fun createTruncationAtCompStartTestCase(): Arguments {
            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 6, 1))
                .withEndDate(LocalDate.of(2025, 6, 30))
                .build()
            val updatedCompensation = compensation.copy(
                startDate = LocalDate.of(2025, 6, 10),
            )
            val affectedItem = TestCompensationItemBuilder()
                .withCompensation(compensation)
                .withStartDate(LocalDate.of(2025, 6, 1))
                .withEndDate(LocalDate.of(2025, 6, 30))
                .build()

            return Arguments.of(
                updatedCompensation,
                affectedItem,
                TestCompensationSchemaItemBuilder().build(),
                LocalDate.of(2025, 6, 10),
                LocalDate.of(2025, 6, 30),
                "Truncates at compensation start date",
            )
        }

        private fun createTruncationAtCompEndTestCase(): Arguments {
            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 1, 31))
                .build()
            val updatedCompensation = compensation.copy(
                endDate = LocalDate.of(2025, 1, 20),
            )
            val affectedItem = TestCompensationItemBuilder()
                .withCompensation(compensation)
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 1, 31))
                .build()

            return Arguments.of(
                updatedCompensation,
                affectedItem,
                TestCompensationSchemaItemBuilder().build(),
                LocalDate.of(2025, 1, 1),
                LocalDate.of(2025, 1, 20),
                "Truncates at compensation end date",
            )
        }

        private fun createTruncationAtBothEndsTestCase(): Arguments {
            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 1, 31))
                .build()
            val updatedCompensation = compensation.copy(
                startDate = LocalDate.of(2025, 1, 10),
                endDate = LocalDate.of(2025, 1, 20),
            )
            val affectedItem = TestCompensationItemBuilder()
                .withCompensation(compensation)
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 1, 31))
                .build()

            return Arguments.of(
                updatedCompensation,
                affectedItem,
                TestCompensationSchemaItemBuilder().build(),
                LocalDate.of(2025, 1, 10),
                LocalDate.of(2025, 1, 20),
                "Truncates at both ends",
            )
        }

        @JvmStatic
        fun provideAdjustedDatesTestCases(): List<Arguments> = listOf(
            createCompensationWithProcessedUntilDateTestCase(),
            createCompensationWithoutProcessedUntilDateTestCase(),
            createCompensationEndingAfterToDateTestCase(),
            createCompensationEndingBeforeToDateTestCase(),
        )

        private fun createCompensationWithProcessedUntilDateTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 6, 1),
                    endDate = LocalDate.of(2025, 6, 30),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 10))
                .withProcessedUntilDate(LocalDate.of(2025, 6, 10))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                LocalDate.of(2025, 6, 11),
                LocalDate.of(2025, 6, 30),
                payScheduleItems,
                "Compensation with a processedUntilDate",
            )
        }

        private fun createCompensationWithoutProcessedUntilDateTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 6, 1),
                    endDate = LocalDate.of(2025, 6, 30),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 3, 10))
                .withProcessedUntilDate(null)
                .withEndDate(LocalDate.of(2025, 12, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                LocalDate.of(2025, 3, 10),
                LocalDate.of(2025, 6, 30),
                payScheduleItems,
                "Compensation without a processedUntilDate",
            )
        }

        private fun createCompensationEndingAfterToDateTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 6, 1),
                    endDate = LocalDate.of(2025, 6, 30),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 7, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                LocalDate.of(2025, 1, 1),
                LocalDate.of(2025, 6, 30),
                payScheduleItems,
                "Compensation ending after toDate",
            )
        }

        private fun createCompensationEndingBeforeToDateTestCase(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 5, 1),
                    endDate = LocalDate.of(2025, 5, 31),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 5, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                LocalDate.of(2025, 1, 1),
                LocalDate.of(2025, 5, 31),
                payScheduleItems,
                "Compensation ending before toDate",
            )
        }

        @JvmStatic
        fun provideGenerateItemsTestCases(): List<Arguments> = listOf(
            createCaseFilteringOutsideRange(),
            createCaseFilteringPartialOverlap(),
            createCaseAdjustingStartDate(),
            createCaseAdjustingStartDateWithProcessedUntilDate(),
            createCaseAdjustingEndDate(),
            createCaseNoAdjustmentNeeded(),
        )

        private fun createCaseFilteringOutsideRange(): Arguments {
            val payScheduleItems = listOf(
                createBasePayScheduleItem().copy(
                    startDate = LocalDate.of(2025, 1, 1),
                    endDate = LocalDate.of(2025, 1, 31),
                ),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 6, 1))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                payScheduleItems,
                emptyList<Pair<LocalDate, LocalDate>>(),
                "Pay schedule items outside the date range",
            )
        }

        private fun createCaseFilteringPartialOverlap(): Arguments {
            val payScheduleItem = createBasePayScheduleItem().copy(
                startDate = LocalDate.of(2025, 6, 1),
                endDate = LocalDate.of(2025, 6, 30),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .withProcessedUntilDate(LocalDate.of(2025, 6, 15))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 7, 15),
                listOf(payScheduleItem),
                listOf(Pair(LocalDate.of(2025, 6, 16), LocalDate.of(2025, 6, 30))),
                "Pay schedule item partially overlapping the date range",
            )
        }

        private fun createCaseAdjustingStartDate(): Arguments {
            val payScheduleItem = createBasePayScheduleItem().copy(
                startDate = LocalDate.of(2025, 6, 1),
                endDate = LocalDate.of(2025, 6, 30),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 6, 15))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                listOf(payScheduleItem),
                listOf(Pair(LocalDate.of(2025, 6, 15), LocalDate.of(2025, 6, 30))),
                "Pay schedule item adjusted to startDate",
            )
        }

        private fun createCaseAdjustingEndDate(): Arguments {
            val payScheduleItem = createBasePayScheduleItem().copy(
                startDate = LocalDate.of(2025, 6, 1),
                endDate = LocalDate.of(2025, 6, 30),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 6, 15))
                .withProcessedUntilDate(LocalDate.of(2025, 5, 31))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                listOf(payScheduleItem),
                listOf(Pair(LocalDate.of(2025, 6, 1), LocalDate.of(2025, 6, 15))),
                "Pay schedule item adjusted to compensation endDate",
            )
        }

        private fun createCaseAdjustingStartDateWithProcessedUntilDate(): Arguments {
            val payScheduleItem = createBasePayScheduleItem().copy(
                startDate = LocalDate.of(2025, 6, 1),
                endDate = LocalDate.of(2025, 6, 30),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 1, 1))
                .withEndDate(LocalDate.of(2025, 12, 31))
                .withProcessedUntilDate(LocalDate.of(2025, 6, 10))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                listOf(payScheduleItem),
                listOf(Pair(LocalDate.of(2025, 6, 11), LocalDate.of(2025, 6, 30))),
                "Pay schedule item adjusted to startDate",
            )
        }

        private fun createCaseNoAdjustmentNeeded(): Arguments {
            val payScheduleItem = createBasePayScheduleItem().copy(
                startDate = LocalDate.of(2025, 6, 1),
                endDate = LocalDate.of(2025, 6, 30),
            )

            val compensation = TestCompensationBuilder()
                .withStartDate(LocalDate.of(2025, 6, 1))
                .withEndDate(LocalDate.of(2025, 6, 30))
                .build()

            return Arguments.of(
                compensation,
                LocalDate.of(2025, 6, 30),
                listOf(payScheduleItem),
                listOf(Pair(LocalDate.of(2025, 6, 1), LocalDate.of(2025, 6, 30))),
                "Pay schedule item already within range",
            )
        }
    }

    private fun createAbortedCompensationTestCase(): Arguments = Arguments.of(
        TestCompensationBuilder()
            .withStatus(CompensationStatus.ABORTED)
            .build(),
        LocalDate.now().plusMonths(1),
        "should return empty list for aborted compensation",
    )

    private fun createDeletedCompensationTestCase(): Arguments = Arguments.of(
        TestCompensationBuilder()
            .withStatus(CompensationStatus.DELETED)
            .build(),
        LocalDate.now().plusMonths(1),
        "should return empty list for deleted compensation",
    )

    private fun createCompensationStartsAfterToDateTestCase(): Arguments = Arguments.of(
        TestCompensationBuilder()
            .withStartDate(LocalDate.of(2025, 2, 28))
            .build(),
        LocalDate.of(2025, 1, 31),
        "should return empty list when compensation starts after toDate",
    )

    private fun createItemGenerationAlreadyCompleteTestCase(): Arguments {
        val compensation = TestCompensationBuilder()
            .withProcessedUntilDate(LocalDate.of(2025, 12, 31))
            .build()

        val toDate = LocalDate.of(2025, 12, 31)

        return Arguments.of(
            compensation,
            toDate,
            "Item generation is already complete, should return empty list",
        )
    }

    private fun createNextItemStartsAfterToDateTestCase(): Arguments {
        val compensation = TestCompensationBuilder()
            .withProcessedUntilDate(LocalDate.of(2025, 12, 30))
            .build()

        val toDate = LocalDate.of(2025, 12, 15)

        return Arguments.of(
            compensation,
            toDate,
            "Next item starts after toDate, should return empty list",
        )
    }
}
