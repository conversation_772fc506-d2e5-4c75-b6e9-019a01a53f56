package com.multiplier.compensation.domain.compensationitem

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationItemEnrichedTest {
    private val id = UUID.randomUUID()
    private val companyId = 1L
    private val entityId = 2L
    private val contractId = 3L
    private val category = "Salary"
    private val schemaComponentName = "Base Pay"
    private val currency = "USD"
    private val billingRateType = BillingRateType.VALUE
    private val billingRate = 5000.0
    private val billingFrequency = BillingFrequency.MONTHLY
    private val payScheduleName = "Biweekly"
    private val payScheduleFrequency = PayScheduleFrequency.BI_WEEKLY
    private val startDate = LocalDate.of(2024, 1, 1)
    private val endDate = LocalDate.of(2024, 12, 31)
    private val isInstallment = false
    private val noOfInstallments: Int? = null
    private val currentInstallment: Int? = null
    private val isTaxable = true
    private val isFixed = false
    private val isProrated = true
    private val isMandatory = false
    private val isPartOfBasePay = true
    private val calculatedAmount = 2500.0
    private val cutOffDate = LocalDate.of(2024, 11, 30)
    private val expectedPayDate = LocalDate.of(2024, 12, 15)
    private val payDate: LocalDate? = null
    private val previousId: UUID? = null
    private val status = CompensationItemStatus.NEW
    private val isArrear = false
    private val arrearOf: UUID? = null
    private val arrearTriggerReference: String? = null
    private val updateTriggerReference: String? = null
    private val label = "Salary"
    private val isOvertimeEligible = false
    private val isPartOfCtc = true
    private val notes = "This is a test note."
    private val createdOn = LocalDateTime.now()
    private val createdBy = 1L
    private val updatedOn = LocalDateTime.now()
    private val updatedBy = 2L

    @Test
    fun `should correctly create CompensationItemEnriched instance`() {
        val item = createCompensationItemEnriched()

        assertEquals(id, item.id)
        assertEquals(companyId, item.companyId)
        assertEquals(entityId, item.entityId)
        assertEquals(contractId, item.contractId)
        assertEquals(category, item.category)
        assertEquals(schemaComponentName, item.schemaComponentName)
        assertEquals(currency, item.currency)
        assertEquals(billingRateType, item.billingRateType)
        assertEquals(billingRate, item.billingRate)
        assertEquals(billingFrequency, item.billingFrequency)
        assertEquals(payScheduleName, item.payScheduleName)
        assertEquals(payScheduleFrequency, item.payScheduleFrequency)
        assertEquals(startDate, item.startDate)
        assertEquals(endDate, item.endDate)
        assertEquals(isInstallment, item.isInstallment)
        assertEquals(noOfInstallments, item.noOfInstallments)
        assertEquals(currentInstallment, item.currentInstallment)
        assertEquals(isTaxable, item.isTaxable)
        assertEquals(isFixed, item.isFixed)
        assertEquals(isProrated, item.isProrated)
        assertEquals(isMandatory, item.isMandatory)
        assertEquals(isPartOfBasePay, item.isPartOfBasePay)
        assertEquals(calculatedAmount, item.calculatedAmount)
        assertEquals(cutOffDate, item.cutOffDate)
        assertEquals(expectedPayDate, item.expectedPayDate)
        assertEquals(payDate, item.payDate)
        assertEquals(previousId, item.previousId)
        assertEquals(status, item.status)
        assertEquals(isArrear, item.isArrear)
        assertEquals(arrearOf, item.arrearOf)
        assertEquals(arrearTriggerReference, item.arrearTriggerReference)
        assertEquals(updateTriggerReference, item.updateTriggerReference)
        assertEquals(label, item.label)
        assertEquals(createdOn, item.createdOn)
        assertEquals(createdBy, item.createdBy)
        assertEquals(updatedOn, item.updatedOn)
        assertEquals(updatedBy, item.updatedBy)
        assertEquals(notes, item.notes)
    }

    private fun createCompensationItemEnriched() = CompensationItemEnriched(
        id = id,
        companyId = companyId,
        entityId = entityId,
        contractId = contractId,
        category = category,
        schemaComponentName = schemaComponentName,
        currency = currency,
        billingRateType = billingRateType,
        billingRate = billingRate,
        billingFrequency = billingFrequency,
        payScheduleName = payScheduleName,
        payScheduleFrequency = payScheduleFrequency,
        startDate = startDate,
        endDate = endDate,
        isInstallment = isInstallment,
        noOfInstallments = noOfInstallments,
        currentInstallment = currentInstallment,
        isTaxable = isTaxable,
        isFixed = isFixed,
        isProrated = isProrated,
        isMandatory = isMandatory,
        isPartOfBasePay = isPartOfBasePay,
        calculatedAmount = calculatedAmount,
        cutOffDate = cutOffDate,
        expectedPayDate = expectedPayDate,
        payDate = payDate,
        previousId = previousId,
        status = status,
        isArrear = isArrear,
        arrearOf = arrearOf,
        arrearTriggerReference = arrearTriggerReference,
        updateTriggerReference = updateTriggerReference,
        label = label,
        isOvertimeEligible = isOvertimeEligible,
        isPartOfCtc = isPartOfCtc,
        notes = notes,
        createdOn = createdOn,
        createdBy = createdBy,
        updatedOn = updatedOn,
        updatedBy = updatedBy,
    )
}
