package com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.semimonthly

import com.multiplier.common.exception.toBusinessException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.payschedule.schedulegeneration.dto.PayScheduleInterval
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.PayScheduleStrategy
import java.time.LocalDate

class NonInstallmentSemiMonthlyPayScheduleStrategy : PayScheduleStrategy {
    override fun generate(
        paySchedule: PaySchedule,
        from: LocalDate,
        to: LocalDate,
    ): Collection<PayScheduleInterval> {
        val payScheduleIntervals = mutableListOf<PayScheduleInterval>()
        if (from < paySchedule.startDateReference) {
            payScheduleIntervals.addAll(
                generatePastIntervals(
                    paySchedule = paySchedule,
                    from = from,
                    to = to,
                ),
            )
        }
        return payScheduleIntervals.plus(
            generateFutureIntervals(
                paySchedule = paySchedule,
                from = from,
                to = to,
            ),
        )
    }

    private fun generatePastIntervals(
        paySchedule: PaySchedule,
        from: LocalDate,
        to: LocalDate,
    ): List<PayScheduleInterval> {
        val payScheduleIntervals = mutableListOf<PayScheduleInterval>()
        var monthsToSubtract = 1L
        var startDateFirstPeriod = paySchedule.startDateReference.minusMonths(1)
        var endDateFirstPeriod = paySchedule.endDateReference.minusMonths(monthsToSubtract)
        var startDateSecondPeriod = endDateFirstPeriod.plusDays(1)
        var endDateSecondPeriod = paySchedule.startDateReference.minusDays(1)
        while (endDateSecondPeriod >= from) {
            monthsToSubtract += 1
            if (startDateFirstPeriod <= to) {
                payScheduleIntervals.add(
                    PayScheduleInterval(
                        startDate = startDateFirstPeriod,
                        endDate = endDateFirstPeriod,
                        payDate = estimatePayDate(
                            startDate = startDateFirstPeriod,
                            endDate = endDateFirstPeriod,
                            payDateReference = paySchedule.payDateReferenceType,
                            relativePayDays = paySchedule.relativePayDays,
                        ),
                    ),
                )
                payScheduleIntervals.add(
                    PayScheduleInterval(
                        startDate = startDateSecondPeriod,
                        endDate = endDateSecondPeriod,
                        payDate = estimatePayDate(
                            startDate = startDateSecondPeriod,
                            endDate = endDateSecondPeriod,
                            payDateReference = paySchedule.payDateReferenceType,
                            relativePayDays = paySchedule.relativePayDays,
                        ),
                    ),
                )
            }
            endDateSecondPeriod = startDateFirstPeriod.minusDays(1)
            startDateFirstPeriod = paySchedule.startDateReference.minusMonths(monthsToSubtract)
            endDateFirstPeriod = paySchedule.endDateReference.minusMonths(monthsToSubtract)
            startDateSecondPeriod = endDateFirstPeriod.plusDays(1)
        }

        return payScheduleIntervals.sortedBy { it.startDate }
    }

    private fun generateFutureIntervals(
        paySchedule: PaySchedule,
        from: LocalDate,
        to: LocalDate,
    ): List<PayScheduleInterval> {
        val payScheduleIntervals = mutableListOf<PayScheduleInterval>()
        var monthsToAdd = 0L
        var startDateFirstPeriod = paySchedule.startDateReference
        var endDateFirstPeriod = paySchedule.endDateReference.plusMonths(monthsToAdd)
        var startDateSecondPeriod = endDateFirstPeriod.plusDays(1)
        var endDateSecondPeriod = paySchedule.startDateReference.plusMonths(monthsToAdd + 1).minusDays(1)
        while (startDateFirstPeriod <= to) {
            monthsToAdd += 1
            if (endDateSecondPeriod >= from) {
                payScheduleIntervals.add(
                    PayScheduleInterval(
                        startDate = startDateFirstPeriod,
                        endDate = endDateFirstPeriod,
                        payDate = estimatePayDate(
                            startDate = startDateFirstPeriod,
                            endDate = endDateFirstPeriod,
                            payDateReference = paySchedule.payDateReferenceType,
                            relativePayDays = paySchedule.relativePayDays,
                        ),
                    ),
                )
                payScheduleIntervals.add(
                    PayScheduleInterval(
                        startDate = startDateSecondPeriod,
                        endDate = endDateSecondPeriod,
                        payDate = estimatePayDate(
                            startDate = startDateSecondPeriod,
                            endDate = endDateSecondPeriod,
                            payDateReference = paySchedule.payDateReferenceType,
                            relativePayDays = paySchedule.relativePayDays,
                        ),
                    ),
                )
            }
            startDateFirstPeriod = endDateSecondPeriod.plusDays(1)
            endDateFirstPeriod = paySchedule.endDateReference.plusMonths(monthsToAdd)
            startDateSecondPeriod = endDateFirstPeriod.plusDays(1)
            endDateSecondPeriod = paySchedule.startDateReference.plusMonths(monthsToAdd + 1).minusDays(1)
        }
        return payScheduleIntervals
    }

    override fun estimateEndDate(
        startDate: LocalDate,
        installments: Int,
    ): LocalDate = throw ValidationErrorCode.UnsupportedUseCase.toBusinessException(
        message = "End date estimation not supported for non-installment pay schedules",
    )
}
