package com.multiplier.compensation.service.compensationschema

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.exception.config.CompensationServiceErrorCode
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.compensationschema.SchemaTags
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import io.github.oshai.kotlinlogging.KotlinLogging

private val log = KotlinLogging.logger {}

object CompensationSchemaDefaultingService {
    fun getApplicableSchemas(
        entitySchemas: List<CompensationSchema>,
        countryDefaultSchemas: List<CompensationSchema>,
        offeringType: OfferingType,
        countryCode: CountryCode?,
    ): CompensationSchemaResolutionResult {
        val schemas = when (offeringType) {
            OfferingType.GLOBAL_PAYROLL ->
                filterGpSchemas(entitySchemas)

            OfferingType.EOR -> {
                if (countryCode == null) {
                    log.error { "Country code is not present for EOR employment type" }
                    return CompensationSchemaResolutionResult.Failure(
                        "Country code is not present for EOR employment",
                        ValidationErrorCode.InvalidSchemaRequest,
                    )
                }

                filterEorSchemas(entitySchemas + countryDefaultSchemas, countryCode)
            }
        }
        return CompensationSchemaResolutionResult.Success(schemas)
    }

    private fun filterGpSchemas(schemas: List<CompensationSchema>): List<CompensationSchema> = schemas.filter {
        SchemaTags.GLOBAL_PAYROLL.name in it.tags
    }

    private fun filterEorSchemas(
        schemas: List<CompensationSchema>,
        countryCode: CountryCode,
    ): List<CompensationSchema> = schemas.filter {
        it.country == countryCode &&
            SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.name in it.tags
    }

    fun getDefaultSchema(
        schemaName: String?,
        offeringType: OfferingType?,
        countryCode: CountryCode?,
        entitySchemas: List<CompensationSchema>,
        countryDefaultSchemas: List<CompensationSchema>,
    ): CompensationSchemaResolutionResult {
        val effectiveOfferingType = offeringType ?: OfferingType.GLOBAL_PAYROLL

        if (!schemaName.isNullOrBlank()) {
            return resolveSchemaIfNameProvided(
                schemaName = schemaName,
                offeringType = effectiveOfferingType,
                countryCode = countryCode,
                entitySchemas = entitySchemas,
                countryDefaultSchemas = countryDefaultSchemas,
            )
        }

        return when (effectiveOfferingType) {
            OfferingType.GLOBAL_PAYROLL -> resolveGlobalPayrollSchema(entitySchemas = entitySchemas)
            OfferingType.EOR ->
                resolveEORSchema(
                    entitySchemas = entitySchemas,
                    countryDefaultSchemas = countryDefaultSchemas,
                    countryCode = countryCode,
                )
        }
    }

    private fun resolveSchemaIfNameProvided(
        schemaName: String,
        offeringType: OfferingType,
        countryCode: CountryCode?,
        entitySchemas: List<CompensationSchema>,
        countryDefaultSchemas: List<CompensationSchema>,
    ): CompensationSchemaResolutionResult {
        val schema = if (offeringType == OfferingType.EOR) {
            entitySchemas.find { it.name == schemaName && it.country == countryCode }
                ?: countryDefaultSchemas.find { it.name == schemaName && it.country == countryCode }
        } else {
            entitySchemas.find { it.name == schemaName }
        }

        return if (schema != null) {
            CompensationSchemaResolutionResult.Success(listOf(schema))
        } else {
            log.error { "No schema found with name [$schemaName]." }
            CompensationSchemaResolutionResult.Failure(
                "No schema found with name [$schemaName]",
                CompensationServiceErrorCode.CompensationSchemaNotFound,
            )
        }
    }

    private fun resolveGlobalPayrollSchema(
        entitySchemas: List<CompensationSchema>,
    ): CompensationSchemaResolutionResult {
        val entityGlobalPayrollSchemas = filterGpSchemas(entitySchemas)
        val entityDefaultGlobalPayrollSchemas = entityGlobalPayrollSchemas.filter { it.isDefault }

        return when {
            entityDefaultGlobalPayrollSchemas.size > 1 -> {
                log.error { "Multiple default schemas found for global payroll" }
                CompensationSchemaResolutionResult.Failure(
                    "Multiple default schemas found for global payroll",
                    CompensationServiceErrorCode.MultipleCompensationSchemaFound,
                )
            }

            entityDefaultGlobalPayrollSchemas.size == 1 -> CompensationSchemaResolutionResult.Success(
                entityDefaultGlobalPayrollSchemas,
            )
            entityGlobalPayrollSchemas.size == 1 -> CompensationSchemaResolutionResult.Success(
                entityGlobalPayrollSchemas,
            )
            else -> {
                log.warn { "Conflicting schemas found for global payroll" }
                CompensationSchemaResolutionResult.Failure(
                    "Conflicting schemas found for global payroll",
                    CompensationServiceErrorCode.ConflictingSchemaFound,
                )
            }
        }
    }

    private fun resolveEORSchema(
        entitySchemas: List<CompensationSchema>,
        countryDefaultSchemas: List<CompensationSchema>,
        countryCode: CountryCode?,
    ): CompensationSchemaResolutionResult {
        if (countryCode == null) {
            log.error { "Country code is not present for EOR employment type" }
            return CompensationSchemaResolutionResult.Failure(
                "Country code is not present for EOR employment type",
                ValidationErrorCode.InvalidSchemaRequest,
            )
        }

        var countryEORSchemas = filterEorSchemas(entitySchemas, countryCode)

        if (countryEORSchemas.isEmpty()) {
            countryEORSchemas = filterEorSchemas(countryDefaultSchemas, countryCode)
        }

        val entityDefaultEORSchemas = countryEORSchemas.filter { it.isDefault }
        return when {
            entityDefaultEORSchemas.size > 1 -> {
                log.error { "Multiple default schemas found for EOR with country $countryCode" }
                CompensationSchemaResolutionResult.Failure(
                    "Multiple default schemas found for EOR with country $countryCode",
                    CompensationServiceErrorCode.MultipleCompensationSchemaFound,
                )
            }

            entityDefaultEORSchemas.size == 1 -> CompensationSchemaResolutionResult.Success(
                entityDefaultEORSchemas,
            )
            countryEORSchemas.size == 1 -> CompensationSchemaResolutionResult.Success(countryEORSchemas)
            else -> {
                log.warn { "Conflicting schemas found for EOR with country $countryCode" }
                CompensationSchemaResolutionResult.Failure(
                    "Conflicting schemas found for EOR with country $countryCode",
                    CompensationServiceErrorCode.ConflictingSchemaFound,
                )
            }
        }
    }
}
