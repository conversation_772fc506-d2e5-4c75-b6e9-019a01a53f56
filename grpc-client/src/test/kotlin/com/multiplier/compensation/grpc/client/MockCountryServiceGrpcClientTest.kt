package com.multiplier.compensation.grpc.client

import com.multiplier.compensation.domain.common.CountryCode
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class MockCountryServiceGrpcClientTest {
    private val mockCountryServiceGrpcClient = MockCountryServiceGrpcClient()

    @Test
    fun `getCountryNamesByCodes should return mock country names`() = runBlocking {
        // Arrange
        val countryCodes = setOf(CountryCode.USA, CountryCode.GBR, CountryCode.IND)

        // Act
        val result = mockCountryServiceGrpcClient.getCountryNamesByCodes(countryCodes)

        // Assert
        assertEquals(3, result.size)
        assertEquals("United States", result[CountryCode.USA])
        assertEquals("United Kingdom", result[CountryCode.GBR])
        assertEquals("India", result[CountryCode.IND])
    }

    @Test
    fun `getCountryNamesByCodes should handle empty set`() = runBlocking {
        // Arrange
        val countryCodes = emptySet<CountryCode>()

        // Act
        val result = mockCountryServiceGrpcClient.getCountryNamesByCodes(countryCodes)

        // Assert
        assertEquals(0, result.size)
    }

    @Test
    fun `getCountryNamesByCodes should handle unknown country codes`() = runBlocking {
        // Arrange
        val unknownCountryCode = CountryCode.ABW // Aruba, not explicitly handled in mock
        val countryCodes = setOf(unknownCountryCode)

        // Act
        val result = mockCountryServiceGrpcClient.getCountryNamesByCodes(countryCodes)

        // Assert
        assertEquals(1, result.size)
        assertEquals("Country ABW", result[unknownCountryCode])
    }

    @Test
    fun `getCountryNamesByCodes should handle mixed known and unknown country codes`() = runBlocking {
        // Arrange
        val countryCodes = setOf(
            CountryCode.USA,
            CountryCode.ABW,
            CountryCode.IND,
            CountryCode.GBR,
            CountryCode.CAN,
            CountryCode.AUS,
            CountryCode.SGP,
            CountryCode.DEU,
            CountryCode.FRA,
            CountryCode.JPN,
            CountryCode.CHN,
        )

        // Act
        val result = mockCountryServiceGrpcClient.getCountryNamesByCodes(countryCodes)

        // Assert
        assertEquals(11, result.size)
        assertEquals("United States", result[CountryCode.USA])
        assertEquals("Country ABW", result[CountryCode.ABW])
        assertEquals("India", result[CountryCode.IND])
        assertEquals("United Kingdom", result[CountryCode.GBR])
        assertEquals("Canada", result[CountryCode.CAN])
        assertEquals("Australia", result[CountryCode.AUS])
        assertEquals("Singapore", result[CountryCode.SGP])
        assertEquals("Germany", result[CountryCode.DEU])
        assertEquals("France", result[CountryCode.FRA])
        assertEquals("Japan", result[CountryCode.JPN])
        assertEquals("China", result[CountryCode.CHN])
    }
}
