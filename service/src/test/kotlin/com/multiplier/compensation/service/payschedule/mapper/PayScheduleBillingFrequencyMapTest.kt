package com.multiplier.compensation.service.payschedule.mapper

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class PayScheduleBillingFrequencyMapTest {
    @Test
    fun `test validPayScheduleFrequencyToBillingFrequencies mapping`() {
        assertEquals(
            setOf(BillingFrequency.ANNUALLY),
            allPossiblePayScheduleFrequencyToBillingFrequencies[PayScheduleFrequency.ANNUALLY],
        )
        assertEquals(
            setOf(BillingFrequency.ANNUALLY, BillingFrequency.SEMIANNUALLY),
            allPossiblePayScheduleFrequencyToBillingFrequencies[PayScheduleFrequency.SEMI_ANNUALLY],
        )
        assertEquals(
            setOf(BillingFrequency.ANNUALLY, BillingFrequency.SEMIANNUALLY, BillingFrequency.QUARTERLY),
            allPossiblePayScheduleFrequencyToBillingFrequencies[PayScheduleFrequency.QUARTERLY],
        )
        assertEquals(
            setOf(
                BillingFrequency.ANNUALLY,
                BillingFrequency.SEMIANNUALLY,
                BillingFrequency.QUARTERLY,
                BillingFrequency.MONTHLY,
                BillingFrequency.SEMIMONTHLY,
                BillingFrequency.DAILY,
                BillingFrequency.HOURLY,
            ),
            allPossiblePayScheduleFrequencyToBillingFrequencies[PayScheduleFrequency.MONTHLY],
        )
        assertEquals(
            setOf(
                BillingFrequency.ANNUALLY,
                BillingFrequency.SEMIANNUALLY,
                BillingFrequency.QUARTERLY,
                BillingFrequency.MONTHLY,
                BillingFrequency.SEMIMONTHLY,
                BillingFrequency.HOURLY,
            ),
            allPossiblePayScheduleFrequencyToBillingFrequencies[PayScheduleFrequency.SEMI_MONTHLY],
        )
    }

    @Test
    fun `verify MONTHLY pay schedule supports all Global Payroll primary salary billing frequencies`() {
        val monthlyBillingFreqs =
            allPossiblePayScheduleFrequencyToBillingFrequencies[PayScheduleFrequency.MONTHLY].orEmpty()

        // Global Payroll primary salary eligible frequencies: MONTHLY, ANNUALLY, HOURLY, DAILY
        val globalPayrollPrimaryFrequencies = listOf(
            BillingFrequency.MONTHLY,
            BillingFrequency.ANNUALLY,
            BillingFrequency.HOURLY,
            BillingFrequency.DAILY,
        )

        // MONTHLY should support all Global Payroll primary salary billing frequencies
        globalPayrollPrimaryFrequencies.forEach { restrictedFreq ->
            assertTrue(
                monthlyBillingFreqs.contains(restrictedFreq),
                "MONTHLY pay schedule should support $restrictedFreq billing frequency",
            )
        }
    }
}
