package com.multiplier.compensation.service.compensation.validation

import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DependencyConfigurationTest {
    @Test
    fun `should identify parent categories correctly`() {
        assertTrue(DependencyConfiguration.isParentCategory(CategoryConstants.CATEGORY_CONTRACT_BASE_PAY))
        assertTrue(DependencyConfiguration.isParentCategory(CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY))
        assertFalse(DependencyConfiguration.isParentCategory("SOME_OTHER_CATEGORY"))
    }

    @Test
    fun `should identify dependent billing rate types correctly`() {
        assertTrue(DependencyConfiguration.isDependentBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE))
        assertTrue(DependencyConfiguration.isDependentBillingRateType(BillingRateType.BASE_PAY_DAYS))
        assertTrue(DependencyConfiguration.isDependentBillingRateType(BillingRateType.CTC_PERCENTAGE))
        assertTrue(DependencyConfiguration.isDependentBillingRateType(BillingRateType.CTC_DAYS))
        assertFalse(DependencyConfiguration.isDependentBillingRateType(BillingRateType.VALUE))
    }

    @Test
    fun `should return correct parent category for dependent billing rate types`() {
        assertEquals(
            CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            DependencyConfiguration.getParentCategory(BillingRateType.BASE_PAY_PERCENTAGE),
        )
        assertEquals(
            CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            DependencyConfiguration.getParentCategory(BillingRateType.BASE_PAY_DAYS),
        )
        assertEquals(
            CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            DependencyConfiguration.getParentCategory(BillingRateType.CTC_PERCENTAGE),
        )
        assertEquals(
            CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            DependencyConfiguration.getParentCategory(BillingRateType.CTC_DAYS),
        )
        assertEquals(null, DependencyConfiguration.getParentCategory(BillingRateType.VALUE))
    }

    @Test
    fun `should return correct dependent billing rate types for parent categories`() {
        val basePayDependents = DependencyConfiguration.getDependentBillingRateTypes(
            CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        )
        assertTrue(basePayDependents.contains(BillingRateType.BASE_PAY_PERCENTAGE))
        assertTrue(basePayDependents.contains(BillingRateType.BASE_PAY_DAYS))
        assertEquals(2, basePayDependents.size)

        val ctcDependents = DependencyConfiguration.getDependentBillingRateTypes(
            CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
        )
        assertTrue(ctcDependents.contains(BillingRateType.CTC_PERCENTAGE))
        assertTrue(ctcDependents.contains(BillingRateType.CTC_DAYS))
        assertEquals(2, ctcDependents.size)

        val unknownDependents = DependencyConfiguration.getDependentBillingRateTypes("UNKNOWN_CATEGORY")
        assertTrue(unknownDependents.isEmpty())
    }

    @Test
    fun `should check dependency relationships correctly`() {
        assertTrue(
            DependencyConfiguration.isDependentOn(
                BillingRateType.BASE_PAY_PERCENTAGE,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            ),
        )
        assertTrue(
            DependencyConfiguration.isDependentOn(
                BillingRateType.BASE_PAY_DAYS,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            ),
        )
        assertTrue(
            DependencyConfiguration.isDependentOn(
                BillingRateType.CTC_PERCENTAGE,
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            ),
        )
        assertTrue(
            DependencyConfiguration.isDependentOn(
                BillingRateType.CTC_DAYS,
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            ),
        )

        assertFalse(
            DependencyConfiguration.isDependentOn(
                BillingRateType.BASE_PAY_PERCENTAGE,
                CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            ),
        )
        assertFalse(
            DependencyConfiguration.isDependentOn(
                BillingRateType.CTC_PERCENTAGE,
                CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            ),
        )
        assertFalse(
            DependencyConfiguration.isDependentOn(BillingRateType.VALUE, CategoryConstants.CATEGORY_CONTRACT_BASE_PAY),
        )
    }
}
