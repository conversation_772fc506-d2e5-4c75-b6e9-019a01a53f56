package com.multiplier.compensation.service.compensation.termination

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.CompensationServiceErrorCode
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.payschedule.PayScheduleService
import org.springframework.stereotype.Service
import java.time.LocalDate

/**
 * Terminates a compensation and returns a copy of that compensation, with the expected data to reflect its termination.
 *
 * For any extensions to this class that make it complex, please refactor this class to use a Strategy design pattern.
 */
@Service
class CompensationTerminationStrategy(
    private val payScheduleService: PayScheduleService,
) {
    fun terminate(
        compensation: Compensation,
        expectedTerminationDate: LocalDate,
        paySchedule: PaySchedule,
    ): Compensation = when {
        compensation.isInstallment -> terminateInstallmentCompensation(
            compensation,
            expectedTerminationDate,
            paySchedule,
        )
        else -> terminateNonInstallmentCompensation(compensation, expectedTerminationDate)
    }

    private fun terminateInstallmentCompensation(
        compensation: Compensation,
        expectedTerminationDate: LocalDate,
        paySchedule: PaySchedule,
    ): Compensation {
        val currentCompensationStartDate = compensation.startDate
        val currentCompensationEndDate = compensation.endDate

        return when {
            // If the installment is supposed to start after expectedTerminationDate, we can simply cancel it.
            currentCompensationStartDate > expectedTerminationDate -> compensation.copy(
                status = CompensationStatus.DELETED,
            )

            // If the installment period is overlapping with expectedTerminationDate, we need to adjust the number of installments.
            currentCompensationStartDate <= expectedTerminationDate &&
                currentCompensationEndDate != null &&
                currentCompensationEndDate >= expectedTerminationDate -> adjustInstallments(
                paySchedule,
                compensation,
                expectedTerminationDate,
            )

            // Any other scenario, including the case when - Compensation is already completed.
            // (If currentCompensationEndDate is before the expectedTerminationDate)
            else -> throw InvalidArgumentException(
                CompensationServiceErrorCode.CompensationTerminationFailed,
                "Termination could not be done for installment compensation [${compensation.id}]" +
                    "for start date [$currentCompensationStartDate] end date [$currentCompensationEndDate] and" +
                    "expectedTerminationDate [$expectedTerminationDate]",
            )
        }
    }

    private fun adjustInstallments(
        paySchedule: PaySchedule,
        compensation: Compensation,
        expectedTerminationDate: LocalDate,
    ): Compensation {
        val overlappingPayScheduleItemsWithRevisedTerminationDate =
            payScheduleService.getPayScheduleItems(
                paySchedule.id,
                compensation.startDate,
                expectedTerminationDate,
            )

        return when {
            overlappingPayScheduleItemsWithRevisedTerminationDate.isEmpty() -> compensation.copy(
                status = CompensationStatus.DELETED,
            )
            else -> compensation.copy(
                noOfInstallments = overlappingPayScheduleItemsWithRevisedTerminationDate.size,
                endDate = overlappingPayScheduleItemsWithRevisedTerminationDate.maxOf { it.endDate },
            )
        }
    }

    private fun terminateNonInstallmentCompensation(
        compensation: Compensation,
        expectedTerminationDate: LocalDate,
    ): Compensation {
        val currentCompensationStartDate = compensation.startDate
        val currentCompensationEndDate = compensation.endDate

        return when {
            // If the compensation has an end date and its period is overlapping with expectedTerminationDate,
            // we need to adjust the end date to expectedTerminationDate.
            currentCompensationStartDate <= expectedTerminationDate &&
                currentCompensationEndDate != null &&
                currentCompensationEndDate > expectedTerminationDate -> compensation.copy(
                endDate = expectedTerminationDate,
            )

            // If the compensation does not have an end date (To signify infinite intervals),
            // and its period is overlapping with expectedTerminationDate,
            // we need to adjust the end date to expectedTerminationDate.
            currentCompensationStartDate <= expectedTerminationDate &&
                currentCompensationEndDate == null -> compensation.copy(
                endDate = expectedTerminationDate,
            )

            // If the compensation is starting after expectedTerminationDate, we can simply cancel it.
            currentCompensationStartDate > expectedTerminationDate -> compensation.copy(
                status = CompensationStatus.DELETED,
            )

            // Any other scenario, including the case when - Compensation is already completed.
            // (If currentCompensationEndDate is before the expectedTerminationDate)
            else -> throw InvalidArgumentException(
                CompensationServiceErrorCode.CompensationTerminationFailed,
                "Termination could not be done for non-installment compensation [${compensation.id}]" +
                    "for start date [$currentCompensationStartDate] end date [$currentCompensationEndDate] and" +
                    "expectedTerminationDate [$expectedTerminationDate]",
            )
        }
    }
}
