package com.multiplier.compensation.graph.compensationitem.mapper

import com.multiplier.compensation.graph.common.mapper.toGraph
import com.multiplier.compensation.graph.common.mapper.toGraphqlStatus
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemsUserUpdateResponse
import com.multiplier.graph.types.CompensationComponentDetails
import com.multiplier.graph.types.FieldType
import com.multiplier.graph.types.RecordDetails
import com.multiplier.graph.types.UpdateCompensationItemResult
import com.multiplier.graph.types.UpdateCompensationItemsResponse

fun CompensationItemsUserUpdateResponse.toGraph(): UpdateCompensationItemsResponse = UpdateCompensationItemsResponse(
    status = this.status.toGraphqlStatus(),
    results = this.results.map {
        UpdateCompensationItemResult(
            compensationItemId = it.compensationItemId,
            resultType = it.resultType.toGraph(),
            message = it.message,
            updatedDetails = RecordDetails(
                id = it.compensationItemId.toString(),
                componentDetails = buildList {
                    it.updatedBillingRate?.let { rate ->
                        add(
                            CompensationComponentDetails(
                                key = CompensationSkeletonField.BILLING_RATE.id,
                                value = rate.toString(),
                                type = FieldType.DOUBLE,
                            ),
                        )
                    }
                },
            ),
        )
    },
)
