package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.dto.paySchedulesById
import com.multiplier.compensation.service.compensation.validation.DependencyConfiguration
import org.springframework.stereotype.Component
import java.time.LocalDate

/**
 * This class validates the dependent component's term against the parent component's term,
 * derived from the DB (if available) and the input.
 * Updated to use DependencyConfiguration for all parent-dependent relationships.
 */
@Component
class BasePayDependantDateRangeValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        draft.contractId ?: return false

        // Note: Ideally this should not happen since it has been validated already in skeleton validator
        draft.payScheduleId ?: return false

        // Check if this is a dependent billing rate type using DependencyConfiguration
        if (!DependencyConfiguration.isDependentBillingRateType(draft.billingRateType ?: return true)) return true

        val paySchedulesById = context.paySchedules.paySchedulesById()
        val paySchedule = paySchedulesById[draft.payScheduleId] ?: return false

        val dependantDraft = modifyEndDateIfInstalments(
            draft = draft,
            paySchedule = paySchedule,
        )

        val (minParentStartDate, maxParentEndDate) = findValidParentDateRange(context, draft, collector)

        val isDependantValid = checkIfCurrentDependantIsValid(
            dependantDraft = dependantDraft,
            minParentStartDate = minParentStartDate,
            maxParentEndDate = maxParentEndDate,
        )

        if (!isDependantValid) {
            addValidationResult(
                input = input,
                field = CompensationSkeletonField.COMPONENT_NAME,
                resultType = ValidationResultType.ERROR,
                message = "Dependant component dates should fall within the parent component start and end date",
                collector = collector,
            )
            return false
        }

        return true
    }

    private fun checkIfCurrentDependantIsValid(
        dependantDraft: CompensationDraft,
        minParentStartDate: LocalDate?,
        maxParentEndDate: LocalDate?,
    ): Boolean {
        if (minParentStartDate == null) return false

        val isStartDateValid = dependantDraft.startDate != null && dependantDraft.startDate >= minParentStartDate
        val isEndDateValid = maxParentEndDate == null ||
            (dependantDraft.endDate != null && dependantDraft.endDate <= maxParentEndDate)

        return isStartDateValid && isEndDateValid
    }
}
