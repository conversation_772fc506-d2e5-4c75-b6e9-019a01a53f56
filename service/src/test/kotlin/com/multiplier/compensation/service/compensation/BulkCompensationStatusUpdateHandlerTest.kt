package com.multiplier.compensation.service.compensation

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.service.compensation.termination.BulkCompensationTerminationService
import com.multiplier.compensation.service.compensationinput.dto.UpdateCompensationStatusBulkRequest
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class BulkCompensationStatusUpdateHandlerTest {
    private lateinit var handler: BulkCompensationStatusUpdateHandler
    private lateinit var bulkActivationService: BulkCompensationActivationService
    private lateinit var bulkTerminationService: BulkCompensationTerminationService

    @BeforeEach
    fun setUp() {
        bulkActivationService = mockk(relaxed = true)
        bulkTerminationService = mockk(relaxed = true)

        handler = BulkCompensationStatusUpdateHandler(
            bulkActivationService,
            bulkTerminationService,
        )
    }

    @Test
    fun `should call activate on bulkActivationService when status is ACTIVATED`() {
        val request = UpdateCompensationStatusBulkRequest(
            entityId = 1L,
            contractIds = setOf(1L, 2L),
            status = CompensationInputStatus.ACTIVATED,
        )

        handler.update(request)

        verify { bulkActivationService.activate(request.entityId, request.contractIds) }
    }

    @Test
    fun `should call terminate on bulkTerminationService when status is TERMINATED`() {
        val request = UpdateCompensationStatusBulkRequest(
            entityId = 1L,
            contractIds = setOf(1L, 2L),
            status = CompensationInputStatus.TERMINATED,
        )

        handler.update(request)

        verify { bulkTerminationService.terminate(request.contractIds) }
    }

    @Test
    fun `should throw InvalidArgumentException for unsupported status`() {
        val request = UpdateCompensationStatusBulkRequest(
            entityId = 1L,
            contractIds = setOf(1L, 2L),
            status = CompensationInputStatus.ABORTED,
        )

        assertThrows<InvalidArgumentException> {
            handler.update(request)
        }
    }
}
