package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.extrapolation.CompensationFrequencyConverter
import com.multiplier.compensation.service.compensation.extrapolation.utils.FormulaRequest
import com.multiplier.compensation.service.compensation.extrapolation.utils.evaluateFormula
import org.springframework.stereotype.Component

@Component
class BillingRateInputValidator(
    private val compensationFrequencyConverter: CompensationFrequencyConverter,
) : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val convertedDraft = compensationFrequencyConverter.adjustBillingFrequencyOnInputDrafts(
            listOf(draft),
            context,
        ).first()
        convertedDraft.schemaItemId ?: return false
        val schemaItem = context.getCompensationSchemaItem(convertedDraft.schemaItemId)
        schemaItem ?: return false

        if (schemaItem.itemType == ItemType.INPUT && schemaItem.validation != null) {
            val componentName = schemaItem.componentName
            val billingRate = convertedDraft.billingRate?.toBigDecimal() ?: 0.0.toBigDecimal()
            val variableMap = mapOf(componentName to billingRate)
            val validationFormula = schemaItem.validation ?: return true

            try {
                val formulaRequest = FormulaRequest(
                    formula = validationFormula,
                    variables = variableMap,
                )
                val result = evaluateFormula(formulaRequest)

                if (result == 0.0) {
                    addValidationResult(
                        input = input,
                        field = CompensationSkeletonField.COMPONENT_NAME,
                        resultType = ValidationResultType.ERROR,
                        message = "The billing rate value for $componentName does not meet the required criteria.",
                        collector = collector,
                    )
                    return false
                }
            } catch (e: Exception) {
                addValidationResult(
                    input = input,
                    field = CompensationSkeletonField.COMPONENT_NAME,
                    resultType = ValidationResultType.ERROR,
                    message = "Invalid validation expression for $componentName: ${e.message}",
                    collector = collector,
                )
                return false
            }
        }
        return true
    }
}
