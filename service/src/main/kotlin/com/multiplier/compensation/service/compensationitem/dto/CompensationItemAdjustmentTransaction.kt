package com.multiplier.compensation.service.compensationitem.dto

import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.transaction.database.jooq.transaction.DeferredTransaction
import com.multiplier.transaction.database.jooq.transaction.deferredTransaction

class CompensationItemAdjustmentTransaction(
    private val updatedItems: List<CompensationItem>,
    private val newItems: List<CompensationItem>,
    private val compensationItemRepository: CompensationItemRepository,
) {
    val newCompensationItems: List<CompensationItem>
        get() = newItems.toList()

    val updatedCompensationItems: List<CompensationItem>
        get() = updatedItems.toList()

    fun execute(): DeferredTransaction<Unit, Unit> = deferredTransaction {
        compensationItemRepository.updateAll(updatedItems, this)
        compensationItemRepository.saveAll(newItems, this)
    }
}
