package com.multiplier.compensation.assertions

import com.multiplier.compensation.ScenarioContext
import com.multiplier.compensation.TestConstants
import com.multiplier.compensation.dbRepository.CompensationItemDb
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.IntegrationTestErrorCode
import com.multiplier.compensation.grpc.schema.CompensationItem
import com.multiplier.compensation.grpc.schema.CompensationItemsResponse
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.utils.GeneralUtils
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputRequest
import org.jooq.Record
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.stereotype.Component
import org.springframework.test.context.ActiveProfiles
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("integration-test")
@Component
class CompensationItemAssertions(
    @Autowired
    private val genericAssertions: GenericAssertions,
    @Autowired
    private val compensationItemDb: CompensationItemDb,
) {
    fun assertCompensationItem(
        responseMap: Map<String, Any>,
        validateUpsertMap: Map<String, List<ValidateUpsertInputRequest>>,
    ) {
        validateUpsertMap.forEach { (entityId, outputList) ->
            val schemaList =
                ScenarioContext.get(
                    TestConstants.COMPENSATION_SCHEMA_INPUT_LIST,
                ) as? Map<String, List<ValidateUpsertInputRequest>>
                    ?: throw InvalidArgumentException(
                        errorCode = IntegrationTestErrorCode.IntegrationTestFailed,
                        message = "Compensation schema input list not found in ScenarioContext",
                    )

            schemaList[entityId]?.let { schema ->
                handleResponses(responseMap, outputList, schema)
            }
        }
    }

    private fun handleResponses(
        responseMap: Map<String, Any>,
        outputList: List<ValidateUpsertInputRequest>,
        schemaList: List<ValidateUpsertInputRequest>,
    ) {
        mapOf(
            TestConstants.COMP_IDS to ::handleIdResponse,
            TestConstants.COMP_CONTRACT_ID to ::handleContractIdResponse,
            TestConstants.COMP_STATUS to ::handleStatusResponse,
            TestConstants.COMP_EXCLUDED_CATEGORIES to ::handleExcludedFilterResponse,
            TestConstants.COMP_INCLUDED_CATEGORIES to ::handleIncludedFilterResponse,
        ).forEach { (key, handler) ->
            val response = responseMap[key] as? CompensationItemsResponse
            handler(response, outputList, schemaList)
        }
    }

    private fun handleIdResponse(
        response: CompensationItemsResponse?,
        outputList: List<ValidateUpsertInputRequest>,
        schemaList: List<ValidateUpsertInputRequest>,
    ) = response?.itemsList?.firstOrNull()?.compensationName?.let { name ->
        val matchingList = outputList.filter { it.dataMap.containsValue(name) }
        assertCompensationData(matchingList, response, schemaList)
    }

    private fun handleContractIdResponse(
        response: CompensationItemsResponse?,
        outputList: List<ValidateUpsertInputRequest>,
        schemaList: List<ValidateUpsertInputRequest>,
    ) = response?.itemsList?.firstOrNull()?.contractId?.let { id ->
        val matchingList = outputList.filter { it.keys.contractId == id }
        assertCompensationData(matchingList, response, schemaList)
    }

    private fun handleStatusResponse(
        response: CompensationItemsResponse?,
        outputList: List<ValidateUpsertInputRequest>,
        schemaList: List<ValidateUpsertInputRequest>,
    ) = response?.itemsList?.firstOrNull()?.status?.toString()?.let { status ->
        val matchingList = outputList.filter { it.dataMap.containsValue(status) }
        assertCompensationData(matchingList, response, schemaList)
    }

    private fun handleExcludedFilterResponse(
        response: CompensationItemsResponse?,
        outputList: List<ValidateUpsertInputRequest>,
        schemaList: List<ValidateUpsertInputRequest>,
    ) = response?.itemsList?.flatMap { item ->
        outputList.filter {
            it.dataMap["COMPONENT_NAME"] == item.compensationName &&
                it.keys.entityId == item.entityId
        }
    }?.let { matchingList ->
        assertCompensationData(matchingList, response, schemaList)
    }

    private fun handleIncludedFilterResponse(
        response: CompensationItemsResponse?,
        outputList: List<ValidateUpsertInputRequest>,
        schemaList: List<ValidateUpsertInputRequest>,
    ) = response?.itemsList?.firstOrNull()?.compensationName?.let { name ->
        val matchingList = outputList.filter { it.dataMap.containsValue(name) }
        assertCompensationData(matchingList, response, schemaList)
    }

    private fun assertCompensationData(
        outputList: List<ValidateUpsertInputRequest>,
        response: CompensationItemsResponse,
        schemaList: List<ValidateUpsertInputRequest>,
    ) {
        outputList.forEach { output ->
            val componentName = output.dataMap[CompensationSkeletonField.COMPONENT_NAME.toString()]
                ?: throw IllegalArgumentException("COMPONENT_NAME is missing in dataMap")

            val matchingItem = response.itemsList.find {
                it.compensationName == componentName && it.contractId == output.keys.contractId
            }

            val matchingSchema = schemaList.find {
                it.dataMap["COMPONENT_NAME"] == componentName
            }

            matchingItem?.let { item ->
                assertEntityKeys(output, item)
                matchingSchema?.let { schema ->
                    assertFields(output, item, schema)
                }
            }
        }
    }

    private fun assertEntityKeys(
        upsertRequest: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        assertEquals(
            upsertRequest.keys.entityId.toDouble(),
            item.entityId.toDouble(),
            "Entity IDs do not match.",
        )
    }

    private fun assertFields(
        output: ValidateUpsertInputRequest,
        item: CompensationItem,
        schema: ValidateUpsertInputRequest,
    ) {
        categoryKeyAssertion(schema, item)
        currencyAssertion(output, item)
        billingRateAssertion(output, item)
        billingFrequencyAssertion(output, item)
        payScheduleNameAssertion(output, item)
        numberOfInstallmentsAssertion(output, item)
        assertBooleanFields(schema, item)
    }

    private fun assertBooleanFields(
        schema: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        listOf(
            "IS_TAXABLE" to item.isTaxable,
            "IS_FIXED" to item.isFixed,
            "IS_PRORATED" to item.isProrated,
            "IS_MANDATORY" to item.isMandatory,
            "IS_PART_OF_BASE_PAY" to item.isPartOfBasePay,
        ).forEach { (key, actualValue) ->
            val expectedValue = schema.dataMap[key]?.let { genericAssertions.convertToBoolean(it) }
            assertEquals(expectedValue, actualValue, "Mismatch in $key.")
        }
    }

    private fun categoryKeyAssertion(
        schema: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        val expectedCategory = schema.dataMap["CATEGORY_KEY"]
        assertEquals(expectedCategory, item.compensationCategory, "Compensation categories do not match.")
    }

    private fun currencyAssertion(
        output: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        val expectedCurrency = output.dataMap[CommonSkeletonField.CURRENCY.toString()]
        assertEquals(expectedCurrency, item.currency, "Currencies do not match.")
    }

    private fun billingRateAssertion(
        output: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        val expectedBillingRate = output.dataMap[CompensationSkeletonField.BILLING_RATE.toString()]?.toDoubleOrNull()
        assertEquals(expectedBillingRate, item.billingRate, "Billing rates do not match.")
    }

    private fun billingFrequencyAssertion(
        output: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        val expectedFrequency =
            "${CompensationSkeletonField.BILLING_FREQUENCY}_${output.dataMap[CompensationSkeletonField.BILLING_FREQUENCY.toString()]}"
        assertEquals(expectedFrequency, item.billingFrequency.toString(), "Billing frequencies do not match.")
    }

    private fun payScheduleNameAssertion(
        output: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        val expectedName = output.dataMap[CompensationSkeletonField.PAY_SCHEDULE_NAME.toString()]
        assertEquals(expectedName, item.payScheduleName.toString(), "Pay schedule names do not match.")
    }

    private fun numberOfInstallmentsAssertion(
        output: ValidateUpsertInputRequest,
        item: CompensationItem,
    ) {
        val expectedInstallments = output.dataMap[CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.toString()]
        assertEquals(
            expectedInstallments,
            genericAssertions.parseNumberOfInstallments(item.numberOfInstallments),
            "Number of installments do not match.",
        )
    }

    fun compensationItemAssertion(
        contractId: String,
        resultCount: Int,
        dataMap: MutableMap<String, String>,
    ) {
        val compensationInputPostRevision = compensationItemDb.getCompensationItemViaContractId(contractId)
        val revisedEntries = compensationInputPostRevision.count() - resultCount
        assertTrue(
            resultCount < compensationInputPostRevision.count(),
            "Count mismatch in compensationItem, for $contractId, " +
                "expected result count is $resultCount, but actual result " +
                "count is ${compensationInputPostRevision.count()}",
        )
        val revisedRecords = compensationInputPostRevision.fetch().takeLast(revisedEntries)
        revisedRecords.forEach { record ->
            validateRecord(contractId, dataMap, record)
        }
    }

    private fun validateRecord(
        contractId: String,
        dataMap: MutableMap<String, String>,
        record: Record,
    ) {
        assertEquals(
            dataMap[TestConstants.BILLING_RATE]?.toDoubleOrNull(),
            (record[TestConstants.BILLING_RATE] as? Double)?.let { GeneralUtils.formatNumber(it) }?.toDoubleOrNull(),
            "billing Rate mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.START_DATE],
            record[TestConstants.COMPENSATION_START_DATE]?.toString(),
            "startDate mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.BILLING_FREQUENCY],
            record[TestConstants.BILLING_FREQUENCY]?.toString(),
            "billingFrequency mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.END_DATE],
            record[TestConstants.COMPENSATION_END_DATE]?.toString(),
            "endDate mismatch for $contractId",
        )
        assertEquals(
            dataMap[TestConstants.CURRENCY],
            record[TestConstants.CURRENCY]?.toString(),
            "CURRENCY mismatch for $contractId",
        )
        assertEquals(TestConstants.NEW, record[TestConstants.STATUS]?.toString(), "status mismatch for $contractId")
        assertEquals(
            ScenarioContext.get(TestConstants.COMPENSATION_SCHEMA_ID),
            record[TestConstants.SCHEMA_ITEM_ID]?.toString(),
            "SchemaItemId mismatch for $contractId",
        )
        assertEquals(
            ScenarioContext.get(TestConstants.COMPENSATION_ID),
            record[TestConstants.COMPENSATION_ID]?.toString(),
            "compensationId mismatch for $contractId",
        )
    }

    fun paySupplementAssertion(
        contractId: String,
        dataMap: MutableMap<String, String>,
    ) {
        val paySupplementEntryInCompensationItem = compensationItemDb.getCompensationItemViaCategoryAndContract(
            "PAY_SUPPLEMENT",
            contractId,
        )
        paySupplementEntryInCompensationItem.forEach { record ->
            validateRecord(contractId, dataMap, record)
        }
    }

    fun deductionAssertion(
        category: String,
        contractId: String,
        dataMap: MutableMap<String, String>,
    ) {
        val deductionEntryInCompensationItem = compensationItemDb.getCompensationItemViaCategoryAndContract(
            category,
            contractId,
        )
        deductionEntryInCompensationItem.forEach { record ->
            validateRecord(contractId, dataMap, record)
        }
    }
}
