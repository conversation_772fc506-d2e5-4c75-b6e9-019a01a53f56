package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import org.springframework.stereotype.Component

@Component
class PayScheduleFrequencyEnricher : Enricher {
    override fun enrich(
        skeletonType: SkeletonType,
        entityId: Long,
        customParams: Map<String, String>,
        skeletonData: SkeletonStructureData,
    ): List<String> = PayScheduleFrequency.entries.map { it.name }
}
