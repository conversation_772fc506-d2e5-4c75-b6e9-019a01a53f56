name: Deploy to production [Rollback]

on:
  workflow_dispatch:

run-name: Deploy [${{ github.ref_name }}] to production [rollback]

jobs:
  deploy:
    # Only a tag is allowed to deploy
    if: startsWith(github.ref, 'refs/tags/')
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: production
      service_name: compensationService
      ecr_repository: prd-app-tech-compensationservice-ecr
      allow_rollback: true
