package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.common.ValidatorUtil.getOriginalInputByKey
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import org.springframework.stereotype.Component

@Component
class ActiveCompensationEntryValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val contractId = collector.drafts[input.id]?.contractId

        if (isSetupValidation(context, contractId)) {
            return true
        } else if (contractId == null) {
            // Not in setup validation, so validation should fail if contract is not given
            return false
        }

        if (context.activeCompContractIds.contains(contractId)) {
            val employeeId = getOriginalInputByKey(CommonSkeletonField.EMPLOYEE_ID, input).value
            addValidationResult(
                input = input,
                field = CommonSkeletonField.EMPLOYEE_ID,
                resultType = ValidationResultType.ERROR,
                message = "Compensation already exists for employee id $employeeId and hence cannot be added again",
                collector = collector,
            )
            return false
        }

        return true
    }
}
