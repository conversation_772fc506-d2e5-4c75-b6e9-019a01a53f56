package com.multiplier.compensation.grpc.server.service.payschedule

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.grpc.schema.Common.OfferingType
import com.multiplier.compensation.grpc.schema.payschedule.PayScheduleFrequency
import com.multiplier.compensation.grpc.server.mapper.payschedule.toDomain
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import com.multiplier.compensation.grpc.schema.payschedule.GetEligibleBillingFrequenciesRequest as GrpcGetEligibleBillingFrequenciesRequest

class GetEligibleBillingFrequenciesRequestMapperTest {
    @Test
    fun `test toDomain mapping`() {
        // Arrange
        val grpcRequest = GrpcGetEligibleBillingFrequenciesRequest.newBuilder()
            .setEntityId(123L)
            .setCountryCode("USA")
            .setOfferingType(OfferingType.EOR)
            .setState("CA")
            .setGrossPayFrequency(PayScheduleFrequency.WEEKLY)
            .setCompensationCategory("CONTRACT_BASE_PAY")
            .build()

        // Act
        val domainRequest: GetEligibleBillingFrequenciesRequest = grpcRequest.toDomain()

        // Assert
        assertEquals(123L, domainRequest.entityId)
        assertEquals(CountryCode.USA, domainRequest.countryCode)
        assertEquals("CA", domainRequest.state)
        assertEquals("CONTRACT_BASE_PAY", domainRequest.compensationCategory)
        assertEquals(com.multiplier.compensation.domain.common.OfferingCode.EOR, domainRequest.offeringCode)
    }
}
