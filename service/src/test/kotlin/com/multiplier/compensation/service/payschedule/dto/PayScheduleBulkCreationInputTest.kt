package com.multiplier.compensation.service.payschedule.dto

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.RowItem
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class PayScheduleBulkCreationInputTest {
    @Test
    fun `payScheduleBulkCreationInput should be correctly created`() {
        val entityId = 1234L
        val commitPartially = false
        val rowItems = listOf(
            RowItem(
                id = "12345",
                keyValuePairs = listOf(
                    KeyValuePair("Key1", " Value1"),
                ),
            ),
        )
        val request = PayScheduleBulkCreationInput(
            entityId = entityId,
            commitPartially = commitPartially,
            customParams = emptyMap(),
            rowItems = rowItems,
        )

        assertEquals(entityId, request.entityId)
        assertEquals(commitPartially, request.commitPartially)
        assertEquals(1, rowItems.size)
        assertEquals("12345", request.rowItems[0].id)
    }

    @Test
    fun `payScheduleBulkCreationInput should be created with default row items`() {
        val input = PayScheduleBulkCreationInput(entityId = 12345L, commitPartially = true, customParams = emptyMap())

        assertEquals(12345L, input.entityId)
        assertEquals(true, input.commitPartially)
        assertEquals(emptyList<RowItem>(), input.rowItems)
    }
}
