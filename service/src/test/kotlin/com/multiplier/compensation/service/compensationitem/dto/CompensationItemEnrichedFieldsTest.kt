package com.multiplier.compensation.service.compensationitem.dto

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class CompensationItemEnrichedFieldsTest {
    @Test
    fun `should map all enum constants to correct field names`() {
        val expectedMappings = mapOf(
            "ID" to CompensationItemEnrichedFields.ID,
            "COMPANY_ID" to CompensationItemEnrichedFields.COMPANY_ID,
            "ENTITY_ID" to CompensationItemEnrichedFields.ENTITY_ID,
            "CONTRACT_ID" to CompensationItemEnrichedFields.CONTRACT_ID,
            "CATEGORY" to CompensationItemEnrichedFields.CATEGORY,
            "CURRENCY" to CompensationItemEnrichedFields.CURRENCY,
            "BILLING_RATE_TYPE" to CompensationItemEnrichedFields.BILLING_RATE_TYPE,
            "BILLING_RATE" to CompensationItemEnrichedFields.BILLING_RATE,
            "BILLING_FREQUENCY" to CompensationItemEnrichedFields.BILLING_FREQUENCY,
            "START_DATE" to CompensationItemEnrichedFields.START_DATE,
            "END_DATE" to CompensationItemEnrichedFields.END_DATE,
            "IS_INSTALLMENT" to CompensationItemEnrichedFields.IS_INSTALLMENT,
            "NO_OF_INSTALLMENTS" to CompensationItemEnrichedFields.NO_OF_INSTALLMENTS,
            "CREATED_ON" to CompensationItemEnrichedFields.CREATED_ON,
            "CREATED_BY" to CompensationItemEnrichedFields.CREATED_BY,
            "CUT_OFF_DATE" to CompensationItemEnrichedFields.CUT_OFF_DATE,
            "EXPECTED_PAY_DATE" to CompensationItemEnrichedFields.EXPECTED_PAY_DATE,
            "CALCULATED_AMOUNT" to CompensationItemEnrichedFields.CALCULATED_AMOUNT,
            "PAY_DATE" to CompensationItemEnrichedFields.PAY_DATE,
            "PREVIOUS_ID" to CompensationItemEnrichedFields.PREVIOUS_ID,
            "STATUS" to CompensationItemEnrichedFields.STATUS,
            "IS_ARREAR" to CompensationItemEnrichedFields.IS_ARREAR,
            "ARREAR_OF" to CompensationItemEnrichedFields.ARREAR_OF,
            "UPDATED_ON" to CompensationItemEnrichedFields.UPDATED_ON,
            "UPDATED_BY" to CompensationItemEnrichedFields.UPDATED_BY,
            "CURRENT_INSTALLMENT" to CompensationItemEnrichedFields.CURRENT_INSTALLMENT,
            "PAY_SCHEDULE_NAME" to CompensationItemEnrichedFields.PAY_SCHEDULE_NAME,
            "PAY_SCHEDULE_FREQUENCY" to CompensationItemEnrichedFields.PAY_SCHEDULE_FREQUENCY,
            "SCHEMA_COMPONENT_NAME" to CompensationItemEnrichedFields.SCHEMA_COMPONENT_NAME,
            "IS_TAXABLE" to CompensationItemEnrichedFields.IS_TAXABLE,
            "IS_FIXED" to CompensationItemEnrichedFields.IS_FIXED,
            "IS_PRORATED" to CompensationItemEnrichedFields.IS_PRORATED,
            "IS_MANDATORY" to CompensationItemEnrichedFields.IS_MANDATORY,
            "IS_PART_OF_BASE_PAY" to CompensationItemEnrichedFields.IS_PART_OF_BASE_PAY,
            "IS_DELETABLE" to CompensationItemEnrichedFields.IS_DELETABLE,
            "IS_EDITABLE" to CompensationItemEnrichedFields.IS_EDITABLE,
            "STATE" to CompensationItemEnrichedFields.STATE,
        )

        expectedMappings.forEach { (fieldName, enumConstant) ->
            assertEquals(fieldName, enumConstant.fieldName)
        }
    }
}
