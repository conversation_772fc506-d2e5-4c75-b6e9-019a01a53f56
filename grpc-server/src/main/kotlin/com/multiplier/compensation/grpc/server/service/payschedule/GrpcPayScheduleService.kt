package com.multiplier.compensation.grpc.server.service.payschedule

import com.multiplier.compensation.domain.common.exception.CompensationServiceException
import com.multiplier.compensation.domain.common.exception.config.CompensationServiceErrorCode
import com.multiplier.compensation.grpc.schema.Common
import com.multiplier.compensation.grpc.schema.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.grpc.schema.payschedule.GetEligiblePaySchedulesRequest
import com.multiplier.compensation.grpc.schema.payschedule.PayScheduleRequest
import com.multiplier.compensation.grpc.schema.payschedule.PayScheduleResponse
import com.multiplier.compensation.grpc.schema.payschedule.PayScheduleServiceGrpcKt
import com.multiplier.compensation.grpc.server.mapper.payschedule.toDomain
import com.multiplier.compensation.grpc.server.mapper.payschedule.toGrpc
import com.multiplier.compensation.service.payschedule.PayScheduleService
import com.multiplier.grpc.common.toLocalDate
import com.multiplier.transaction.grpc.grpcApi
import io.github.oshai.kotlinlogging.KotlinLogging
import net.devh.boot.grpc.server.service.GrpcService
import kotlin.coroutines.CoroutineContext

private val log = KotlinLogging.logger {}

@GrpcService
class GrpcPayScheduleService(
    grpcContext: CoroutineContext,
    private val payScheduleService: PayScheduleService,
) : PayScheduleServiceGrpcKt.PayScheduleServiceCoroutineImplBase(coroutineContext = grpcContext) {
    override suspend fun getPaySchedules(request: PayScheduleRequest): PayScheduleResponse = grpcApi {
        log.info {
            "Get pay schedules for entities ${request.entityIdsList} " +
                "and date range between [${request.referenceStartDate}] to [${request.referenceEndDate}] "
        }
        try {
            return@grpcApi payScheduleService.getPaySchedules(
                entityIds = request.entityIdsList,
                referenceStartDate = request.referenceStartDate.toLocalDate(),
                referenceEndDate = request.referenceEndDate.toLocalDate(),
            ).toGrpc()
        } catch (exception: Exception) {
            throw CompensationServiceException(
                errorCode = CompensationServiceErrorCode.PayScheduleFailed,
                message = "Exception while fetching pay schedules for entities ${request.entityIdsList} " +
                    "and date range between [${request.referenceStartDate}] to [${request.referenceEndDate}] ",
                context = mapOf(
                    "entities" to request.entityIdsList,
                    "referenceStartDate" to request.referenceStartDate,
                    "referenceEndDate" to request.referenceEndDate,
                ),
                exception = exception,
            )
        }
    }

    override suspend fun getEligiblePaySchedules(request: GetEligiblePaySchedulesRequest): PayScheduleResponse =
        grpcApi {
            log.info { "Get eligible pay schedules for request $request" }
            try {
                return@grpcApi payScheduleService.getEligiblePaySchedules(request = request.toDomain()).toGrpc()
            } catch (exception: Exception) {
                throw CompensationServiceException(
                    errorCode = CompensationServiceErrorCode.PayScheduleFailed,
                    message = "Exception while fetching eligible pay schedules for entities $request",
                    context = mapOf(
                        "countryCode" to request.countryCode,
                        "offeringCode" to request.offeringType,
                        "state" to request.state,
                        "grossSalaryPayScheduleFrequency" to request.grossPayFrequency,
                        "billingFrequency" to request.billingFrequency,
                        "compensationCategory" to request.compensationCategory,
                    ),
                    exception = exception,
                )
            }
        }

    override suspend fun getEligibleBillingFrequencies(
        request: GetEligibleBillingFrequenciesRequest,
    ): Common.BillingFrequencyResponse = grpcApi {
        log.info { "Get eligible billing frequencies for request $request" }
        try {
            return@grpcApi payScheduleService.getEligibleBillingFrequencies(
                request = request.toDomain(),
            ).toGrpc()
        } catch (exception: Exception) {
            throw CompensationServiceException(
                errorCode = CompensationServiceErrorCode.PayScheduleFailed,
                message = "Exception while fetching eligible billing frequencies for entities $request",
                context = mapOf(
                    "countryCode" to request.countryCode,
                    "offeringCode" to request.offeringType,
                    "state" to request.state,
                    "grossSalaryPayScheduleFrequency" to request.grossPayFrequency,
                    "compensationCategory" to request.compensationCategory,
                ),
                exception = exception,
            )
        }
    }
}
