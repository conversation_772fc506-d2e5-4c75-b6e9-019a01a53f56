package com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.annually

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.payschedule.common.getExpectedIntervals
import com.multiplier.compensation.service.payschedule.common.verifyIntervals
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockKExtension::class)
class InstallmentAnnuallyPayScheduleStrategyTest {
    private val strategy = InstallmentAnnuallyPayScheduleStrategy()
    private val sampleDataDirectory = "src/test/resources/payschedule/schedulegeneration/sample/annually/installment"

    @Test
    fun `for installment with start date same as year first date`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 12, 31),
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 1, 1),
            to = LocalDate.of(2027, 2, 25),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = getExpectedIntervals(
                "$sampleDataDirectory/" +
                    "annually_installment_test_1_start_date_reference_same_as_year_first_date.csv",
            ),
        )
    }

    @Test
    fun `for installment with start date same as month first date and not year first date`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 12, 31),
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 3, 1),
            to = LocalDate.of(2027, 8, 25),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = getExpectedIntervals(
                "$sampleDataDirectory/" +
                    "annually_installment_test_2_start_date_same_as_month_first_date_and_not_year_first_date.csv",
            ),
        )
    }

    @Test
    fun `for installment with start date not same as month first date nor year first date with leap year edge cases`() {
        val paySchedule = getMockedPaySchedule(
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 12, 31),
        )

        val intervals = strategy.generate(
            paySchedule = paySchedule,
            from = LocalDate.of(2024, 2, 29),
            to = LocalDate.of(2029, 1, 25),
        )

        verifyIntervals(
            intervals = intervals,
            expectedIntervals = getExpectedIntervals(
                "$sampleDataDirectory/" +
                    "annually_installment_test_3_start_date_not_same_as_month_first_date_nor_year_first_date_with_leap_year_edge_cases.csv",
            ),
        )
    }

    private fun getMockedPaySchedule(
        startDateReference: LocalDate,
        endDateReference: LocalDate,
    ) = PaySchedule(
        id = UUID.randomUUID(),
        entityId = 1L,
        companyId = 1L,
        name = "Yearly-05",
        frequency = PayScheduleFrequency.ANNUALLY,
        configurationScope = ConfigurationScope.COMPANY,
        country = CountryCode.USA,
        startDateReference = startDateReference,
        endDateReference = endDateReference,
        payDateReferenceType = PayDateReference.COMPENSATION_START_DATE,
        relativePayDays = 0,
        isInstallment = true,
        isActive = true,
        createdOn = LocalDateTime.now(),
        createdBy = 1,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1,
        label = "Yearly Pay Schedule",
    )
}
