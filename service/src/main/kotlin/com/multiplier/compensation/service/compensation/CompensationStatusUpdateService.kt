package com.multiplier.compensation.service.compensation

import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.validCompensationStatus
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.UUID

private val log = KotlinLogging.logger {}

@Service
class CompensationStatusUpdateService(
    private val compensationItemRepository: CompensationItemRepository,
    private val compensationRepository: CompensationRepository,
    private val transactional: Transactional,
) {
    /**
     * Refreshes the processing status dates for a given compensation.
     * - Fetches the earliest cutoff date from compensation items for evaluating processingFrom date
     * - Calculates last pay date from compensation items  for evaluating processingTo date
     *
     * @param compensationId The unique identifier of the compensation to refresh
     * @throws InvalidArgumentException if compensation is not found
     */
    fun refreshStatus(compensationId: UUID) {
        log.info { "Refresh processing dates for compensation [$compensationId]" }

        transactional {
            val compensation = compensationRepository.findById(compensationId)
                ?: throw InvalidArgumentException(
                    errorCode = ValidationErrorCode.CompensationNotFound,
                    message = "Compensation with Id [$compensationId] not found.",
                    context = mapOf("compensationId" to compensationId),
                )

            requireCondition(
                validCompensationStatus().contains(compensation.status),
                ValidationErrorCode.InvalidCompensationStatus,
                "Compensation with Id [${compensation.id}] has invalid status [${compensation.status}]",
                context = mapOf("compensationId" to compensationId),
            )

            val earliestCutoffDate = compensationItemRepository.findEarliestCutoffForCompensation(
                compensationId,
                this,
            )

            val lastPayDate = findLastPayDate(compensation, this)

            val updatedCompensation = compensation.copy(
                processingFrom = earliestCutoffDate,
                processingTo = lastPayDate,
            )

            compensationRepository.updateAll(listOf(updatedCompensation), this)
        }
    }

    private fun findLastPayDate(
        compensation: Compensation,
        transactionContext: TransactionContext,
    ): LocalDate? {
        if (compensation.endDate == null) return null

        val lastItem = compensationItemRepository.findLastCompensationItem(
            compensation.id,
            transactionContext,
        ) ?: return null

        return when (lastItem.endDate) {
            compensation.endDate -> compensationItemRepository.findLastPayDateForCompensation(
                compensation.id,
                transactionContext,
            )
            else -> null
        }
    }
}
