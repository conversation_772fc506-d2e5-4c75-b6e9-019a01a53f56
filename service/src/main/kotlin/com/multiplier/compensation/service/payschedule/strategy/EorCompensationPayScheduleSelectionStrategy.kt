package com.multiplier.compensation.service.payschedule.strategy

import com.multiplier.compensation.database.repository.payschedule.CountryPayScheduleConfigRepository
import com.multiplier.compensation.database.repository.payschedule.PayScheduleRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.CategoryConstants.isPrimarySalaryOrDirectDependentComponent
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import org.springframework.stereotype.Component

/**
 * Strategy implementation for EOR (Employer of Record) offering type.
 * Handles pay schedule and billing frequency logic specific to EOR.
 */
@Component
class EorCompensationPayScheduleSelectionStrategy(
    private val payScheduleRepository: PayScheduleRepository,
    private val countryPayScheduleConfigRepository: CountryPayScheduleConfigRepository,
) : CompensationPayScheduleSelectionStrategy {
    override fun getEligiblePaySchedules(request: GetEligiblePayScheduleRequest): List<PaySchedule> {
        validateRequest(request)

        val payScheduleFrequencies = if (isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)) {
            getGrossPayPayScheduleFrequencies(request)
        } else {
            getAdditionalComponentPayScheduleFrequencies(request)
        }

        return getPaySchedules(request, payScheduleFrequencies)
    }

    override fun getEligibleBillingFrequencies(request: GetEligibleBillingFrequenciesRequest): List<BillingFrequency> {
        validateBillingFrequenciesRequest(request)

        return if (isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)) {
            getGrossPayBillingFrequencies(request)
        } else {
            getAdditionalComponentBillingFrequencies(request)?.toList().orEmpty()
        }
    }

    override fun validateRequest(request: GetEligiblePayScheduleRequest) {
        if (!isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)) {
            requireCondition(
                condition = request.grossSalaryPayScheduleFrequency != null &&
                    request.grossSalaryPayScheduleFrequency in
                    grossPayScheduleToAdditionalPaysFrequenciesForEOR.keys,
                errorCode = ValidationErrorCode.InvalidPayScheduleFrequency,
                message = "Invalid pay schedule frequency [${request.grossSalaryPayScheduleFrequency}] " +
                    "to get the additional component pay schedules",
                context = mapOf(
                    "grossSalaryPayScheduleFrequency" to request.grossSalaryPayScheduleFrequency,
                    "validFrequencies" to grossPayScheduleToAdditionalPaysFrequenciesForEOR.keys,
                    "offeringCode" to request.offeringCode,
                ),
            )
        }
    }

    override fun validateBillingFrequenciesRequest(request: GetEligibleBillingFrequenciesRequest) {
        // Validate grossSalaryPayScheduleFrequency for additional components
        if (!isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)) {
            requireCondition(
                condition = request.grossSalaryPayScheduleFrequency != null &&
                    request.grossSalaryPayScheduleFrequency in
                    grossPayScheduleToAdditionalPaysFrequenciesForEOR.keys,
                errorCode = ValidationErrorCode.InvalidPayScheduleFrequency,
                message = "Pay schedule frequency [${request.grossSalaryPayScheduleFrequency}] is invalid",
                context = mapOf(
                    "grossSalaryPayScheduleFrequency" to request.grossSalaryPayScheduleFrequency,
                    "validFrequencies" to grossPayScheduleToAdditionalPaysFrequenciesForEOR.keys,
                    "offeringCode" to request.offeringCode,
                    "compensationCategory" to request.compensationCategory,
                ),
            )
        }
    }

    private fun getGrossPayBillingFrequencies(request: GetEligibleBillingFrequenciesRequest): List<BillingFrequency> =
        request.countryCode?.let { countryCode ->
            countryPayScheduleConfigRepository.getAllBillingFrequenciesByCountryAndState(
                country = countryCode,
                state = getUpdatedStateCode(countryCode, request.state),
            ).toMutableList()
        }.orEmpty()

    private fun getAdditionalComponentBillingFrequencies(
        request: GetEligibleBillingFrequenciesRequest,
    ): Set<BillingFrequency>? =
        grossPayScheduleToAdditionalPaysFrequenciesForEOR[request.grossSalaryPayScheduleFrequency]?.keys

    private fun getGrossPayPayScheduleFrequencies(request: GetEligiblePayScheduleRequest): List<PayScheduleFrequency> =
        request.countryCode?.let { countryCode ->
            countryPayScheduleConfigRepository.getAllPayScheduleFrequenciesByCountryStateAndBillingFrequency(
                country = countryCode,
                state = getUpdatedStateCode(countryCode, request.state),
                billingFrequency = request.billingFrequency,
            ).toMutableList()
        }.orEmpty()

    private fun getAdditionalComponentPayScheduleFrequencies(
        request: GetEligiblePayScheduleRequest,
    ): List<PayScheduleFrequency> = request.billingFrequency?.let { bf ->
        grossPayScheduleToAdditionalPaysFrequenciesForEOR[request.grossSalaryPayScheduleFrequency]?.get(bf)
    }?.toList().orEmpty()

    private fun getPaySchedules(
        request: GetEligiblePayScheduleRequest,
        frequencies: List<PayScheduleFrequency>,
    ): List<PaySchedule> {
        val isInstallment: Boolean? = false.takeIf {
            isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)
        }
        return payScheduleRepository.findAllEligiblePaySchedules(
            request = request,
            scope = getScopeByOfferingCode(request.offeringCode),
            frequencies = frequencies,
            isInstallment = isInstallment,
            excludeInactiveSchedules = true,
        ).toMutableList()
    }

    private fun getScopeByOfferingCode(offeringCode: OfferingCode): ConfigurationScope = when (offeringCode) {
        OfferingCode.EOR -> ConfigurationScope.COUNTRY
        else -> ConfigurationScope.COMPANY
    }

    private fun getUpdatedStateCode(
        countryCode: CountryCode,
        state: String?,
    ): String? {
        val states = countryPayScheduleConfigRepository.getStateCodesForCountry(countryCode)
        if (state != null && (states.isEmpty() || !states.contains(state))) {
            // State code is not required for the country or invalid state provided
            return null
        }
        return state
    }

    val grossPayScheduleToAdditionalPaysFrequenciesForEOR:
        Map<PayScheduleFrequency, Map<BillingFrequency, List<PayScheduleFrequency>>> =
        mapOf(
            PayScheduleFrequency.MONTHLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
                BillingFrequency.DAILY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.BIWEEKLY to listOf(PayScheduleFrequency.MONTHLY),
            ),
            PayScheduleFrequency.SEMI_MONTHLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.SEMIMONTHLY to listOf(PayScheduleFrequency.SEMI_MONTHLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
                BillingFrequency.DAILY to listOf(PayScheduleFrequency.SEMI_MONTHLY),
            ),
            PayScheduleFrequency.BI_WEEKLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.BIWEEKLY to listOf(PayScheduleFrequency.BI_WEEKLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
                BillingFrequency.DAILY to listOf(PayScheduleFrequency.BI_WEEKLY),
            ),
            PayScheduleFrequency.WEEKLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.WEEKLY to listOf(PayScheduleFrequency.WEEKLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
            ),
        )
}
