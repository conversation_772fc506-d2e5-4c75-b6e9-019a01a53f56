package com.multiplier.compensation.service.payschedule.schedulegeneration

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.payschedule.schedulegeneration.dto.PayScheduleInterval
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.PayScheduleStrategy
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.PayScheduleStrategyFactory
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals

class PayScheduleIntervalGeneratorTest {
    private lateinit var payScheduleIntervalGenerator: PayScheduleIntervalGenerator
    private lateinit var mockStrategy: PayScheduleStrategy

    @BeforeEach
    fun setUp() {
        payScheduleIntervalGenerator = PayScheduleIntervalGenerator()
        mockStrategy = mockk()
        mockkObject(PayScheduleStrategyFactory)
    }

    @AfterEach
    fun tearDown() {
        unmockkObject(PayScheduleStrategyFactory)
    }

    @ParameterizedTest
    @EnumSource(PayScheduleFrequency::class)
    fun `generateIntervals should call the correct strategy based on frequency`(frequency: PayScheduleFrequency) {
        // Given
        val paySchedule = createPaySchedule(frequency = frequency)
        val from = LocalDate.of(2024, 1, 1)
        val to = LocalDate.of(2024, 12, 31)
        val expectedIntervals = listOf(
            PayScheduleInterval(
                startDate = from,
                endDate = to,
                payDate = to.plusDays(5),
            ),
        )

        every {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = frequency,
                installment = paySchedule.isInstallment,
                payDateReferenceType = paySchedule.payDateReferenceType,
            )
        } returns mockStrategy

        every {
            mockStrategy.generate(
                paySchedule = paySchedule,
                from = from,
                to = to,
            )
        } returns expectedIntervals

        // When
        val result = payScheduleIntervalGenerator.generateIntervals(
            paySchedule = paySchedule,
            from = from,
            to = to,
        )

        // Then
        assertEquals(expectedIntervals, result)
        verify {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = frequency,
                installment = paySchedule.isInstallment,
                payDateReferenceType = paySchedule.payDateReferenceType,
            )
        }
        verify {
            mockStrategy.generate(
                paySchedule = paySchedule,
                from = from,
                to = to,
            )
        }
    }

    @Test
    fun `generateIntervals should handle installment pay schedules correctly`() {
        // Given
        val paySchedule = createPaySchedule(
            frequency = PayScheduleFrequency.MONTHLY,
            isInstallment = true,
        )
        val from = LocalDate.of(2024, 1, 1)
        val to = LocalDate.of(2024, 3, 31)
        val expectedIntervals = listOf(
            PayScheduleInterval(
                startDate = LocalDate.of(2024, 1, 1),
                endDate = LocalDate.of(2024, 1, 31),
                payDate = LocalDate.of(2024, 2, 5),
            ),
            PayScheduleInterval(
                startDate = LocalDate.of(2024, 2, 1),
                endDate = LocalDate.of(2024, 2, 29),
                payDate = LocalDate.of(2024, 3, 5),
            ),
            PayScheduleInterval(
                startDate = LocalDate.of(2024, 3, 1),
                endDate = LocalDate.of(2024, 3, 31),
                payDate = LocalDate.of(2024, 4, 5),
            ),
        )

        every {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = paySchedule.frequency,
                installment = true,
                payDateReferenceType = paySchedule.payDateReferenceType,
            )
        } returns mockStrategy

        every {
            mockStrategy.generate(
                paySchedule = paySchedule,
                from = from,
                to = to,
            )
        } returns expectedIntervals

        // When
        val result = payScheduleIntervalGenerator.generateIntervals(
            paySchedule = paySchedule,
            from = from,
            to = to,
        )

        // Then
        assertEquals(expectedIntervals, result)
        verify {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = paySchedule.frequency,
                installment = true,
                payDateReferenceType = paySchedule.payDateReferenceType,
            )
        }
    }

    @Test
    fun `generateIntervals should handle non-installment pay schedules correctly`() {
        // Given
        val paySchedule = createPaySchedule(
            frequency = PayScheduleFrequency.MONTHLY,
            isInstallment = false,
            payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
        )
        val from = LocalDate.of(2024, 1, 1)
        val to = LocalDate.of(2024, 3, 31)
        val expectedIntervals = listOf(
            PayScheduleInterval(
                startDate = LocalDate.of(2024, 1, 1),
                endDate = LocalDate.of(2024, 1, 31),
                payDate = LocalDate.of(2024, 2, 5),
            ),
            PayScheduleInterval(
                startDate = LocalDate.of(2024, 2, 1),
                endDate = LocalDate.of(2024, 2, 29),
                payDate = LocalDate.of(2024, 3, 5),
            ),
            PayScheduleInterval(
                startDate = LocalDate.of(2024, 3, 1),
                endDate = LocalDate.of(2024, 3, 31),
                payDate = LocalDate.of(2024, 4, 5),
            ),
        )

        every {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = paySchedule.frequency,
                installment = false,
                payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
            )
        } returns mockStrategy

        every {
            mockStrategy.generate(
                paySchedule = paySchedule,
                from = from,
                to = to,
            )
        } returns expectedIntervals

        // When
        val result = payScheduleIntervalGenerator.generateIntervals(
            paySchedule = paySchedule,
            from = from,
            to = to,
        )

        // Then
        assertEquals(expectedIntervals, result)
        verify {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = paySchedule.frequency,
                installment = false,
                payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
            )
        }
    }

    @Test
    fun `generateIntervals should handle different pay date reference types`() {
        // Given
        val paySchedule = createPaySchedule(
            frequency = PayScheduleFrequency.MONTHLY,
            isInstallment = false,
            payDateReferenceType = PayDateReference.PAY_SCHEDULE_START_DATE,
        )
        val from = LocalDate.of(2024, 1, 1)
        val to = LocalDate.of(2024, 1, 31)
        val expectedIntervals = listOf(
            PayScheduleInterval(
                startDate = from,
                endDate = to,
                payDate = from.plusDays(5),
            ),
        )

        every {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = paySchedule.frequency,
                installment = false,
                payDateReferenceType = PayDateReference.PAY_SCHEDULE_START_DATE,
            )
        } returns mockStrategy

        every {
            mockStrategy.generate(
                paySchedule = paySchedule,
                from = from,
                to = to,
            )
        } returns expectedIntervals

        // When
        val result = payScheduleIntervalGenerator.generateIntervals(
            paySchedule = paySchedule,
            from = from,
            to = to,
        )

        // Then
        assertEquals(expectedIntervals, result)
        verify {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = paySchedule.frequency,
                installment = false,
                payDateReferenceType = PayDateReference.PAY_SCHEDULE_START_DATE,
            )
        }
    }

    @Test
    fun `generateIntervals should handle one-time pay schedules correctly`() {
        // Given
        val paySchedule = createPaySchedule(
            frequency = PayScheduleFrequency.ONE_TIME,
            isInstallment = false,
        )
        val from = LocalDate.of(2024, 1, 1)
        val to = LocalDate.of(2024, 1, 1)
        val expectedIntervals = listOf(
            PayScheduleInterval(
                startDate = from,
                endDate = to,
                payDate = to.plusDays(5),
            ),
        )

        every {
            PayScheduleStrategyFactory.getPayScheduleStrategy(
                payScheduleFrequency = PayScheduleFrequency.ONE_TIME,
                installment = false,
                payDateReferenceType = paySchedule.payDateReferenceType,
            )
        } returns mockStrategy

        every {
            mockStrategy.generate(
                paySchedule = paySchedule,
                from = from,
                to = to,
            )
        } returns expectedIntervals

        // When
        val result = payScheduleIntervalGenerator.generateIntervals(
            paySchedule = paySchedule,
            from = from,
            to = to,
        )

        // Then
        assertEquals(expectedIntervals, result)
    }

    private fun createPaySchedule(
        id: UUID = UUID.randomUUID(),
        entityId: Long = 1L,
        companyId: Long = 1L,
        name: String = "Test Pay Schedule",
        frequency: PayScheduleFrequency = PayScheduleFrequency.MONTHLY,
        configurationScope: ConfigurationScope = ConfigurationScope.COMPANY,
        country: com.multiplier.compensation.domain.common.CountryCode? = null,
        startDateReference: LocalDate = LocalDate.of(2024, 1, 1),
        endDateReference: LocalDate = LocalDate.of(2024, 12, 31),
        relativePayDays: Long = 5L,
        payDateReferenceType: PayDateReference = PayDateReference.PAY_SCHEDULE_END_DATE,
        isInstallment: Boolean = false,
        isActive: Boolean = true,
        label: String = "Test Label",
        createdOn: LocalDateTime = LocalDateTime.now(),
        createdBy: Long = 1L,
        updatedOn: LocalDateTime = LocalDateTime.now(),
        updatedBy: Long = 1L,
    ): PaySchedule = PaySchedule(
        id = id,
        entityId = entityId,
        companyId = companyId,
        name = name,
        frequency = frequency,
        configurationScope = configurationScope,
        country = country,
        startDateReference = startDateReference,
        endDateReference = endDateReference,
        relativePayDays = relativePayDays,
        payDateReferenceType = payDateReferenceType,
        isInstallment = isInstallment,
        isActive = isActive,
        label = label,
        createdOn = createdOn,
        createdBy = createdBy,
        updatedOn = updatedOn,
        updatedBy = updatedBy,
    )
}
