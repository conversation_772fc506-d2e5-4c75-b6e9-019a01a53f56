package com.multiplier.compensation.grpc.client.adapter

import com.multiplier.compensation.domain.common.CountryCode

interface CountryServiceAdapter {
    /**
     * Retrieves country names for the given country codes.
     *
     * @param countryCodes Set of country codes to get names for
     * @return Map of country code to country name
     */
    suspend fun getCountryNamesByCodes(countryCodes: Set<CountryCode>): Map<CountryCode, String>
}
