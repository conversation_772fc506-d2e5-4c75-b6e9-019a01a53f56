package com.multiplier.compensation.service.grpc.client.config

import com.multiplier.company.schema.grpc.CompanyServiceGrpc
import com.multiplier.contract.offboarding.schema.ContractOffboardingServiceGrpcKt
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import com.multiplier.country.schema.CountryServiceGrpc
import com.multiplier.orgmanagement.schema.DepartmentServiceGrpc
import com.multiplier.orgmanagement.schema.ManagerServiceGrpc
import com.multiplier.payroll.schema.grpc.schema.PayrollSchemaServiceGrpc
import com.multiplier.pigeonservice.PigeonNotificationClient
import io.mockk.mockk
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class GrpcClientMockConfig {
    @Bean
    fun payrollSchemaServiceBlockingStub(): PayrollSchemaServiceGrpc.PayrollSchemaServiceBlockingStub = mockk()

    @Bean
    fun companyServiceBlockingStub(): CompanyServiceGrpc.CompanyServiceBlockingStub = mockk()

    @Bean
    fun contractServiceBlockingStub(): ContractServiceGrpc.ContractServiceBlockingStub = mockk()

    @Bean
    fun contractOffboardingServiceStub(): ContractOffboardingServiceGrpcKt.ContractOffboardingServiceCoroutineStub =
        mockk()

    @Bean
    fun managerServiceBlockingStub(): ManagerServiceGrpc.ManagerServiceBlockingStub = mockk()

    @Bean
    fun departmentServiceBlockingStub(): DepartmentServiceGrpc.DepartmentServiceBlockingStub = mockk()

    @Bean
    fun pigeonNotificationClient(): PigeonNotificationClient = mockk()

    @Bean
    fun countryServiceBlockingStub(): CountryServiceGrpc.CountryServiceBlockingStub = mockk()
}
