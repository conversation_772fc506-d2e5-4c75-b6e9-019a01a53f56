syntax = "proto3";

package com.multiplier.compensation.grpc.schema;

import "com/multiplier/compensation/grpc/schema/compensation/compensation_data.proto";
import "com/multiplier/compensation/grpc/schema/compensation/compensation_input.proto";
import "com/multiplier/compensation/grpc/schema/compensation/compensation_item.proto";
import "com/multiplier/compensation/grpc/schema/compensation/compensation_log.proto";

option java_multiple_files = true;

service CompensationService {
  // Compensation Log
  rpc GetCompensationLogs(CompensationLogsFilter) returns (CompensationLogsResponse);

  // Compensation Item
  rpc GetCompensationItems(CompensationItemsFilter) returns (CompensationItemsResponse);

  // Compensation Records
  rpc GetCompensationRecords(CompensationRecordsFilter) returns (CompensationRecordsResponse);
  
  // Compensation Input
  rpc GetCompensationInputs(CompensationInputsFilter) returns (CompensationInputsResponse);

  // Compensation Input Watermaking
  rpc GetCompensationWatermarkingInputs(CompensationWatermarkingInputsFilter) returns (CompensationInputsResponse);

  // Bulk Update compensation status
  rpc BulkUpdateCompensationStatus(UpdateCompensationStatusBulkRequest) returns (UpdateCompensationStatusBulkResponse);
}
