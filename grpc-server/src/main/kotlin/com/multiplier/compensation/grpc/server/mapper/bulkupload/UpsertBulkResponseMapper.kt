package com.multiplier.compensation.grpc.server.mapper.bulkupload

import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.RowValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationBulkCreateResponse
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaBulkUpsertResponse
import com.multiplier.compensation.service.payschedule.dto.PayScheduleBulkCreationOutput
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertResponse
import com.multiplier.grpc.common.bulkupload.v1.upsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.upsertResponse
import com.multiplier.grpc.common.v1.toGrpc
import java.util.UUID

fun PayScheduleBulkCreationOutput.toGrpcUpsertBulkResponse(request: UpsertBulkRequest) = toGrpcUpsertBulkResponse(
    request = request,
    rowValidationResults = rowValidationResults,
)

fun CompensationSchemaBulkUpsertResponse.toGrpcUpsertBulkResponse(request: UpsertBulkRequest) =
    toGrpcUpsertBulkResponse(
        request = request,
        rowValidationResults = rowValidationResults,
    )

fun CompensationBulkCreateResponse.toGrpcUpsertBulkResponse(request: UpsertBulkRequest) = toGrpcUpsertBulkResponse(
    request = request,
    rowValidationResults = rowValidationResults,
    requestId = requestId,
)

private fun toGrpcUpsertBulkResponse(
    request: UpsertBulkRequest,
    rowValidationResults: List<RowValidationResult>,
    requestId: String? = null,
) = upsertBulkResponse {
    val validationErrorsPerRow = rowValidationResults.filter {
        it.cellValidationResults.any { result ->
            result.type == ValidationResultType.WARN || result.type == ValidationResultType.ERROR
        }
    }.associate { it.id to it.cellValidationResults }

    // Filter out extrapolated inputs from validation errors
    val filteredValidationErrors = filterValidationErrorsFromExtrapolatedInputs(validationErrorsPerRow)

    // Filter out extrapolated inputs from request inputs
    val filteredInputs = filterExtrapolatedInputs(request.inputsList) { it.inputId }

    val grpcUpsertResponses = filteredInputs.map { upsertRequest ->
        toGrpcUpsertResponse(upsertRequest.inputId, filteredValidationErrors)
    }

    this.results.addAll(grpcUpsertResponses)

    requestId?.let {
        this.id = UUID.fromString(requestId).toGrpc()
    }
}

private fun toGrpcUpsertResponse(
    inputId: String,
    validationErrorsPerRow: Map<String, List<CellValidationResult>>,
): UpsertResponse = upsertResponse {
    this.inputId = inputId
    this.success = validationErrorsPerRow.getOrDefault(inputId, emptyList()).isEmpty()
    validationErrorsPerRow[inputId]?.let { cellValidationResults ->
        this.errors.addAll(cellValidationResults.map { it.message })
    }
}
