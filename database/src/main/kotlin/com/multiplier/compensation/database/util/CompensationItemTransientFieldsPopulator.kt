package com.multiplier.compensation.database.util

import com.multiplier.compensation.domain.compensationitem.CompensationItemEnriched
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemState
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import java.time.LocalDate

fun CompensationItemEnriched.calculateState(): CompensationItemState {
    if (status in setOf(CompensationItemStatus.REVOKED, CompensationItemStatus.ABORTED, CompensationItemStatus.DRAFT)) {
        return CompensationItemState.valueOf(status.name)
    }

    val cutOffDate = cutOffDate ?: return CompensationItemState.PENDING

    val today = LocalDate.now()

    val isBeforeOrOnCutOff = !today.isAfter(cutOffDate)
    val isBeforeOrOnPayDate = payDate == null || !today.isAfter(payDate)
    val isAfterPayDate = payDate != null && today.isAfter(payDate)

    return when {
        isBeforeOrOnCutOff -> CompensationItemState.PENDING
        !isBeforeOrOnCutOff && isBeforeOrOnPayDate -> CompensationItemState.PROCESSING
        isAfterPayDate -> CompensationItemState.COMPLETED
        else -> CompensationItemState.valueOf(status.name) // Won't reach. But added as a fallback mechanism.
    }
}

fun CompensationItemEnriched.calculateIsDeletable(): Boolean {
    if (status in setOf(CompensationItemStatus.REVOKED, CompensationItemStatus.ABORTED)) {
        return false
    }

    return !(cutOffDate?.isBefore(LocalDate.now()) ?: false)
}

fun CompensationItemEnriched.calculateIsEditable(): Boolean = !this.isFixed && this.isDeletable
