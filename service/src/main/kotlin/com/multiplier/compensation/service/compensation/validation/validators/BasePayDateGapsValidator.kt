package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import org.springframework.stereotype.Component

@Component
class BasePayDateGapsValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        draft.contractId ?: return false
        if (draft.schemaCategory != CategoryConstants.CATEGORY_CONTRACT_BASE_PAY) return true

        // Note: Only one base pay component per contractId is allowed in the input.
        val basePayRecordFromDraft = draft
        val basePayRecordsFromContext = getBasePayFromContextForCurrentDraft(context, draft)

        if (basePayRecordsFromContext.isEmpty()) return true

        val sortedBasePayRecordsFromContext = basePayRecordsFromContext.sortedBy { it.startDate }

        return when {
            // If the revision leads to an inline update of the existing record
            isAnInlineUpdate(sortedBasePayRecordsFromContext, basePayRecordFromDraft) -> isValidInlineUpdate(
                sortedBasePayRecordsFromContext,
                basePayRecordFromDraft,
                input,
                collector,
            )
            // If the billing rate is revised, then the system treats it as a new base pay record.
            // Its contiguity is checked with the latest base pay record from the context.
            else -> isValidNewRecordCreation(
                sortedBasePayRecordsFromContext.last(),
                basePayRecordFromDraft,
                input,
                collector,
            )
        }
    }

    private fun isAnInlineUpdate(
        sortedBasePayComponentsFromContext: List<CompensationDraft>,
        basePayRecordFromDraft: CompensationDraft,
    ): Boolean = sortedBasePayComponentsFromContext.last().billingRate == basePayRecordFromDraft.billingRate

    private fun isValidInlineUpdate(
        sortedBasePayRecordsFromContext: List<CompensationDraft>,
        basePayRecordFromDraft: CompensationDraft,
        input: ValidationInputItem,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        if (!doesInlineUpdateMaintainContiguity(sortedBasePayRecordsFromContext, basePayRecordFromDraft)) {
            addValidationResult(
                input = input,
                field = CompensationSkeletonField.COMPONENT_NAME,
                resultType = ValidationResultType.ERROR,
                message = "Base Pay components are not contiguous",
                collector = collector,
            )
            return false
        }
        return true
    }

    private fun doesInlineUpdateMaintainContiguity(
        sortedBasePayComponents: List<CompensationDraft>,
        draft: CompensationDraft,
    ): Boolean {
        // Only one base pay record exists, so no need to check contiguity
        if (sortedBasePayComponents.size == 1) return true

        // Since the latest base pay component is updated, so the contiguity check happens between the second-latest base
        // pay record (previous-record) from the context and the revised bay pay record from the draft.
        val previousRecord = sortedBasePayComponents[sortedBasePayComponents.size - 2]
        validatePreviousRecord(previousRecord)

        return previousRecord.endDate?.plusDays(1) == draft.startDate
    }

    // Only the latest base pay record can have a null end date.
    private fun validatePreviousRecord(previousRecord: CompensationDraft) {
        requireCondition(
            condition = previousRecord.endDate != null,
            errorCode = ValidationErrorCode.MissingEndDate,
            message = "End Date is missing in second latest BasePay Record",
        )
    }

    private fun isValidNewRecordCreation(
        latestBasePayRecord: CompensationDraft,
        basePayRecordFromDraft: CompensationDraft,
        input: ValidationInputItem,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        if (!doesNewRecordMaintainContiguity(latestBasePayRecord, basePayRecordFromDraft)) {
            addValidationResult(
                input = input,
                field = CompensationSkeletonField.COMPONENT_NAME,
                resultType = ValidationResultType.ERROR,
                message = "Base Pay components are not contiguous",
                collector = collector,
            )
            return false
        }
        return true
    }

    private fun doesNewRecordMaintainContiguity(
        latestBasePayRecord: CompensationDraft,
        newBasePayRecord: CompensationDraft,
    ): Boolean = doesNewRecordOverlapWithLatestRecord(latestBasePayRecord, newBasePayRecord) ||
        isContiguousWithoutOverlap(latestBasePayRecord, newBasePayRecord)

    private fun doesNewRecordOverlapWithLatestRecord(
        latestBasePayRecord: CompensationDraft,
        newBasePayRecord: CompensationDraft,
    ): Boolean {
        val (latestRecordStartDate, latestRecordEndDate) = latestBasePayRecord.startDate to latestBasePayRecord.endDate
        val newRecordStartDate = newBasePayRecord.startDate

        return newRecordStartDate != null &&
            newRecordStartDate >= latestRecordStartDate &&
            (latestRecordEndDate == null || newRecordStartDate <= latestRecordEndDate)
    }

    private fun isContiguousWithoutOverlap(
        latestBasePayRecord: CompensationDraft,
        newBasePayRecord: CompensationDraft,
    ): Boolean = latestBasePayRecord.endDate != null &&
        latestBasePayRecord.endDate.plusDays(1) == newBasePayRecord.startDate
}
