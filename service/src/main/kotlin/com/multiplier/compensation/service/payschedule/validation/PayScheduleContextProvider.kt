package com.multiplier.compensation.service.payschedule.validation

import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.payschedule.dto.PayScheduleValidatorContext
import com.multiplier.compensation.service.skeleton.SkeletonService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class PayScheduleContextProvider(
    private val skeletonService: SkeletonService,
) {
    fun getValidationContext(
        entityId: Long,
        customParams: Map<String, String>,
    ): PayScheduleValidatorContext {
        log.info { "Get Pay schedule context for entity [$entityId]" }

        return PayScheduleValidatorContext(
            entityId = entityId,
            skeleton = getSkeleton(entityId, customParams),
        )
    }

    private fun getSkeleton(
        entityId: Long,
        customParams: Map<String, String>,
    ): Skeleton {
        log.info { "Get [${SkeletonType.PAY_SCHEDULE}] skeleton for entity [$entityId]" }
        return skeletonService.getSkeleton(
            entityId = entityId,
            skeletonType = SkeletonType.PAY_SCHEDULE,
            customParams = customParams,
        )
    }
}
