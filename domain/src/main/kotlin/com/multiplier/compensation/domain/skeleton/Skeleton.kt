package com.multiplier.compensation.domain.skeleton

import com.multiplier.compensation.domain.skeleton.enums.SkeletonKey
import com.multiplier.compensation.domain.skeleton.enums.ValueType

data class Skeleton(
    val keys: List<SkeletonKey>,
    val data: List<SkeletonData>,
)

data class SkeletonData(
    val fieldId: String,
    val fieldName: String,
    val valueType: ValueType,
    val description: String,
    val mandatory: Boolean,
    val possibleValues: List<String>?,
    val validationRegex: ValidationRegex?,
    val defaultValue: String?,
)

data class ValidationRegex(
    val regex: String,
    val errorMessage: String,
)
