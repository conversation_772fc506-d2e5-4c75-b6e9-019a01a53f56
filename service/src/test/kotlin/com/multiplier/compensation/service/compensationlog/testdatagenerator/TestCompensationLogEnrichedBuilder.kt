package com.multiplier.compensation.service.compensationlog.testdatagenerator

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationlog.CompensationLogEnriched
import java.time.LocalDate
import java.util.UUID

class TestCompensationLogEnrichedBuilder {
    private companion object TestData {
        val DEFAULT_BILLING_RATE_TYPE = BillingRateType.VALUE
        val DEFAULT_BILLING_FREQUENCY = BillingFrequency.MONTHLY
        val DEFAULT_START_DATE: LocalDate = LocalDate.of(2024, 1, 1)
        val DEFAULT_END_DATE: LocalDate = LocalDate.of(2024, 12, 31)
        val DEFAULT_NO_OF_INSTALLMENTS: Int? = null
        val DEFAULT_REQUEST_TYPE = RequestType.COMPENSATION_SETUP

        const val DEFAULT_PAY_SCHEDULE_NAME = "Monthly-05"
        const val DEFAULT_COMPENSATION_NAME = "Performance Bonus"
        const val DEFAULT_COMPENSATION_CATEGORY = "Incentive"
        const val DEFAULT_IS_TAXABLE = true
        const val DEFAULT_IS_FIXED = false
        const val DEFAULT_IS_PRORATED = true
        const val DEFAULT_IS_MANDATORY = false
        const val DEFAULT_IS_PART_OF_BASE_PAY = false
        const val DEFAULT_ENTITY_ID = 123L
        const val DEFAULT_CONTRACT_ID = 456L
        const val DEFAULT_CATEGORY = "Bonus"
        const val DEFAULT_CURRENCY = "USD"
        const val DEFAULT_IS_INSTALLMENT = false
        const val DEFAULT_REQUEST_ID = "REQ-12345"
    }

    private var category: String = DEFAULT_CATEGORY
    private var billingRateType: BillingRateType = DEFAULT_BILLING_RATE_TYPE
    private var billingRate: Double = 50.0
    private var billingFrequency: BillingFrequency = DEFAULT_BILLING_FREQUENCY
    private var startDate: LocalDate = DEFAULT_START_DATE
    private var endDate: LocalDate = DEFAULT_END_DATE
    private var isInstallment: Boolean = DEFAULT_IS_INSTALLMENT
    private var noOfInstallments: Int? = DEFAULT_NO_OF_INSTALLMENTS
    private var requestType: RequestType = DEFAULT_REQUEST_TYPE
    private val requestId: String = DEFAULT_REQUEST_ID
    private var payScheduleName: String = DEFAULT_PAY_SCHEDULE_NAME
    private var compensationName: String = DEFAULT_COMPENSATION_NAME
    private var compensationCategory: String = DEFAULT_COMPENSATION_CATEGORY
    private var isTaxable: Boolean = DEFAULT_IS_TAXABLE
    private var isFixed: Boolean = DEFAULT_IS_FIXED
    private var isProrated: Boolean = DEFAULT_IS_PRORATED
    private var isMandatory: Boolean = DEFAULT_IS_MANDATORY
    private var isPartOfBasePay: Boolean = DEFAULT_IS_PART_OF_BASE_PAY

    fun withCategory(category: String) = apply { this.category = category }

    fun withBillingRateType(billingRateType: BillingRateType) = apply { this.billingRateType = billingRateType }

    fun withBillingRate(billingRate: Double) = apply { this.billingRate = billingRate }

    fun withBillingFrequency(billingFrequency: BillingFrequency) = apply { this.billingFrequency = billingFrequency }

    fun withStartDate(startDate: LocalDate) = apply { this.startDate = startDate }

    fun withEndDate(endDate: LocalDate) = apply { this.endDate = endDate }

    fun withIsInstallment(isInstallment: Boolean) = apply { this.isInstallment = isInstallment }

    fun withNoOfInstallments(noOfInstallments: Int?) = apply { this.noOfInstallments = noOfInstallments }

    fun withRequestType(requestType: RequestType) = apply { this.requestType = requestType }

    fun withPayScheduleName(payScheduleName: String) = apply { this.payScheduleName = payScheduleName }

    fun withCompensationName(compensationName: String) = apply { this.compensationName = compensationName }

    fun withCompensationCategory(compensationCategory: String) = apply {
        this.compensationCategory =
            compensationCategory
    }

    fun withIsTaxable(isTaxable: Boolean) = apply { this.isTaxable = isTaxable }

    fun withIsFixed(isFixed: Boolean) = apply { this.isFixed = isFixed }

    fun withIsProrated(isProrated: Boolean) = apply { this.isProrated = isProrated }

    fun withIsMandatory(isMandatory: Boolean) = apply { this.isMandatory = isMandatory }

    fun withIsPartOfBasePay(isPartOfBasePay: Boolean) = apply { this.isPartOfBasePay = isPartOfBasePay }

    fun build() = CompensationLogEnriched(
        id = UUID.randomUUID(),
        entityId = DEFAULT_ENTITY_ID,
        contractId = DEFAULT_CONTRACT_ID,
        category = category,
        currency = DEFAULT_CURRENCY,
        billingRateType = billingRateType,
        billingRate = billingRate,
        billingFrequency = billingFrequency,
        startDate = startDate,
        endDate = endDate,
        isInstallment = isInstallment,
        noOfInstallments = noOfInstallments,
        requestType = DEFAULT_REQUEST_TYPE,
        requestId = requestId,
        payScheduleName = payScheduleName,
        compensationName = compensationName,
        compensationCategory = compensationCategory,
        isTaxable = isTaxable,
        isFixed = isFixed,
        isProrated = isProrated,
        isMandatory = isMandatory,
        isPartOfBasePay = isPartOfBasePay,
    )
}
