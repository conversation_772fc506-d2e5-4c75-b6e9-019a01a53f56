package com.multiplier.compensation.service.payschedule.mapper

import com.multiplier.compensation.service.common.enums.skeletionfield.SkeletonField

enum class PayScheduleSkeletonField(
    override val id: String,
) : Skeleton<PERSON>ield {
    ID("ID"),
    COMPANY_ID("COMPANY_ID"),
    ENTITY_ID("ENTITY_ID"),
    PAY_SCHEDULE_NAME("PAY_SCHEDULE_NAME"),
    FREQUENCY("FREQUENCY"),
    START_DATE_REFERENCE("START_DATE_REFERENCE"),
    END_DATE_REFERENCE("END_DATE_REFERENCE"),
    RELATIVE_PAY_DAYS("RELATIVE_PAY_DAYS"),
    PAY_DATE_REFERENCE_TYPE("PAY_DATE_REFERENCE_TYPE"),
    IS_INSTALLMENT("IS_INSTALLMENT"),
    IS_ACTIVE("IS_ACTIVE"),
    LABEL("<PERSON><PERSON>L"),
}
