package com.multiplier.compensation.service.compensation.extrapolation

import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.extrapolation.utils.FormulaRequest
import com.multiplier.compensation.service.compensation.extrapolation.utils.evaluateFormula
import com.multiplier.compensation.service.compensation.extrapolation.utils.parseFixedAmount
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.math.BigDecimal

private val logger = KotlinLogging.logger {}

@Service
class CompensationDerivationService {
    /**
     * Calculates compensation drafts for FIXED and CALCULATED schema item types based on INPUT drafts.
     * Each input draft is processed individually to support formula evaluation on each input row separately,
     * allowing multiple drafts with the same contractId and employeeId to be handled correctly.
     * This ensures that formulas are evaluated with the correct context for each individual input row.
     *
     * @param inputDrafts List of compensation drafts for INPUT type schema items
     * @param allSchemaItems All schema items for the schema (INPUT, FIXED, CALCULATED)
     * @return List of calculated compensation drafts for FIXED and CALCULATED items
     */
    fun deriveCompensations(
        inputDrafts: List<CompensationDraft>,
        allSchemaItems: List<CompensationSchemaItem>,
    ): List<CompensationDraft> {
        logger.info {
            "Calculating derived compensations for ${inputDrafts.size} input drafts and ${allSchemaItems.size} schema items"
        }

        if (inputDrafts.isEmpty()) {
            logger.info { "No input drafts provided, returning empty list" }
            return emptyList()
        }

        val derivedDrafts = mutableListOf<CompensationDraft>()

        // Process each input draft individually to support formula evaluation on each input row separately
        inputDrafts.forEach { inputDraft ->
            logger.debug {
                "Processing individual input draft for contract [${inputDraft.contractId}], " +
                    "employee [${inputDraft.employeeId}], schema item [${inputDraft.schemaItemId}]"
            }

            if (
                inputDraft.schemaCategory != CategoryConstants.CATEGORY_CONTRACT_BASE_PAY &&
                inputDraft.schemaCategory != CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY
            ) {
                logger.info {
                    "Skipping input draft for contract [${inputDraft.contractId}] employee [${inputDraft.employeeId}]" +
                        " schema item [${inputDraft.schemaItemId}] as it is not a base pay category"
                }
                return@forEach
            }

            // Create variable map for calculations using only this input draft
            val variableMap = createVariableMap(listOf(inputDraft), allSchemaItems)

            // Generate FIXED compensation drafts using this input draft as template
            val fixedDrafts = generateFixedCompensationDrafts(allSchemaItems, listOf(inputDraft))
            derivedDrafts.addAll(fixedDrafts)

            // Generate CALCULATED compensation drafts using this input draft's values
            val calculatedDrafts =
                generateCalculatedCompensationDrafts(allSchemaItems, listOf(inputDraft), variableMap)
            derivedDrafts.addAll(calculatedDrafts)
        }

        logger.info { "Generated [${derivedDrafts.size}] derived compensation drafts" }
        return derivedDrafts
    }

    /**
     * Generates FIXED compensation drafts for all FIXED schema items
     */
    private fun generateFixedCompensationDrafts(
        allSchemaItems: List<CompensationSchemaItem>,
        contractInputDrafts: List<CompensationDraft>,
    ): List<CompensationDraft> {
        val fixedItems = allSchemaItems.filter { it.itemType == ItemType.FIXED }
        logger.info { "Generating [${fixedItems.size}] FIXED compensation drafts" }

        return fixedItems.mapNotNull { fixedItem ->
            val templateDraft = findTemplateForFixedItem(contractInputDrafts)
            if (templateDraft != null) {
                createFixedCompensationDraft(fixedItem, templateDraft)
            } else {
                logger.info {
                    "Skipping FIXED item [${fixedItem.componentName}] as no suitable template found."
                }
                null
            }
        }
    }

    /**
     * Generates CALCULATED compensation drafts for relevant CALCULATED schema items
     */
    private fun generateCalculatedCompensationDrafts(
        allSchemaItems: List<CompensationSchemaItem>,
        contractInputDrafts: List<CompensationDraft>,
        variableMap: Map<String, BigDecimal>,
    ): List<CompensationDraft> {
        val calculatedItems = allSchemaItems.filter { it.itemType == ItemType.CALCULATED }
        logger.info { "Generating CALCULATED compensation drafts for [${calculatedItems.size}] items" }

        return calculatedItems.mapNotNull { calculatedItem ->
            if (calculationReferencesInputComponents(calculatedItem, variableMap)) {
                val templateDraft = findTemplateForCalculatedItem(calculatedItem, contractInputDrafts, allSchemaItems)
                if (templateDraft != null) {
                    createCalculatedCompensationDraft(calculatedItem, templateDraft, variableMap)
                } else {
                    logger.info {
                        "Skipping CALCULATED item [${calculatedItem.componentName}] as no suitable template found."
                    }
                    null
                }
            } else {
                logger.warn {
                    "Skipping CALCULATED item '${calculatedItem.componentName}' as it doesn't reference any input components"
                }
                null
            }
        }
    }

    /**
     * Finds template draft for FIXED items - looks for TOTAL_COST_TO_COMPANY first, then CONTRACT_BASE_PAY
     */
    private fun findTemplateForFixedItem(contractInputDrafts: List<CompensationDraft>): CompensationDraft? {
        // First check for TOTAL_COST_TO_COMPANY
        val totalCostDraft = contractInputDrafts.find { draft ->
            draft.schemaCategory == CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY
        }

        // If not found, check for CONTRACT_BASE_PAY
        return totalCostDraft ?: contractInputDrafts.find { draft ->
            draft.schemaCategory == CategoryConstants.CATEGORY_CONTRACT_BASE_PAY
        }
    }

    /**
     * Finds template draft for CALCULATED items - looks for component referenced in formula
     */
    private fun findTemplateForCalculatedItem(
        calculatedItem: CompensationSchemaItem,
        contractInputDrafts: List<CompensationDraft>,
        allSchemaItems: List<CompensationSchemaItem>,
    ): CompensationDraft? {
        val formula = calculatedItem.calculation
        if (formula.isNullOrBlank()) return null

        // Find the component name referenced in the formula
        val referencedComponent = allSchemaItems.find { schemaItem ->
            formula.contains(schemaItem.componentName)
        }

        return if (referencedComponent != null) {
            contractInputDrafts.find { draft ->
                draft.schemaItemId == referencedComponent.id
            }
        } else {
            null
        }
    }

    /**
     * Creates a variable map for formula evaluation from input drafts
     */
    private fun createVariableMap(
        inputDrafts: List<CompensationDraft>,
        allSchemaItems: List<CompensationSchemaItem>,
    ): Map<String, BigDecimal> {
        val schemaItemsById = allSchemaItems.associateBy { it.id }

        return inputDrafts.mapNotNull { draft ->
            val schemaItem = schemaItemsById[draft.schemaItemId]
            if (schemaItem != null && draft.billingRate != null) {
                schemaItem.componentName to BigDecimal.valueOf(draft.billingRate)
            } else {
                logger.warn { "Could not create variable for draft with schemaItemId ${draft.schemaItemId}" }
                null
            }
        }.toMap()
    }

    /**
     * Creates a compensation draft for a FIXED item type
     */
    private fun createFixedCompensationDraft(
        fixedSchemaItem: CompensationSchemaItem,
        templateDraft: CompensationDraft,
    ): CompensationDraft {
        logger.debug { "Creating FIXED compensation draft for item [${fixedSchemaItem.componentName}]" }

        val fixedAmount = parseFixedAmount(
            fixedSchemaItem.calculation,
            fixedSchemaItem.componentName,
        )

        return templateDraft.copy(
            schemaItemId = fixedSchemaItem.id,
            schemaCategory = fixedSchemaItem.category,
            billingRateType = fixedSchemaItem.billingRateType,
            billingRate = fixedAmount,
            billingFrequency = fixedSchemaItem.billingFrequency,
            payScheduleId = fixedSchemaItem.payScheduleId,
            currency = fixedSchemaItem.currency,
        )
    }

    /**
     * Creates a compensation draft for a CALCULATED item type using formula evaluation
     */
    private fun createCalculatedCompensationDraft(
        calculatedItem: CompensationSchemaItem,
        templateDraft: CompensationDraft,
        variableMap: Map<String, BigDecimal>,
    ): CompensationDraft {
        logger.debug { "Creating CALCULATED compensation draft for item ${calculatedItem.componentName}" }

        val calculation = calculatedItem.calculation
        val calculatedAmount = if (calculation.isNullOrBlank()) {
            throw InvalidArgumentException(
                ValidationErrorCode.InvalidFixedCalculatedInputs,
                "No calculation formula found for item [${calculatedItem.componentName}]",
            )
        } else {
            evaluateFormula(FormulaRequest(calculation, variableMap))
        }

        return templateDraft.copy(
            schemaItemId = calculatedItem.id,
            schemaCategory = calculatedItem.category,
            billingRateType = calculatedItem.billingRateType,
            billingRate = calculatedAmount,
            billingFrequency = templateDraft.billingFrequency,
            payScheduleId = templateDraft.payScheduleId,
            currency = templateDraft.currency,
        )
    }

    /**
     * Checks if a calculation formula references any of the input components
     */
    private fun calculationReferencesInputComponents(
        calculatedItem: CompensationSchemaItem,
        variableMap: Map<String, BigDecimal>,
    ): Boolean {
        val calculation = calculatedItem.calculation
        if (calculation.isNullOrBlank()) {
            logger.debug { "No calculation formula for item ${calculatedItem.componentName}" }
            return false
        }

        // Check if any of the input component names are referenced in the formula
        val inputComponentNames = variableMap.keys
        val referencesInputComponents = inputComponentNames.any { componentName ->
            // Check if the component name appears as a variable in the formula
            // This is a simple string contains check - could be made more sophisticated
            calculation.contains(componentName)
        }

        logger.debug {
            "Calculation '$calculation' for item '${calculatedItem.componentName}' references input components $referencesInputComponents"
        }
        return referencesInputComponents
    }
}

/**
 * Retrieves the parent input row for a derived input row.
 *
 * Derived input rows have IDs in format: "derived_<parentId>_<index>"
 * Returns the input unchanged if it's not a derived row.
 *
 * @param input The input row to resolve
 * @param allInputs Map of all input rows keyed by their IDs
 * @return The parent input row if derived, or the input itself if not derived
 * @throws InvalidArgumentException if the parent row is not found
 */
fun getParentInputRowForDerivedInputRow(
    input: ValidationInputItem,
    allInputs: Map<String, ValidationInputItem>,
): ValidationInputItem {
    if (input.id.startsWith(DERIVED_INPUT_ROW_ID_PREFIX)) {
        val originalInputId = extractParentInputRowIdFromDerivedRowId(input.id)
        return allInputs[originalInputId] ?: throw InvalidArgumentException(
            errorCode = ValidationErrorCode.InvalidDerivedInputRow,
            "Original input row not found for derived input row [${input.id}]",
        )
    }
    return input
}

/**
 * Retrieves all derived input rows created from a given parent input row.
 *
 * Searches for input rows with IDs matching pattern: "derived_<parentId>_*"
 *
 * @param input The parent input row to find derived rows for
 * @param allInputs Map of all input rows keyed by their IDs
 * @return List of derived input rows, or empty list if none exist
 */
fun getAllDerivedRowsForParentInputRow(
    input: ValidationInputItem,
    allInputs: Map<String, ValidationInputItem>,
): List<ValidationInputItem> {
    val derivedInputRowIdPrefix = "$DERIVED_INPUT_ROW_ID_PREFIX${input.id}_"
    return allInputs.values.filter { it.id.startsWith(derivedInputRowIdPrefix) }
}

/**
 * Extracts the parent input row ID from a derived input row ID.
 *
 * Parses "derived_<parentId>_<index>" format to extract the parent ID.
 * Returns the input unchanged if it's not a derived ID.
 *
 * @param derivedInputId The input ID to parse
 * @return The parent input row ID, or the input unchanged if not derived
 */
private fun extractParentInputRowIdFromDerivedRowId(derivedInputId: String): String {
    if (!derivedInputId.startsWith(DERIVED_INPUT_ROW_ID_PREFIX)) {
        return derivedInputId
    }

    // Remove the prefix to get the remaining part: "sourceItemId_index"
    val withoutPrefix = derivedInputId.removePrefix(DERIVED_INPUT_ROW_ID_PREFIX)

    // Find the last underscore to separate sourceItemId from index
    val lastUnderscoreIndex = withoutPrefix.lastIndexOf("_")

    return if (lastUnderscoreIndex > 0) {
        // Extract everything before the last underscore as the parent ID
        withoutPrefix.substring(0, lastUnderscoreIndex)
    } else {
        // Fallback: if no underscore found, return the whole remaining part
        // This handles edge cases where the format might be unexpected
        withoutPrefix
    }
}
