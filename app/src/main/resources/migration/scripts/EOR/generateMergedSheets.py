#!/usr/bin/env python3
"""
Compensation Excel File Merger Script

PURPOSE:
    This script merges compensation Excel files from multiple entities within each country
    into consolidated files. It processes both COMPENSATION_BACKFILL and
    COMPENSATION_REVISION_BACKFILL files while preserving header formatting.

USAGE:
    python3 generateMergedSheets.py --input <input_directory> --output <output_directory>

ARGUMENTS:
    --input     Path to the input directory containing country folders
    --output    Path to the output directory where merged files will be saved

EXAMPLE:
    python3 generateMergedSheets.py --input /path/to/input --output /path/to/output
"""

import os
import pandas as pd
import argparse
from openpyxl import load_workbook
from openpyxl.styles import Font

def apply_header_format(source_path, target_path, rows=3):
    source_wb = load_workbook(source_path)
    source_ws = source_wb.active

    target_wb = load_workbook(target_path)
    target_ws = target_wb.active

    for row in range(1, rows + 1):
        for col in range(1, source_ws.max_column + 1):
            source_cell = source_ws.cell(row=row, column=col)
            target_cell = target_ws.cell(row=row, column=col)
            target_cell.font = Font(
                bold=source_cell.font.bold,
                italic=source_cell.font.italic,
                name=source_cell.font.name,
                size=source_cell.font.size
            )

    target_wb.save(target_path)

def main(input_dir, output_dir):
    for country_folder in os.listdir(input_dir):
        country_path = os.path.join(input_dir, country_folder)
        if not os.path.isdir(country_path):
            continue

        merged_backfill_data = []
        merged_revision_data = []
        backfill_header = None
        revision_header = None
        backfill_header_path = None
        revision_header_path = None

        for entity_folder in os.listdir(country_path):
            entity_path = os.path.join(country_path, entity_folder)
            if not os.path.isdir(entity_path):
                continue

            backfill_file = next(
                (f for f in os.listdir(entity_path)
                 if "COMPENSATION_BACKFILL_" in f and f.endswith(".xlsx")),
                None
            )

            revision_file = next(
                (f for f in os.listdir(entity_path)
                 if "COMPENSATION_REVISION_BACKFILL_" in f and f.endswith(".xlsx")),
                None
            )

            if backfill_file:
                backfill_path = os.path.join(entity_path, backfill_file)
                try:
                    if backfill_header is None:
                        backfill_header = pd.read_excel(backfill_path, header=None, nrows=3)
                        backfill_header_path = backfill_path
                    df = pd.read_excel(backfill_path, header=None, skiprows=3)
                    df = df.iloc[:, 1:]
                    merged_backfill_data.append(df)
                except Exception as e:
                    print(f"Error reading {backfill_path}: {e}")

            if revision_file:
                revision_path = os.path.join(entity_path, revision_file)
                try:
                    if revision_header is None:
                        revision_header = pd.read_excel(revision_path, header=None, nrows=3)
                        revision_header_path = revision_path
                    df = pd.read_excel(revision_path, header=None, skiprows=3)
                    df = df.iloc[:, 1:]
                    merged_revision_data.append(df)
                except Exception as e:
                    print(f"Error reading {revision_path}: {e}")

        # Write merged files inside the output/country folder
        output_country_path = os.path.join(output_dir, country_folder)
        os.makedirs(output_country_path, exist_ok=True)

        if backfill_header is not None and merged_backfill_data:
            output_backfill = pd.concat(merged_backfill_data, ignore_index=True)
            output_path = os.path.join(output_country_path, "COMPENSATION_BACKFILL.xlsx")
            with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
                backfill_header.to_excel(writer, index=False, header=False)
                output_backfill.to_excel(writer, index=False, header=False, startrow=3, startcol=1)
            apply_header_format(backfill_header_path, output_path)

        if revision_header is not None and merged_revision_data:
            output_revision = pd.concat(merged_revision_data, ignore_index=True)
            output_path = os.path.join(output_country_path, "COMPENSATION_REVISION_BACKFILL.xlsx")
            with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
                revision_header.to_excel(writer, index=False, header=False)
                output_revision.to_excel(writer, index=False, header=False, startrow=3, startcol=1)
            apply_header_format(revision_header_path, output_path)

        print(f"Merged files created for country: {country_folder}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Merge Compensation Excel files.")
    parser.add_argument("--input", required=True, help="Path to input directory")
    parser.add_argument("--output", required=True, help="Path to output directory")
    args = parser.parse_args()

    main(args.input, args.output)
