package com.multiplier.compensation.service.compensationschema.mapper

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaRequest
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class CompensationSchemaRequestTest {
    @Test
    fun `should create request with all fields`() {
        val request = CompensationSchemaRequest(
            entityId = 123L,
            offeringType = OfferingType.EOR,
            country = CountryCode.IND,
            returnDefaultOnly = true,
        )

        assertEquals(123L, request.entityId)
        assertEquals(OfferingType.EOR, request.offeringType)
        assertEquals(CountryCode.IND, request.country)
    }

    @Test
    fun `should allow null country`() {
        val request = CompensationSchemaRequest(
            entityId = 456L,
            offeringType = OfferingType.GLOBAL_PAYROLL,
            country = null,
            returnDefaultOnly = true,
        )

        assertEquals(456L, request.entityId)
        assertEquals(OfferingType.GLOBAL_PAYROLL, request.offeringType)
        assertNull(request.country)
    }

    @Test
    fun `should support equality and copy`() {
        val original = CompensationSchemaRequest(1L, OfferingType.EOR, CountryCode.USA, true)
        val copy = original.copy()

        assertEquals(original, copy)
        assertEquals(original.hashCode(), copy.hashCode())
    }
}
