@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.multiplier.compensation.service.compensation

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.compensationinput.CompensationInput
import com.multiplier.compensation.domain.compensationschema.SchemaTags
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.RowValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationBulkCreateResponse
import com.multiplier.compensation.service.compensation.dto.CompensationBulkInput
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.UpsertCompensationByMemberRequest
import com.multiplier.compensation.service.compensation.validation.validators.UpsertCompensationByMemberValidator
import com.multiplier.compensation.service.compensationinput.CompensationInputService
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.payschedule.PayScheduleService
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.UUID
import kotlin.test.assertEquals

class UpsertCompensationByMemberServiceTest {
    private lateinit var bulkCompensationCreationService: BulkCompensationCreationService
    private lateinit var compensationInputService: CompensationInputService
    private lateinit var timeBasedEpochGenerator: TimeBasedEpochGenerator
    private lateinit var compensationSchemaService: CompensationSchemaService
    private lateinit var payScheduleService: PayScheduleService
    private lateinit var upsertCompensationByMemberValidator: UpsertCompensationByMemberValidator
    private lateinit var upsertCompensationByMemberService: UpsertCompensationByMemberService

    @BeforeEach
    fun setUp() {
        bulkCompensationCreationService = mockk()
        compensationInputService = mockk()
        timeBasedEpochGenerator = mockk()
        compensationSchemaService = mockk()
        payScheduleService = mockk()
        upsertCompensationByMemberValidator = UpsertCompensationByMemberValidator() // Use real validator

        upsertCompensationByMemberService = UpsertCompensationByMemberService(
            bulkCompensationCreationService,
            compensationInputService,
            timeBasedEpochGenerator,
            compensationSchemaService,
            payScheduleService,
            upsertCompensationByMemberValidator,
        )
    }

    private fun basePayComponentMock(
        category: String = "CONTRACT_BASE_PAY",
        isInstallment: Boolean = false,
        noOfInstallments: Int? = null,
    ): CompensationInput = mockk(relaxed = true) {
        every { <EMAIL> } returns category
        every { <EMAIL> } returns isInstallment
        every { <EMAIL> } returns noOfInstallments
        every { <EMAIL> } returns "Notes for base pay component"
        every { <EMAIL> } returns ReasonCode.SC001
    }

    @Test
    fun `upsertCompensationByMember should return validation errors for invalid country`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "USA"),
            ),
        )
        val basePayComponent = basePayComponentMock()
        val onboardingComponents = listOf(basePayComponent)
        every { compensationInputService.getCompensationInputData(any()) } returns onboardingComponents
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        every { payScheduleService.getPaySchedulesByIds(any()) } returns setOf(mockk(relaxed = true))
        every { compensationSchemaService.getSchemaItemById(any()) } returns mockk(relaxed = true)
        every { compensationSchemaService.getSchemaById(any()) } returns mockk(relaxed = true) {
            every { country } returns mockk { every { name } returns "USA" }
        }
        every { payScheduleService.getPaySchedules(any(), any(), any(), any()) } returns mockk(relaxed = true)
        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)
        // Then
        assertNotNull(result)
        assertEquals(556851L, result.contractId)
        assertEquals(OperationStatus.FAILURE, result.status)
        assertTrue(result.componentValidationResults.isNotEmpty())
        assertTrue(
            result.componentValidationResults.any {
                it.message.contains("CAN") ||
                    it.message.contains("not found")
            },
        )
    }

    @Test
    fun `upsertCompensationByMember should return error when base pay component not found`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )

        every { compensationInputService.getCompensationInputData(any()) } returns emptyList()

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)

        // Then
        assertNotNull(result)
        assertEquals(556851L, result.contractId)
        assertEquals(OperationStatus.FAILURE, result.status)
        assertTrue(result.componentValidationResults.isNotEmpty())
        assertTrue(result.componentValidationResults.any { it.message.contains("CONTRACT_BASE_PAY") })
    }

    @Test
    fun `upsertCompensationByMember should return validation errors for missing required fields`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)

        // Then
        assertNotNull(result)
        assertEquals(556851L, result.contractId)
        assertEquals(OperationStatus.FAILURE, result.status)
        // Should have validation errors for missing COMPONENT_NAME, BILLING_RATE_TYPE, BILLING_RATE
        assertEquals(2, result.componentValidationResults.size)

        val errorMessages = result.componentValidationResults.map { it.message }
        assert(errorMessages.any { it.contains("COMPONENT_NAME") && it.contains("required") })
        assert(errorMessages.any { it.contains("BILLING_RATE_TYPE") && it.contains("required") })
    }

    @Test
    fun `upsertCompensationByMember should handle successful case with mocked dependencies`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )
        val basePayComponent = basePayComponentMock()
        val onboardingComponents = listOf(basePayComponent)
        val bulkResponse = CompensationBulkCreateResponse(
            entityId = 123L,
            status = OperationStatus.SUCCESS,
            requestId = "test-request-id",
            rowValidationResults = emptyList(),
        )
        every { compensationInputService.getCompensationInputData(any()) } returns onboardingComponents
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        every { payScheduleService.getPaySchedulesByIds(any()) } returns setOf(mockk(relaxed = true))
        every { compensationSchemaService.getSchemaItemById(any()) } returns mockk(relaxed = true) {
            every { componentName } returns "Test Component"
            every { category } returns CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION
        }
        every { compensationSchemaService.getSchemaById(any()) } returns mockk(relaxed = true) {
            every { country } returns mockk { every { name } returns "CAN" }
            every { configurationScope } returns ConfigurationScope.COUNTRY
            every { tags } returns listOf(SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.toString())
            every { schemaItems } returns listOf(
                mockk(relaxed = true) {
                    every { componentName } returns "Test Component"
                    every { category } returns "EMPLOYEE_CONTRIBUTION"
                },
            )
        }
        every { payScheduleService.getPaySchedules(any(), any(), any(), any()) } returns mockk(relaxed = true)
        every { bulkCompensationCreationService.execute(any()) } returns bulkResponse
        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)
        // Then
        assertNotNull(result)
        System.out.println("Result: $result")
        assertEquals(556851L, result.contractId)
        assertEquals(OperationStatus.SUCCESS, result.status)
        assertEquals(0, result.componentValidationResults.size)
        verify { compensationInputService.getCompensationInputData(any()) }
        verify { bulkCompensationCreationService.execute(any()) }
    }

    @Test
    fun `validation should catch all required field errors through main method`() {
        // Given
        val requestWithMissingFields = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "CAN"),
                // Missing: COMPONENT_NAME, BILLING_RATE_TYPE, BILLING_RATE
            ),
        )

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(requestWithMissingFields)

        // Then
        assertEquals(OperationStatus.FAILURE, result.status)
        assertEquals(2, result.componentValidationResults.size)

        val errorFields = result.componentValidationResults.map { it.field.key }
        assert(errorFields.contains("COMPONENT_NAME"))
        assert(errorFields.contains("BILLING_RATE_TYPE"))

        result.componentValidationResults.forEach { validationResult ->
            assertEquals(ValidationResultType.ERROR, validationResult.type)
            assert(validationResult.message.contains("required"))
        }
    }

    @Test
    fun `upsertCompensationByMember should handle null values in key value inputs`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", null),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", null),
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)

        // Then
        assertEquals(OperationStatus.FAILURE, result.status)
        assertTrue(result.componentValidationResults.isNotEmpty())
        assertTrue(
            result.componentValidationResults.any {
                it.message.contains("COMPONENT_NAME") &&
                    it.message.contains("required")
            },
        )
    }

    @Test
    fun `upsertCompensationByMember should handle empty string values`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", ""),
                KeyValuePair("BILLING_RATE_TYPE", ""),
                KeyValuePair("BILLING_RATE", ""),
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)

        // Then
        assertEquals(OperationStatus.FAILURE, result.status)
        assertTrue(result.componentValidationResults.isNotEmpty())
        assertTrue(
            result.componentValidationResults.any {
                it.message.contains("COMPONENT_NAME") &&
                    it.message.contains("required")
            },
        )
        assertTrue(
            result.componentValidationResults.any {
                it.message.contains("BILLING_RATE_TYPE") &&
                    it.message.contains("required")
            },
        )
        assertTrue(
            result.componentValidationResults.any {
                it.message.contains("BILLING_RATE") &&
                    it.message.contains("required")
            },
        )
    }

    @Test
    fun `upsertCompensationByMember should handle pay schedule service failures`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("CATEGORY", "EMPLOYEE_CONTRIBUTION"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )
        val basePayComponent = basePayComponentMock()
        val onboardingComponents = listOf(basePayComponent)
        every { compensationInputService.getCompensationInputData(any()) } returns onboardingComponents
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        every { payScheduleService.getPaySchedulesByIds(any()) } throws
            RuntimeException("Pay schedule service unavailable")
        // When & Then
        assertThrows<RuntimeException> {
            upsertCompensationByMemberService.upsertCompensationByMember(request)
        }
    }

    @Test
    fun `upsertCompensationByMember should clear end date for installment components`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )

        // Create base pay component with installment settings
        val basePayComponent = basePayComponentMock(
            isInstallment = true,
            noOfInstallments = 12,
        )
        val onboardingComponents = listOf(basePayComponent)
        val bulkResponse = CompensationBulkCreateResponse(
            entityId = 123L,
            status = OperationStatus.SUCCESS,
            requestId = "test-request-id",
            rowValidationResults = emptyList(),
        )

        every { compensationInputService.getCompensationInputData(any()) } returns onboardingComponents
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        every { payScheduleService.getPaySchedulesByIds(any()) } returns setOf(mockk(relaxed = true))
        every { compensationSchemaService.getSchemaItemById(any()) } returns mockk(relaxed = true) {
            every { componentName } returns "Test Component"
            every { category } returns "EMPLOYEE_CONTRIBUTION"
        }
        every { compensationSchemaService.getSchemaById(any()) } returns mockk(relaxed = true) {
            every { country } returns mockk { every { name } returns "CAN" }
            every { configurationScope } returns ConfigurationScope.COUNTRY
            every { tags } returns listOf(SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.toString())
            every { schemaItems } returns listOf(
                mockk(relaxed = true) {
                    every { componentName } returns "Test Component"
                    every { category } returns "EMPLOYEE_CONTRIBUTION"
                },
            )
        }
        every { payScheduleService.getPaySchedules(any(), any(), any(), any()) } returns emptyList()

        // Capture the bulk input to verify end date is not included for installments
        val capturedBulkInput = slot<CompensationBulkInput>()
        every { bulkCompensationCreationService.execute(capture(capturedBulkInput)) } returns bulkResponse

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)

        // Then
        assertEquals(OperationStatus.SUCCESS, result.status)

        // Verify that the new employee contribution row item does NOT have end date for installments
        val newEmployeeContributionRowItem = capturedBulkInput.captured.rowItems.find { rowItem ->
            rowItem.keyValuePairs.any {
                it.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                    it.value == "Test Component"
            }
        }
        assertNotNull(newEmployeeContributionRowItem)

        val endDatePair = newEmployeeContributionRowItem!!.keyValuePairs.find {
            it.key == CompensationSkeletonField.END_DATE.id
        }
        val isInstallmentPair = newEmployeeContributionRowItem.keyValuePairs.find {
            it.key == CompensationSkeletonField.IS_INSTALLMENT.id
        }

        // For installments, end date should NOT be present
        assertNull(endDatePair, "End date should not be present for installment components")
        assertEquals("Yes", isInstallmentPair?.value, "Component should be marked as installment")
    }

    @Test
    fun `upsertCompensationByMember should include end date for non-installment components`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )

        // Create base pay component without installment settings
        val basePayComponent = basePayComponentMock(
            isInstallment = false,
            noOfInstallments = null,
        )
        val onboardingComponents = listOf(basePayComponent)
        val bulkResponse = CompensationBulkCreateResponse(
            entityId = 123L,
            status = OperationStatus.SUCCESS,
            requestId = "test-request-id",
            rowValidationResults = emptyList(),
        )

        every { compensationInputService.getCompensationInputData(any()) } returns onboardingComponents
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        every { payScheduleService.getPaySchedulesByIds(any()) } returns setOf(mockk(relaxed = true))
        every { compensationSchemaService.getSchemaItemById(any()) } returns mockk(relaxed = true) {
            every { componentName } returns "Test Component"
            every { category } returns "EMPLOYEE_CONTRIBUTION"
        }
        every { compensationSchemaService.getSchemaById(any()) } returns mockk(relaxed = true) {
            every { country } returns mockk { every { name } returns "CAN" }
            every { configurationScope } returns ConfigurationScope.COUNTRY
            every { tags } returns listOf(SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.toString())
            every { schemaItems } returns listOf(
                mockk(relaxed = true) {
                    every { componentName } returns "Test Component"
                    every { category } returns "EMPLOYEE_CONTRIBUTION"
                },
            )
        }
        every { payScheduleService.getPaySchedules(any(), any(), any(), any()) } returns emptyList()

        // Capture the bulk input to verify end date is included for non-installments
        val capturedBulkInput = slot<CompensationBulkInput>()
        every { bulkCompensationCreationService.execute(capture(capturedBulkInput)) } returns bulkResponse

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)

        // Then
        assertEquals(OperationStatus.SUCCESS, result.status)

        // Verify that the new employee contribution row item DOES have end date for non-installments
        val newEmployeeContributionRowItem = capturedBulkInput.captured.rowItems.find { rowItem ->
            rowItem.keyValuePairs.any {
                it.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                    it.value == "Test Component"
            }
        }
        assertNotNull(newEmployeeContributionRowItem)

        val endDatePair = newEmployeeContributionRowItem!!.keyValuePairs.find {
            it.key == CompensationSkeletonField.END_DATE.id
        }
        val isInstallmentPair = newEmployeeContributionRowItem.keyValuePairs.find {
            it.key == CompensationSkeletonField.IS_INSTALLMENT.id
        }

        // For non-installments, end date should be present
        assertNotNull(endDatePair, "End date should be present for non-installment components")
        assertEquals("No", isInstallmentPair?.value, "Component should not be marked as installment")
    }

    @Test
    fun `upsertCompensationByMember should use correct pay schedules for each component`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )

        // Create multiple components with different pay schedules
        val basePayScheduleId = UUID.randomUUID()
        val basePayComponent = basePayComponentMock()
        every { basePayComponent.payScheduleId } returns basePayScheduleId
        val existingComponentPayScheduleId = UUID.randomUUID()
        val existingComponentSchemaItemId = UUID.randomUUID()
        val existingComponent = mockk<CompensationInput>(relaxed = true) {
            every { category } returns "ALLOWANCE"
            every { payScheduleId } returns existingComponentPayScheduleId
            every { schemaItemId } returns existingComponentSchemaItemId
            every { isInstallment } returns false
        }
        val onboardingComponents = listOf(basePayComponent, existingComponent)

        // Create mock pay schedules
        val basePaySchedule = mockk<PaySchedule>(relaxed = true) {
            every { id } returns basePayScheduleId
            every { name } returns "Monthly Base Pay Schedule"
        }
        val allowancePaySchedule = mockk<PaySchedule>(relaxed = true) {
            every { id } returns existingComponentPayScheduleId
            every { name } returns "Quarterly Allowance Schedule"
        }

        val bulkResponse = CompensationBulkCreateResponse(
            entityId = 123L,
            status = OperationStatus.SUCCESS,
            requestId = "test-request-id",
            rowValidationResults = emptyList(),
        )

        every { compensationInputService.getCompensationInputData(any()) } returns onboardingComponents
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        every { payScheduleService.getPaySchedulesByIds(any()) } returns setOf(basePaySchedule)
        every { compensationSchemaService.getSchemaItemById(any()) } returns mockk(relaxed = true) {
            every { componentName } returns "Test Component"
            every { category } returns "EMPLOYEE_CONTRIBUTION"
        }
        every { compensationSchemaService.getSchemaById(any()) } returns mockk(relaxed = true) {
            every { country } returns mockk { every { name } returns "CAN" }
            every { configurationScope } returns ConfigurationScope.COUNTRY
            every { tags } returns listOf(SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.toString())
            every { schemaItems } returns listOf(
                mockk(relaxed = true) {
                    every { id } returns UUID.randomUUID()
                    every { componentName } returns "Test Component"
                    every { category } returns "EMPLOYEE_CONTRIBUTION"
                },
                mockk(relaxed = true) {
                    every { id } returns existingComponentSchemaItemId
                    every { componentName } returns "Existing Allowance"
                    every { category } returns "ALLOWANCE"
                },
            )
        }
        // Return both pay schedules when fetching all pay schedules
        every { payScheduleService.getPaySchedules(any(), any(), any(), any()) } returns
            listOf(basePaySchedule, allowancePaySchedule)

        // Capture the bulk input to verify pay schedules are correctly assigned
        val capturedBulkInput = slot<CompensationBulkInput>()
        every { bulkCompensationCreationService.execute(capture(capturedBulkInput)) } returns bulkResponse

        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)

        // Then
        assertEquals(OperationStatus.SUCCESS, result.status)

        // Verify that each component uses its correct pay schedule
        val rowItems = capturedBulkInput.captured.rowItems

        // Find the new employee contribution component
        val newEmployeeContributionRowItem = rowItems.find { rowItem ->
            rowItem.keyValuePairs.any {
                it.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                    it.value == "Test Component"
            }
        }
        assertNotNull(newEmployeeContributionRowItem)

        val newComponentPaySchedule = newEmployeeContributionRowItem!!.keyValuePairs.find {
            it.key == CompensationSkeletonField.PAY_SCHEDULE_NAME.id
        }
        assertEquals(
            "Monthly Base Pay Schedule",
            newComponentPaySchedule?.value,
            "New component should use base pay schedule",
        )

        // Find the existing allowance component
        val existingAllowanceRowItem = rowItems.find { rowItem ->
            rowItem.keyValuePairs.any {
                it.key == CompensationSkeletonField.COMPONENT_NAME.id &&
                    it.value == "Existing Allowance"
            }
        }
        assertNotNull(existingAllowanceRowItem)

        val existingComponentPaySchedule = existingAllowanceRowItem!!.keyValuePairs.find {
            it.key == CompensationSkeletonField.PAY_SCHEDULE_NAME.id
        }
        assertEquals(
            "Quarterly Allowance Schedule",
            existingComponentPaySchedule?.value,
            "Existing component should use its own pay schedule",
        )
    }

    @Test
    fun `upsertCompensationByMember should handle bulk compensation service failures`() {
        // Given
        val request = UpsertCompensationByMemberRequest(
            contractId = 556851L,
            keyValueInputs = listOf(
                KeyValuePair("COMPONENT_NAME", "Test Component"),
                KeyValuePair("BILLING_RATE_TYPE", "Value"),
                KeyValuePair("BILLING_RATE", "100.0"),
                KeyValuePair("COUNTRY", "CAN"),
            ),
        )
        val basePayComponent = basePayComponentMock()
        val onboardingComponents = listOf(basePayComponent)
        every { compensationInputService.getCompensationInputData(any()) } returns onboardingComponents
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        every { payScheduleService.getPaySchedulesByIds(any()) } returns setOf(mockk(relaxed = true))
        every { compensationSchemaService.getSchemaItemById(any()) } returns mockk(relaxed = true)
        every { compensationSchemaService.getSchemaById(any()) } returns mockk(relaxed = true) {
            every { country } returns mockk { every { name } returns "CAN" }
            every { configurationScope } returns ConfigurationScope.COUNTRY
            every { tags } returns listOf(SchemaTags.MULTIPLIER_EOR_OPS_APPROVED.toString())
            every { schemaItems.any { it.componentName == "Test Component" } } returns true
            every { schemaItems.any { it.category == "EMPLOYEE_CONTRIBUTION" } } returns true
            every { schemaItems } returns listOf(
                mockk(relaxed = true) {
                    every { componentName } returns "Test Component"
                    every { category } returns "EMPLOYEE_CONTRIBUTION"
                },
            )
        }
        every { payScheduleService.getPaySchedules(any(), any(), any(), any()) } returns mockk(relaxed = true)

        every { bulkCompensationCreationService.execute(any()) } returns CompensationBulkCreateResponse(
            entityId = 123L,
            status = OperationStatus.FAILURE,
            requestId = "test-request-id",
            rowValidationResults = listOf(
                RowValidationResult(
                    id = "1",
                    cellValidationResults = listOf(
                        CellValidationResult(
                            field = KeyValuePair("BILLING_RATE", "100.0"),
                            type = ValidationResultType.ERROR,
                            message = "Invalid billing rate",
                        ),
                    ),
                ),
            ),
        )
        // When
        val result = upsertCompensationByMemberService.upsertCompensationByMember(request)
        System.out.println("Result: $result")
        // Then
        assertEquals(OperationStatus.FAILURE, result.status)
        // The error message will only be present if CONTRACT_BASE_PAY exists, so check for that
        assertTrue(result.componentValidationResults.any { it.message == "Invalid billing rate" })
    }
}
