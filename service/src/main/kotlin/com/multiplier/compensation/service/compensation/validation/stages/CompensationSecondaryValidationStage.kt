package com.multiplier.compensation.service.compensation.validation.stages

import com.multiplier.compensation.service.common.validationpipeline.common.stages.SecondaryValidationStage
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.validators.BasePayDependantDateRangeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingFrequencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateInputValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateValidator
import com.multiplier.compensation.service.compensation.validation.validators.CompensationDateValidator
import com.multiplier.compensation.service.compensation.validation.validators.DependentComponentCurrencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.InstallmentValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleValidator
import org.springframework.stereotype.Component

@Component
class CompensationSecondaryValidationStage(
    billingRateValidator: BillingRateValidator,
    compensationDateValidator: CompensationDateValidator,
    billingRateTypeValidator: BillingRateTypeValidator,
    billingFrequencyValidator: BillingFrequencyValidator,
    installmentValidator: InstallmentValidator,
    payScheduleValidator: PayScheduleValidator,
    payScheduleTypeValidator: PayScheduleTypeValidator,
    basePayDependantDateRangeValidator: BasePayDependantDateRangeValidator,
    dependentComponentCurrencyValidator: DependentComponentCurrencyValidator,
    billingRateInputValidator: BillingRateInputValidator,
) : SecondaryValidationStage<CompensationValidatorContext, CompensationDraft>() {
    init {
        setValidators(
            billingRateValidator,
            compensationDateValidator,
            billingRateTypeValidator,
            billingFrequencyValidator,
            installmentValidator,
            payScheduleValidator,
            payScheduleTypeValidator,
            basePayDependantDateRangeValidator,
            dependentComponentCurrencyValidator,
            billingRateInputValidator,
        )
    }
}
