package com.multiplier.compensation.domain.compensationinput

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.PayrollStatus
import com.multiplier.compensation.domain.common.ReasonCode
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.compensationinput.enums.CompensationLifecycle
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationInputTest {
    @Test
    fun `should create CompensationInput with all fields populated`() {
        // Given
        val id = UUID.randomUUID()
        val companyId = 101L
        val entityId = 202L
        val contractId = 303L
        val schemaItemId = UUID.randomUUID()
        val category = "CONTRACT_BASE_PAY"
        val currency = "USD"
        val billingRateType = BillingRateType.VALUE
        val billingRate = 1234.56
        val billingFrequency = BillingFrequency.MONTHLY
        val payScheduleId = UUID.randomUUID()
        val startDate = LocalDate.of(2025, 1, 1)
        val endDate = LocalDate.of(2025, 12, 31)
        val isInstallment = true
        val noOfInstallments = 12
        val requestType = RequestType.COMPENSATION_SETUP
        val requestId = "REQ-001"
        val reasonCode = ReasonCode.SC001
        val status = CompensationInputStatus.ONBOARDING_DRAFT
        val payrollCutoffDate = LocalDate.of(2025, 6, 15)
        val payrollStatus = PayrollStatus.STAGED
        val notes = "This is a test note."
        val createdOn = LocalDateTime.of(2025, 1, 1, 10, 0)
        val createdBy = 999L
        val updatedOn = LocalDateTime.of(2025, 5, 1, 12, 0)
        val updatedBy = 888L
        val lifecycle = CompensationLifecycle.MIGRATED
        val sourceEffectiveDate = LocalDateTime.of(2024, 12, 31, 0, 0)

        // When
        val input = CompensationInput(
            id,
            companyId,
            entityId,
            contractId,
            schemaItemId,
            category,
            currency,
            billingRateType,
            billingRate,
            billingFrequency,
            payScheduleId,
            startDate,
            endDate,
            isInstallment,
            noOfInstallments,
            requestType,
            requestId,
            reasonCode,
            status,
            payrollCutoffDate,
            payrollStatus,
            notes,
            createdOn,
            createdBy,
            updatedOn,
            updatedBy,
            lifecycle,
            sourceEffectiveDate,
        )

        // Then
        assertEquals(id, input.id)
        assertEquals(companyId, input.companyId)
        assertEquals(entityId, input.entityId)
        assertEquals(contractId, input.contractId)
        assertEquals(schemaItemId, input.schemaItemId)
        assertEquals(category, input.category)
        assertEquals(currency, input.currency)
        assertEquals(billingRateType, input.billingRateType)
        assertEquals(billingRate, input.billingRate)
        assertEquals(billingFrequency, input.billingFrequency)
        assertEquals(payScheduleId, input.payScheduleId)
        assertEquals(startDate, input.startDate)
        assertEquals(endDate, input.endDate)
        assertEquals(isInstallment, input.isInstallment)
        assertEquals(noOfInstallments, input.noOfInstallments)
        assertEquals(requestType, input.requestType)
        assertEquals(requestId, input.requestId)
        assertEquals(reasonCode, input.reasonCode)
        assertEquals(status, input.status)
        assertEquals(payrollCutoffDate, input.payrollCutoffDate)
        assertEquals(payrollStatus, input.payrollStatus)
        assertEquals(notes, input.notes)
        assertEquals(createdOn, input.createdOn)
        assertEquals(createdBy, input.createdBy)
        assertEquals(updatedOn, input.updatedOn)
        assertEquals(updatedBy, input.updatedBy)
        assertEquals(lifecycle, input.lifecycle)
        assertEquals(sourceEffectiveDate, input.sourceEffectiveDate)
    }

    @Test
    fun `should handle nullable fields correctly`() {
        // Given
        val input = CompensationInput(
            id = UUID.randomUUID(),
            companyId = 101L,
            entityId = 202L,
            contractId = 303L,
            schemaItemId = UUID.randomUUID(),
            category = "CONTRACT_BASE_PAY",
            currency = "EUR",
            billingRateType = BillingRateType.VALUE,
            billingRate = 3000.0,
            billingFrequency = BillingFrequency.QUARTERLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 2, 1),
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = "REQ-002",
            reasonCode = null,
            status = CompensationInputStatus.ACTIVATED,
            payrollCutoffDate = null,
            payrollStatus = null,
            notes = null,
        )

        // Then
        assertNull(input.endDate)
        assertNull(input.noOfInstallments)
        assertNull(input.reasonCode)
        assertNull(input.payrollCutoffDate)
        assertNull(input.payrollStatus)
        assertNull(input.notes)
        assertEquals(CompensationLifecycle.NEW, input.lifecycle)
        assertNull(input.sourceEffectiveDate)
    }

    @Test
    fun `should initialize createdOn and updatedOn with default values`() {
        // When
        val input = CompensationInput(
            id = UUID.randomUUID(),
            companyId = 101L,
            entityId = 202L,
            contractId = 303L,
            schemaItemId = UUID.randomUUID(),
            category = "CONTRACT_BASE_PAY",
            currency = "INR",
            billingRateType = BillingRateType.VALUE,
            billingRate = 90000.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            startDate = LocalDate.of(2025, 1, 1),
            isInstallment = false,
            noOfInstallments = null,
            requestType = RequestType.COMPENSATION_SETUP,
            requestId = "REQ-003",
            status = CompensationInputStatus.ACTIVATED,
        )

        // Then
        assertNotNull(input.createdOn)
        assertNotNull(input.updatedOn)
        assertEquals(-1L, input.createdBy)
        assertEquals(-1L, input.updatedBy)
        assertEquals(CompensationLifecycle.NEW, input.lifecycle)
    }
}
