-- liquibase formatted sql

-- changeset <PERSON><PERSON><PERSON>:20250507110100_insert_values_to_country_pay_frequency_table2

INSERT INTO compensation.country_pay_schedule_config (country_code, state, state_code, billing_frequency, pay_schedule_frequency)
VALUES
('NZL'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'BI_WEEKLY'::compensation.pay_schedule_frequency),
('NZL'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'BI_WEEKLY'::compensation.pay_schedule_frequency),
('PAK'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('PAK'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('LAO'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('LAO'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('GIB'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('GIB'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('BOL'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('BOL'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('KGZ'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('KGZ'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('TGO'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('TGO'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('MAC'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('MAC'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('COD'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('COD'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('GUM'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('GUM'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('HTI'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('HTI'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('VEN'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('VEN'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('LSO'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('LSO'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('GEO'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('GEO'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('ZMB'::compensation.country_code, NULL, NULL, 'ANNUALLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency),
('ZMB'::compensation.country_code, NULL, NULL, 'MONTHLY'::compensation.billing_frequency, 'MONTHLY'::compensation.pay_schedule_frequency);
