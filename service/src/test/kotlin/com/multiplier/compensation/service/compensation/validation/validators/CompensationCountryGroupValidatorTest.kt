package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate

class CompensationCountryGroupValidatorTest {
    private lateinit var validator: CompensationCountryGroupValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private val skeleton = mockk<Skeleton>(relaxed = true)

    private val draftId = "1"
    private val contractId = 123L
    private val countryCode = CountryCode.USA
    private val entityId = 100L

    @BeforeEach
    fun setup() {
        validator = CompensationCountryGroupValidator()
        context = CompensationValidatorContext(
            entityId = entityId,
            contracts = mapOf(
                contractId to Contract(
                    id = contractId,
                    companyId = 20L,
                    memberId = 30L,
                    employeeId = "123",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.of(2023, 1, 1),
                    currency = "USD",
                    countryCode = countryCode,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
                234L to Contract(
                    id = 234,
                    companyId = 20L,
                    memberId = 30L,
                    employeeId = "123",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.of(2023, 1, 1),
                    currency = "USD",
                    countryCode = CountryCode.CAN,
                    workplaceEntityId = 100L,
                    stateCode = "",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            skeleton = skeleton,
            contextSource = RequestType.COMPENSATION_SETUP,
            contextUseCase = ContextUseCase.VALIDATE,
            customParams = mapOf(
                "COUNTRY_CODE" to countryCode.name,
                "OFFERING_TYPE" to OfferingType.EOR.name,
            ),
        )
        collector = ValidationDataCollector(
            rowValidationResult = mutableMapOf(),
            drafts = mutableMapOf(
                draftId to CompensationDraft(
                    contractId = contractId,
                    companyId = 1L,
                    employeeId = "1",
                    schemaItemId = null,
                    schemaCategory = null,
                    currency = null,
                    billingRateType = null,
                    billingRate = null,
                    billingFrequency = null,
                    payScheduleId = null,
                    startDate = null,
                    endDate = null,
                    isInstallment = false,
                    noOfInstallments = null,
                    reasonCode = null,
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
    }

    @Test
    fun `should pass when all contracts match country code`() {
        val inputs = listOf(mockk<ValidationInputItem>(relaxed = true))
        val result = validator.validate(inputs, context, collector)
        Assertions.assertTrue(result)
    }

    @Test
    fun `should return true when offering type is GLOBAL_PAYROLL`() {
        val inputs = mockValidationInputItem()
        context = context.copy(
            customParams = mapOf(
                "COUNTRY_CODE" to countryCode.name,
                "OFFERING_TYPE" to OfferingType.GLOBAL_PAYROLL.name,
            ),
        )

        val result = validator.validate(inputs, context, collector)
        Assertions.assertTrue(result)
    }

    @Test
    fun `should fail when a contract does not match country code`() {
        val inputs = mockValidationInputItem()

        val context = context.copy(
            customParams = mapOf(
                "COUNTRY_CODE" to CountryCode.CAN.name,
                "OFFERING_TYPE" to OfferingType.EOR.name,
            ),
        )

        val result = validator.validate(inputs, context, collector)
        Assertions.assertFalse(result)
        val error = collector.rowValidationResult[draftId]?.first()?.message
        Assertions.assertTrue(error?.contains("does not match with the given country code") == true)
    }

    @Test
    fun `should return false when one of the contract belongs to different country`() {
        val inputs = mockValidationInputItem().toMutableList()
        inputs.add(
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair(
                        key = "COUNTRY_CODE",
                        value = "CAN",
                    ),
                    KeyValuePair(
                        key = "CONTRACT_ID",
                        value = "234",
                    ),
                ),
            ),
        )

        val drafts = collector.drafts.toMutableMap()
        drafts["2"] = CompensationDraft(
            contractId = 234,
            companyId = 1L,
            employeeId = "1",
            schemaItemId = null,
            schemaCategory = null,
            currency = null,
            billingRateType = null,
            billingRate = null,
            billingFrequency = null,
            payScheduleId = null,
            startDate = null,
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )
        val collectorWithDifferentCountryCode =
            collector.copy(drafts = drafts)

        val result =
            validator.validate(inputs, context, collectorWithDifferentCountryCode)
        Assertions.assertFalse(result)
    }

    @Test
    fun `should return true when setup validation is true`() {
        val inputs = mockValidationInputItem()

        val collector = ValidationDataCollector(
            rowValidationResult = mutableMapOf(),
            drafts = mutableMapOf(
                "0" to CompensationDraft(
                    contractId = 0L,
                    companyId = 1L,
                    employeeId = "1",
                    schemaItemId = null,
                    schemaCategory = null,
                    currency = null,
                    billingRateType = null,
                    billingRate = null,
                    billingFrequency = null,
                    payScheduleId = null,
                    startDate = null,
                    endDate = null,
                    isInstallment = false,
                    noOfInstallments = null,
                    reasonCode = null,
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )
        val result = validator.validate(inputs, context, collector)
        Assertions.assertTrue(result)
    }

    private fun mockValidationInputItem(): List<ValidationInputItem> = listOf(
        ValidationInputItem(
            id = draftId,
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = "COUNTRY_CODE",
                    value = countryCode.name,
                ),
                KeyValuePair(
                    key = "CONTRACT_ID",
                    value = contractId.toString(),
                ),
            ),
        ),
    )
}
