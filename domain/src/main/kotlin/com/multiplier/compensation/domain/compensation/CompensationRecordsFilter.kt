package com.multiplier.compensation.domain.compensation

import com.multiplier.compensation.domain.compensation.enums.CompensationState
import java.time.LocalDate

data class CompensationRecordsFilter(
    override val state: CompensationState? = null,
    override val activeAsOn: LocalDate? = null,
    val entityId: Long? = null,
    val companyId: Long? = null,
    val contractIds: List<Long>? = null,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
    val categories: List<String>? = null,
    val recordTypes: List<CompensationRecordType>? = null,
    // Add more fields as required
) : CompensationFilter
