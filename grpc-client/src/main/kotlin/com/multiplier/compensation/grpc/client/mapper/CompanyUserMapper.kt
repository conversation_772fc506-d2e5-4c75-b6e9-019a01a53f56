package com.multiplier.compensation.grpc.client.mapper

import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.compensation.grpc.client.dto.CompanyUser
import com.multiplier.compensation.grpc.client.dto.EmailAddress
import com.multiplier.core.schema.common.Common.EmailAddress as GrpcEmailAddress

fun CompanyOuterClass.CompanyUser.map() = CompanyUser(
    id = id,
    firstName = firstName,
    lastName = lastName,
    isAdmin = isAdmin,
    emails = emailsList.map { it.map() },
)

fun GrpcEmailAddress.map() = EmailAddress(
    type = type,
    email = email,
)
