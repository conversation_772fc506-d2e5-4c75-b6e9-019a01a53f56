package com.multiplier.compensation.service.compensationitem.generation.steps

import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.CompensationItemPublisher
import com.multiplier.compensation.service.compensationitem.generation.pipeline.CompensationItemGenerationContext
import com.multiplier.compensation.service.compensationitem.generation.pipeline.getUpdatedFailureContext
import com.multiplier.compensation.service.compensationitem.generation.pipeline.steps.PostCommitCompensationItemsStep
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withTimeout
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

class PostCommitCompensationItemsStepTest {
    private lateinit var step: PostCommitCompensationItemsStep
    private lateinit var compensationItemPublisher: CompensationItemPublisher

    @BeforeEach
    fun setup() {
        compensationItemPublisher = mockk(relaxed = true)
        step = PostCommitCompensationItemsStep(compensationItemPublisher)
    }

    private fun createContext(
        generatedItems: Map<UUID, List<CompensationItem>> = emptyMap(),
        shouldPersist: Boolean = true,
    ): CompensationItemGenerationContext = CompensationItemGenerationContext(
        jobId = UUID.randomUUID(),
        request = mockk {
            every { <EMAIL> } returns shouldPersist
        },
        compensations = emptyMap(),
        generatedCompensationItems = generatedItems,
        failureContext = emptyMap(),
    )

    @Test
    fun `should publish all generated compensation items`() = runTest {
        val compensationItem1 = TestCompensationItemBuilder().build()
        val compensationItem2 = TestCompensationItemBuilder().build()
        val generatedItems = mapOf(
            UUID.randomUUID() to listOf(compensationItem1),
            UUID.randomUUID() to listOf(compensationItem2),
        )

        val context = createContext(generatedItems)

        val result = step.process(context)

        coVerify(exactly = 1) { compensationItemPublisher.publish(listOf(compensationItem1)) }
        coVerify(exactly = 1) { compensationItemPublisher.publish(listOf(compensationItem2)) }
        assertTrue(result.failureContext.isEmpty())
    }

    @Test
    fun `should handle timeout while publishing items`() = runTest {
        val compensationItem = TestCompensationItemBuilder().build()
        val compensationId = compensationItem.compensationId
        val generatedItems = mapOf(
            UUID.randomUUID() to listOf(compensationItem),
        )

        val context = createContext(generatedItems)

        every { compensationItemPublisher.publish(any()) } coAnswers {
            withTimeout(1000) {
                delay(2000)
            }
        }

        step.process(context)

        val failureContext = context.getUpdatedFailureContext(step.failureEvents)
        assertEquals(1, failureContext.size)

        val failureEvent = failureContext[compensationId]?.first()
        assertEquals("PostCommitCompensationItemsStep", failureEvent?.failedStep)
        assertEquals("Timed out waiting for 1000 ms", failureEvent?.errorMessage)
    }

    @Test
    fun `should handle generic exceptions while publishing items`() = runTest {
        val compensationItem = TestCompensationItemBuilder().build()
        val generatedItems = mapOf(
            UUID.randomUUID() to listOf(compensationItem),
        )

        val context = createContext(generatedItems)

        every { compensationItemPublisher.publish(any()) } throws RuntimeException("Publishing error")

        step.process(context)

        val failureContext = context.getUpdatedFailureContext(step.failureEvents)
        assertEquals(1, failureContext.size)

        val failureEvent = failureContext[compensationItem.compensationId]?.first()
        assertEquals("PostCommitCompensationItemsStep", failureEvent?.failedStep)
        assertEquals("Publishing error", failureEvent?.errorMessage)
    }
}
