package com.multiplier.compensation.dbRepository

import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.ResultQuery
import org.springframework.stereotype.Component

@Component
class CompensationLogDb(
    private val dsl: DSLContext,
) {
    fun getCompensationLog(): ResultQuery<Record> = dsl.resultQuery("SELECT * FROM compensation.compensation_log;")

    fun getCompensationLogViaContractId(contractId: String): ResultQuery<Record> = dsl.resultQuery(
        "SELECT * " +
            "FROM compensation.compensation_log where contract_id= $contractId;",
    )

    fun getCompensationLogViaCategoryAndContract(
        category: String,
        contractId: String,
    ): ResultQuery<Record> = dsl.resultQuery(
        "SELECT * FROM compensation.compensation_log WHERE category = ? and contract_id = ?",
        category,
        contractId,
    )
}
