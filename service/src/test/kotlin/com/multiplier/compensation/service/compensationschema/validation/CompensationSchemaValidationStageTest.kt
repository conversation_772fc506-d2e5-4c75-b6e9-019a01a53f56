package com.multiplier.compensation.service.compensationschema.validation

import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaComponentNameValidator
import com.multiplier.compensation.service.compensationschema.validation.validators.SchemaIsFixedValidator
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.reflect.full.declaredFunctions
import kotlin.reflect.full.superclasses
import kotlin.reflect.jvm.isAccessible

@ExtendWith(MockKExtension::class)
class CompensationSchemaValidationStageTest {
    private lateinit var schemaComponentNameValidator: SchemaComponentNameValidator
    private lateinit var schemaIsFixedValidator: SchemaIsFixedValidator

    @BeforeEach
    fun setup() {
        schemaComponentNameValidator = mockk(relaxed = true)
        schemaIsFixedValidator = mockk(relaxed = true)
    }

    @Test
    fun `should call setValidators in init block`() {
        val stage = CompensationSchemaValidationStage(
            schemaComponentNameValidator,
            schemaIsFixedValidator,
        )

        val setValidatorsMethod = stage::class
            .superclasses.first()
            .declaredFunctions.find { it.name == "setValidators" }

        requireNotNull(setValidatorsMethod) { "setValidators method not found" }

        setValidatorsMethod.isAccessible = true

        val result = setValidatorsMethod.call(stage, arrayOf(schemaComponentNameValidator, schemaIsFixedValidator))
        assert(result != null)
    }
}
