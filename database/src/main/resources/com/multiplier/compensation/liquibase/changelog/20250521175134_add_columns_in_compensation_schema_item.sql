-- liquibase formatted sql

-- changeset Abhishek Yadav:20250521175134_add_columns_in_compensation_schema_item

-- Add new columns to the compensation_schema_item table
ALTER TABLE compensation.compensation_schema_item
ADD COLUMN item_type compensation.item_type;

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN validation TEXT;

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN calculation TEXT;

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN billing_rate_type compensation.billing_rate_type;

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN is_overtime_eligible BOOLEAN;

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN description TEXT;

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN billing_frequency compensation.billing_frequency;

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN pay_schedule_id UUID REFERENCES compensation.pay_schedule(id);

ALTER TABLE compensation.compensation_schema_item
ADD COLUMN currency VARCHAR(3);

-- Set default values
UPDATE compensation.compensation_schema_item
SET item_type = 'INPUT'::compensation.item_type;

UPDATE compensation.compensation_schema_item
SET billing_rate_type = 'VALUE'::compensation.billing_rate_type;

UPDATE compensation.compensation_schema_item
SET is_overtime_eligible = false;

-- update the columns to be non-nullable
ALTER TABLE compensation.compensation_schema_item
ALTER COLUMN item_type SET NOT NULL;

ALTER TABLE compensation.compensation_schema_item
ALTER COLUMN billing_rate_type SET NOT NULL;

ALTER TABLE compensation.compensation_schema_item
ALTER COLUMN is_overtime_eligible SET NOT NULL;


