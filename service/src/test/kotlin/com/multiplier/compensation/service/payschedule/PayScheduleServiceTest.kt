package com.multiplier.compensation.service.payschedule

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.database.repository.payschedule.CountryPayScheduleConfigRepository
import com.multiplier.compensation.database.repository.payschedule.PayScheduleItemRepository
import com.multiplier.compensation.database.repository.payschedule.PayScheduleRepository
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.common.PageRequest
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.PayScheduleItem
import com.multiplier.compensation.domain.payschedule.PayScheduleRequest
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.payschedule.common.getExpectedIntervals
import com.multiplier.compensation.service.payschedule.schedulegeneration.PayScheduleIntervalGenerator
import com.multiplier.compensation.service.payschedule.schedulegeneration.dto.PayScheduleInterval
import com.multiplier.compensation.service.payschedule.schedulegeneration.mapper.toPayScheduleItems
import com.multiplier.compensation.service.payschedule.strategy.CompensationPayScheduleSelectionStrategyFactory
import com.multiplier.transaction.database.jooq.audit.Audit
import com.multiplier.transaction.database.jooq.exception.TransactionException
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.jooq.DSLContext
import org.jooq.exception.IntegrityConstraintViolationException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Suppress("UNCHECKED_CAST")
@ExtendWith(MockKExtension::class)
class PayScheduleServiceTest {
    private lateinit var audit: Audit
    private lateinit var dsl: DSLContext
    private lateinit var payScheduleIntervalGenerator: PayScheduleIntervalGenerator
    private lateinit var payScheduleItemsGenerator: PayScheduleItemsGenerator
    private lateinit var payScheduleItemRepository: PayScheduleItemRepository
    private lateinit var payScheduleRepository: PayScheduleRepository
    private lateinit var timeBasedEpochGenerator: TimeBasedEpochGenerator
    private lateinit var transactional: Transactional
    private lateinit var payScheduleService: PayScheduleService
    private lateinit var countryPayScheduleConfigRepository: CountryPayScheduleConfigRepository
    private lateinit var compensationPayScheduleSelectionStrategyFactory:
        CompensationPayScheduleSelectionStrategyFactory
    private val payScheduleItemSamplesDirectory =
        "src/test/resources/payschedule/schedulegeneration/sample/monthly/noninstallment"

    @BeforeEach
    fun setUp() {
        audit = mockk()
        dsl = mockk()
        payScheduleIntervalGenerator = mockk()
        payScheduleItemRepository = mockk()
        payScheduleRepository = mockk()
        timeBasedEpochGenerator = mockk()
        transactional = mockk()
        countryPayScheduleConfigRepository = mockk()
        compensationPayScheduleSelectionStrategyFactory = mockk(relaxed = true)

        every {
            transactional.invoke(any<TransactionContext.() -> Any>())
        } answers {
            val lambda = it.invocation.args[0] as TransactionContext.() -> List<PayScheduleItem>
            lambda(TransactionContext(dsl, audit))
        }

        payScheduleItemsGenerator = PayScheduleItemsGenerator(
            payScheduleIntervalGenerator,
            timeBasedEpochGenerator,
        )

        payScheduleService = PayScheduleService(
            payScheduleItemsGenerator,
            payScheduleItemRepository,
            payScheduleRepository,
            transactional,
            compensationPayScheduleSelectionStrategyFactory,
        )
    }

    @Test
    fun `getPaySchedules for non-installments should use latest item start and end dates if available`() {
        val entityIds = listOf(1L)
        val referenceStartDate = LocalDate.of(2024, 1, 1)
        val referenceEndDate = LocalDate.of(2024, 1, 31)
        val payScheduleId = UUID.randomUUID()
        val paySchedule = PaySchedule(
            id = payScheduleId,
            entityId = 1L,
            companyId = 1L,
            name = "Monthly",
            frequency = PayScheduleFrequency.MONTHLY,
            configurationScope = ConfigurationScope.COMPANY,
            country = CountryCode.USA,
            startDateReference = referenceStartDate,
            endDateReference = referenceEndDate,
            payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
            relativePayDays = 0L,
            isInstallment = false,
            isActive = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1,
            label = "Monthly Pay Schedule",
        )
        val latestItem = PayScheduleItem(
            id = UUID.randomUUID(),
            payScheduleId = payScheduleId,
            entityId = 1L,
            companyId = 1L,
            name = "Monthly",
            frequency = PayScheduleFrequency.MONTHLY,
            startDate = LocalDate.of(2024, 12, 1),
            endDate = LocalDate.of(2024, 12, 31),
            payDate = LocalDate.of(2024, 12, 31),
            isActive = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1,
        )

        every { payScheduleRepository.findAllByEntityIds(entityIds) } returns listOf(paySchedule)
        every {
            payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(
                listOf(payScheduleId),
                LocalDate.of(2024, 11, 15),
                LocalDate.of(2024, 12, 14),
            )
        } returns listOf(latestItem)

        val result = payScheduleService.getPaySchedules(
            entityIds,
            LocalDate.of(2024, 11, 15),
            LocalDate.of(2024, 12, 14),
        )

        assertEquals(1, result.size)
        val schedules = result[1L]
        assertEquals(1, schedules?.size)
        val resultPaySchedule = schedules?.first()
        assertEquals(latestItem.startDate, resultPaySchedule?.startDateReference)
        assertEquals(latestItem.endDate, resultPaySchedule?.endDateReference)
    }

    @Test
    fun `getPaySchedules for non-installments should use start and end date references if latest item not available`() {
        val entityIds = listOf(1L)
        val referenceStartDate = LocalDate.of(2024, 12, 1)
        val referenceEndDate = LocalDate.of(2024, 12, 31)
        val payScheduleId = UUID.randomUUID()
        val paySchedule = PaySchedule(
            id = payScheduleId,
            entityId = 1L,
            companyId = 1L,
            name = "Monthly",
            frequency = PayScheduleFrequency.MONTHLY,
            configurationScope = ConfigurationScope.COMPANY,
            country = CountryCode.USA,
            startDateReference = LocalDate.of(2024, 12, 1),
            endDateReference = LocalDate.of(2024, 12, 31),
            payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
            relativePayDays = 0L,
            isInstallment = false,
            isActive = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1,
            label = "Monthly Pay Schedule",
        )

        every { payScheduleRepository.findAllByEntityIds(entityIds) } returns listOf(paySchedule)
        every {
            payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(
                listOf(payScheduleId),
                LocalDate.of(2024, 11, 15),
                LocalDate.of(2024, 12, 14),
            )
        } returns emptyList()

        val result = payScheduleService.getPaySchedules(
            entityIds,
            LocalDate.of(2024, 11, 15),
            LocalDate.of(2024, 12, 14),
        )

        assertEquals(1, result.size)
        val schedules = result[1L]
        assertEquals(1, schedules?.size)
        val updatedPaySchedule = schedules?.first()
        assertEquals(referenceStartDate, updatedPaySchedule?.startDateReference)
        assertEquals(referenceEndDate, updatedPaySchedule?.endDateReference)
    }

    @Test
    fun `getPaySchedules for installments should always use start and end date references`() {
        val entityIds = listOf(1L)
        val referenceStartDate = LocalDate.of(2024, 12, 1)
        val referenceEndDate = LocalDate.of(2024, 12, 31)
        val payScheduleId = UUID.randomUUID()
        val paySchedule = PaySchedule(
            id = payScheduleId,
            entityId = 1L,
            companyId = 1L,
            name = "Monthly",
            frequency = PayScheduleFrequency.MONTHLY,
            configurationScope = ConfigurationScope.COMPANY,
            country = CountryCode.USA,
            startDateReference = LocalDate.of(2024, 12, 1),
            endDateReference = LocalDate.of(2024, 12, 31),
            payDateReferenceType = PayDateReference.COMPENSATION_START_DATE,
            relativePayDays = 0L,
            isInstallment = true,
            isActive = true,
            createdOn = LocalDateTime.now(),
            createdBy = 1,
            updatedOn = LocalDateTime.now(),
            updatedBy = 1,
            label = "Monthly Pay Schedule",
        )

        every { payScheduleRepository.findAllByEntityIds(entityIds) } returns listOf(paySchedule)
        every {
            payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(
                any(),
                LocalDate.of(2024, 11, 15),
                LocalDate.of(2024, 12, 14),
            )
        } returns emptyList()

        val result = payScheduleService.getPaySchedules(
            entityIds,
            LocalDate.of(2024, 11, 15),
            LocalDate.of(2024, 12, 14),
        )

        verify {
            payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(
                emptyList(),
                LocalDate.of(2024, 11, 15),
                LocalDate.of(2024, 12, 14),
            )
        }

        assertEquals(1, result.size)
        val schedules = result[1L]
        assertEquals(1, schedules?.size)
        val resultPaySchedule = schedules?.first()
        assertEquals(referenceStartDate, resultPaySchedule?.startDateReference)
        assertEquals(referenceEndDate, resultPaySchedule?.endDateReference)
    }

    @Test
    fun `get pay schedule items when existing pay schedule items exist`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2023, 12, 16)
        val to = LocalDate.of(2025, 3, 24)
        val paySchedule = mockedPaySchedule(
            payScheduleId = payScheduleId,
            startDateReference = LocalDate.of(2023, 12, 25),
            endDateReference = LocalDate.of(2024, 1, 24),
            isInstallment = false,
        )

        val existingPayScheduleItems = mockedPayScheduleItems(
            paySchedule,
            listOf(LocalDate.of(2023, 11, 25)),
            listOf(LocalDate.of(2023, 12, 24)),
            listOf(LocalDate.of(2023, 12, 23)),
        )

        val backFilledPayScheduleIntervals = emptyList<PayScheduleInterval>()

        every {
            payScheduleRepository.findById(payScheduleId)
        } returns paySchedule

        every {
            payScheduleItemRepository.findAllByPayScheduleIdAndDateRangeBetween(payScheduleId, from, to)
        } returns existingPayScheduleItems

        val newPayScheduleIntervalsGenerated = getExpectedIntervals(
            "$payScheduleItemSamplesDirectory/" +
                "monthly_noninstallment_test_1_start_date_not_same_as_month_first_date.csv",
        )

        every {
            payScheduleIntervalGenerator.generateIntervals(paySchedule, LocalDate.of(2023, 12, 25), to)
        } returns newPayScheduleIntervalsGenerated

        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()

        every {
            payScheduleItemRepository.saveAll(any(), any())
        } returns Unit

        val result = payScheduleService.getPayScheduleItems(
            payScheduleId,
            from,
            to,
        )

        verify(exactly = 1) {
            payScheduleIntervalGenerator.generateIntervals(
                any(),
                any(),
                any(),
            )
        }

        verify {
            payScheduleIntervalGenerator.generateIntervals(
                paySchedule,
                LocalDate.of(2023, 12, 25),
                to,
            )
        }

        verifyPayScheduleItems(
            payScheduleItems = result,
            expectedPayScheduleItems = backFilledPayScheduleIntervals.toPayScheduleItems(
                paySchedule,
                timeBasedEpochGenerator,
            ) + existingPayScheduleItems + newPayScheduleIntervalsGenerated.toPayScheduleItems(
                paySchedule,
                timeBasedEpochGenerator,
            ),
        )
    }

    @Test
    fun `get pay schedule items when existing pay schedule items exist with backfill of items needed`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2023, 11, 16)
        val to = LocalDate.of(2025, 3, 24)
        val paySchedule = mockedPaySchedule(
            payScheduleId = payScheduleId,
            startDateReference = LocalDate.of(2023, 12, 25),
            endDateReference = LocalDate.of(2024, 1, 24),
            isInstallment = false,
        )

        val existingPayScheduleItems = mockedPayScheduleItems(
            paySchedule,
            listOf(LocalDate.of(2023, 11, 25)),
            listOf(LocalDate.of(2023, 12, 24)),
            listOf(LocalDate.of(2023, 12, 23)),
        )

        val backFilledPayScheduleIntervals = listOf(
            PayScheduleInterval(
                LocalDate.of(2023, 10, 25),
                LocalDate.of(2023, 11, 24),
                LocalDate.of(2023, 11, 23),
            ),
        )

        every {
            payScheduleRepository.findById(payScheduleId)
        } returns paySchedule

        every {
            payScheduleItemRepository.findAllByPayScheduleIdAndDateRangeBetween(payScheduleId, from, to)
        } returns existingPayScheduleItems

        val newPayScheduleIntervalsGenerated = getExpectedIntervals(
            "$payScheduleItemSamplesDirectory/" +
                "monthly_noninstallment_test_1_start_date_not_same_as_month_first_date.csv",
        )

        every {
            payScheduleIntervalGenerator.generateIntervals(
                paySchedule,
                LocalDate.of(2023, 10, 25),
                LocalDate.of(2023, 11, 24),
            )
        } returns backFilledPayScheduleIntervals

        every {
            payScheduleIntervalGenerator.generateIntervals(paySchedule, LocalDate.of(2023, 12, 25), to)
        } returns newPayScheduleIntervalsGenerated

        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()

        every {
            payScheduleItemRepository.saveAll(any(), any())
        } returns Unit

        val result = payScheduleService.getPayScheduleItems(
            payScheduleId,
            from,
            to,
        )

        verify {
            payScheduleIntervalGenerator.generateIntervals(
                paySchedule,
                LocalDate.of(2023, 10, 25),
                LocalDate.of(2023, 11, 24),
            )
            payScheduleIntervalGenerator.generateIntervals(
                paySchedule,
                LocalDate.of(2023, 12, 25),
                to,
            )
        }

        verifyPayScheduleItems(
            payScheduleItems = result,
            expectedPayScheduleItems = backFilledPayScheduleIntervals.toPayScheduleItems(
                paySchedule,
                timeBasedEpochGenerator,
            ) + existingPayScheduleItems + newPayScheduleIntervalsGenerated.toPayScheduleItems(
                paySchedule,
                timeBasedEpochGenerator,
            ),
        )
    }

    @Test
    fun `get pay schedule items when backfill of items needed and adjustment of last date needed`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2023, 11, 16)
        val to = LocalDate.of(2025, 3, 24)
        val paySchedule = mockedPaySchedule(
            payScheduleId = payScheduleId,
            startDateReference = LocalDate.of(2023, 12, 25),
            endDateReference = LocalDate.of(2024, 1, 24),
            isInstallment = false,
        )

        val existingPayScheduleItems = mockedPayScheduleItems(
            paySchedule,
            listOf(LocalDate.of(2023, 11, 25)),
            listOf(LocalDate.of(2023, 12, 24)),
            listOf(LocalDate.of(2023, 12, 23)),
        )

        val backFilledPayScheduleIntervals = listOf(
            PayScheduleInterval(
                LocalDate.of(2023, 10, 25),
                LocalDate.of(2023, 11, 25),
                LocalDate.of(2023, 11, 23),
            ),
        )

        val expectedBackFilledPayScheduleIntervals = listOf(
            PayScheduleInterval(
                LocalDate.of(2023, 10, 25),
                LocalDate.of(2023, 11, 24),
                LocalDate.of(2023, 11, 23),
            ),
        )

        every {
            payScheduleRepository.findById(payScheduleId)
        } returns paySchedule

        every {
            payScheduleItemRepository.findAllByPayScheduleIdAndDateRangeBetween(payScheduleId, from, to)
        } returns existingPayScheduleItems

        val newPayScheduleIntervalsGenerated = getExpectedIntervals(
            "$payScheduleItemSamplesDirectory/" +
                "monthly_noninstallment_test_1_start_date_not_same_as_month_first_date.csv",
        )

        every {
            payScheduleIntervalGenerator.generateIntervals(
                paySchedule,
                LocalDate.of(2023, 10, 25),
                LocalDate.of(2023, 11, 24),
            )
        } returns backFilledPayScheduleIntervals

        every {
            payScheduleIntervalGenerator.generateIntervals(paySchedule, LocalDate.of(2023, 12, 25), to)
        } returns newPayScheduleIntervalsGenerated

        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()

        every {
            payScheduleItemRepository.saveAll(any(), any())
        } returns Unit

        val result = payScheduleService.getPayScheduleItems(
            payScheduleId,
            from,
            to,
        )

        verify {
            payScheduleIntervalGenerator.generateIntervals(
                paySchedule,
                LocalDate.of(2023, 10, 25),
                LocalDate.of(2023, 11, 24),
            )
            payScheduleIntervalGenerator.generateIntervals(
                paySchedule,
                LocalDate.of(2023, 12, 25),
                to,
            )
        }

        verifyPayScheduleItems(
            payScheduleItems = result,
            expectedPayScheduleItems = expectedBackFilledPayScheduleIntervals.toPayScheduleItems(
                paySchedule,
                timeBasedEpochGenerator,
            ) + existingPayScheduleItems + newPayScheduleIntervalsGenerated.toPayScheduleItems(
                paySchedule,
                timeBasedEpochGenerator,
            ),
        )
    }

    @Test
    fun `get pay schedule items when existing pay schedule items do not exist`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2023, 11, 16)
        val to = LocalDate.of(2025, 3, 24)
        val paySchedule = mockedPaySchedule(
            payScheduleId = payScheduleId,
            startDateReference = LocalDate.of(2023, 12, 25),
            endDateReference = LocalDate.of(2024, 1, 24),
            isInstallment = false,
        )

        val existingPayScheduleItems = emptyList<PayScheduleItem>()

        every {
            payScheduleRepository.findById(payScheduleId)
        } returns paySchedule

        every {
            payScheduleItemRepository.findAllByPayScheduleIdAndDateRangeBetween(payScheduleId, from, to)
        } returns existingPayScheduleItems

        val newPayScheduleIntervalsGenerated = listOf(
            PayScheduleInterval(
                startDate = LocalDate.of(2023, 10, 25),
                endDate = LocalDate.of(2023, 11, 24),
                payDate = LocalDate.of(2023, 11, 23),
            ),
            PayScheduleInterval(
                startDate = LocalDate.of(2023, 11, 25),
                endDate = LocalDate.of(2023, 12, 24),
                payDate = LocalDate.of(2023, 12, 23),
            ),
        ).plus(
            getExpectedIntervals(
                "$payScheduleItemSamplesDirectory/" +
                    "monthly_noninstallment_test_1_start_date_not_same_as_month_first_date.csv",
            ),
        )

        every {
            payScheduleIntervalGenerator.generateIntervals(paySchedule, any(), to)
        } returns newPayScheduleIntervalsGenerated

        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()

        every {
            payScheduleItemRepository.saveAll(any(), any())
        } returns Unit

        val result = payScheduleService.getPayScheduleItems(
            payScheduleId,
            from,
            to,
        )

        verify {
            payScheduleIntervalGenerator.generateIntervals(paySchedule, LocalDate.of(2023, 10, 25), to)
        }

        verifyPayScheduleItems(
            payScheduleItems = result,
            expectedPayScheduleItems = newPayScheduleIntervalsGenerated.toPayScheduleItems(
                paySchedule,
                timeBasedEpochGenerator,
            ),
        )
    }

    @Test
    fun `get pay schedule items with invalid date range throws Exception`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2023, 11, 16)
        val to = LocalDate.of(2021, 3, 24)
        val paySchedule = mockedPaySchedule(
            payScheduleId = payScheduleId,
            startDateReference = LocalDate.of(2024, 1, 1),
            endDateReference = LocalDate.of(2024, 1, 31),
            isInstallment = false,
        )

        every {
            payScheduleRepository.findById(payScheduleId)
        } returns paySchedule

        val exception = assertThrows<InvalidArgumentException> {
            payScheduleService.getPayScheduleItems(
                payScheduleId,
                from,
                to,
            )
        }

        assertEquals(
            "Invalid pay schedule date range requested from [$from] to [$to]",
            exception.message,
        )
    }

    @Test
    fun `test getPaySchedulesWithPagination with valid request`() {
        val payScheduleRequest = PayScheduleRequest(
            companyIds = listOf(1L, 2L),
            entityIds = listOf(3L, 4L),
            referenceStartDate = LocalDate.of(2023, 1, 1),
            referenceEndDate = LocalDate.of(2023, 12, 31),
            offeringType = OfferingCode.GLOBAL_PAYROLL,
            country = CountryCode.USA,
        )
        val pageRequest = PageRequest(pageNumber = 1, pageSize = 10)
        val mockPaySchedules = listOf(
            mockedPaySchedule(
                payScheduleId = UUID.randomUUID(),
                startDateReference = LocalDate.of(2023, 1, 1),
                endDateReference = LocalDate.of(2023, 1, 31),
                isInstallment = false,
            ),
        )
        val totalRecords = 1L
        val mockPayScheduleItems = mockedPayScheduleItems(
            mockPaySchedules[0],
            listOf(LocalDate.of(2023, 1, 1)),
            listOf(LocalDate.of(2023, 1, 31)),
            listOf(LocalDate.of(2023, 1, 31)),
        )

        every { payScheduleRepository.findAllByPayScheduleRequestWithPagination(any(), any()) } returns Pair(
            mockPaySchedules,
            totalRecords,
        )
        every {
            payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(
                any(),
                any(),
                any(),
            )
        } returns mockPayScheduleItems

        val result = payScheduleService.getPaySchedulesWithPagination(payScheduleRequest, pageRequest)

        verify { payScheduleRepository.findAllByPayScheduleRequestWithPagination(any(), any()) }
        verify { payScheduleItemRepository.findAllByPayScheduleIdsAndDateRangeBetween(any(), any(), any()) }

        assertEquals(1, result.first.size)
        assertEquals(totalRecords, result.second)
    }

    @Test
    fun `getPayScheduleItems should handle concurrent item generation gracefully`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2023, 11, 16)
        val to = LocalDate.of(2025, 3, 24)
        val paySchedule = mockedPaySchedule(
            payScheduleId = payScheduleId,
            startDateReference = LocalDate.of(2023, 12, 25),
            endDateReference = LocalDate.of(2024, 1, 24),
            isInstallment = false,
        )

        val existingPayScheduleItems = emptyList<PayScheduleItem>()

        every {
            payScheduleRepository.findById(payScheduleId)
        } returns paySchedule

        every {
            payScheduleItemRepository.findAllByPayScheduleIdAndDateRangeBetween(payScheduleId, from, to)
        } returns existingPayScheduleItems

        val newPayScheduleIntervalsGenerated = listOf(
            PayScheduleInterval(
                startDate = LocalDate.of(2023, 10, 25),
                endDate = LocalDate.of(2023, 11, 24),
                payDate = LocalDate.of(2023, 11, 23),
            ),
            PayScheduleInterval(
                startDate = LocalDate.of(2023, 11, 25),
                endDate = LocalDate.of(2023, 12, 24),
                payDate = LocalDate.of(2023, 12, 23),
            ),
        ).plus(
            getExpectedIntervals(
                "$payScheduleItemSamplesDirectory/" +
                    "monthly_noninstallment_test_1_start_date_not_same_as_month_first_date.csv",
            ),
        )

        every {
            payScheduleIntervalGenerator.generateIntervals(paySchedule, any(), to)
        } returns newPayScheduleIntervalsGenerated

        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()

        every {
            payScheduleItemRepository.saveAll(any(), any())
        } throws TransactionException("Test exception", IntegrityConstraintViolationException("Duplicate key"))

        val result = payScheduleService.getPayScheduleItems(payScheduleId, from, to)

        verify { payScheduleItemRepository.saveAll(any(), any()) }

        assertThat(result.size).isEqualTo(newPayScheduleIntervalsGenerated.size)
    }

    @Test
    fun `getPayScheduleItems should handle runtime exceptions while persisting during item generation gracefully`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2023, 11, 16)
        val to = LocalDate.of(2025, 3, 24)
        val paySchedule = mockedPaySchedule(
            payScheduleId = payScheduleId,
            startDateReference = LocalDate.of(2023, 12, 25),
            endDateReference = LocalDate.of(2024, 1, 24),
            isInstallment = false,
        )

        val existingPayScheduleItems = emptyList<PayScheduleItem>()

        every {
            payScheduleRepository.findById(payScheduleId)
        } returns paySchedule

        every {
            payScheduleItemRepository.findAllByPayScheduleIdAndDateRangeBetween(payScheduleId, from, to)
        } returns existingPayScheduleItems

        val newPayScheduleIntervalsGenerated = listOf(
            PayScheduleInterval(
                startDate = LocalDate.of(2023, 10, 25),
                endDate = LocalDate.of(2023, 11, 24),
                payDate = LocalDate.of(2023, 11, 23),
            ),
            PayScheduleInterval(
                startDate = LocalDate.of(2023, 11, 25),
                endDate = LocalDate.of(2023, 12, 24),
                payDate = LocalDate.of(2023, 12, 23),
            ),
        ).plus(
            getExpectedIntervals(
                "$payScheduleItemSamplesDirectory/" +
                    "monthly_noninstallment_test_1_start_date_not_same_as_month_first_date.csv",
            ),
        )

        every {
            payScheduleIntervalGenerator.generateIntervals(paySchedule, any(), to)
        } returns newPayScheduleIntervalsGenerated

        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()

        every {
            payScheduleItemRepository.saveAll(any(), any())
        } throws TransactionException("Test exception", RuntimeException("Test exception"))

        val result = payScheduleService.getPayScheduleItems(payScheduleId, from, to)

        verify { payScheduleItemRepository.saveAll(any(), any()) }

        assertThat(result.size).isEqualTo(newPayScheduleIntervalsGenerated.size)
    }

    @Test
    fun `getPayScheduleItems should throw exception for invalid pay schedule`() {
        val payScheduleId = UUID.randomUUID()
        val from = LocalDate.of(2024, 1, 1)
        val to = LocalDate.of(2024, 12, 31)

        every { payScheduleRepository.findById(payScheduleId) } returns null

        assertThrows<InvalidArgumentException> {
            payScheduleService.getPayScheduleItems(payScheduleId, from, to)
        }
    }

    @Test
    fun `should return empty collection when no pay schedules found`() {
        val entityId = 1L
        val offeringType = OfferingType.EOR
        val countryCode = CountryCode.USA
        val excludeInactiveSchedules = true

        every {
            payScheduleRepository.findAllByCountryCodeAndConfigurationScope(
                countryCode,
                ConfigurationScope.COUNTRY,
                true,
            )
        } returns emptyList()

        every { payScheduleRepository.findAllByEntityId(entityId, true) } returns emptyList()

        val result = payScheduleService.getPaySchedules(
            entityId = entityId,
            offeringType = offeringType,
            countryCode = countryCode,
            excludeInactiveSchedules = excludeInactiveSchedules,
        )

        assertEquals(emptyList<PaySchedule>(), result)
        verify(exactly = 1) {
            payScheduleRepository.findAllByCountryCodeAndConfigurationScope(
                countryCode,
                ConfigurationScope.COUNTRY,
                true,
            )
        }
        verify(exactly = 0) {
            payScheduleRepository.findAllByEntityId(entityId, true)
        }
    }

    @Test
    fun `getPaySchedulesWithPagination should throw exception when offeringType is EOR and country is not provided`() {
        val payScheduleRequest = PayScheduleRequest(
            companyIds = listOf(1L, 2L),
            entityIds = listOf(3L, 4L),
            referenceStartDate = LocalDate.of(2025, 1, 1),
            referenceEndDate = LocalDate.of(2025, 1, 31),
            offeringType = OfferingCode.EOR,
            country = null,
        )
        val pageRequest = PageRequest(pageNumber = 1, pageSize = 10)
        val exception = assertThrows<InvalidArgumentException> {
            payScheduleService.getPaySchedulesWithPagination(payScheduleRequest, pageRequest)
        }
        assertEquals("Country code is required when offering type is EOR", exception.message)
    }
}

private fun verifyPayScheduleItems(
    payScheduleItems: Collection<PayScheduleItem>,
    expectedPayScheduleItems: Collection<PayScheduleItem>,
) = assertThat(payScheduleItems.size).isEqualTo(expectedPayScheduleItems.size).also {
    payScheduleItems.zip(expectedPayScheduleItems).onEach { (paySchedule, expectedPaySchedule) ->
        run {
            assertThat(paySchedule.startDate).isEqualTo(expectedPaySchedule.startDate)
            assertThat(paySchedule.endDate).isEqualTo(expectedPaySchedule.endDate)
            assertThat(paySchedule.payDate).isEqualTo(expectedPaySchedule.payDate)
        }
    }
}

private fun mockedPaySchedule(
    payScheduleId: UUID = UUID.randomUUID(),
    startDateReference: LocalDate,
    endDateReference: LocalDate,
    isInstallment: Boolean,
    entityId: Long = 1L,
) = PaySchedule(
    id = payScheduleId,
    entityId = entityId,
    companyId = 1L,
    name = "Monthly-05",
    frequency = PayScheduleFrequency.MONTHLY,
    configurationScope = ConfigurationScope.COMPANY,
    country = CountryCode.USA,
    startDateReference = startDateReference,
    endDateReference = endDateReference,
    payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
    relativePayDays = -1L,
    isInstallment = isInstallment,
    isActive = true,
    createdOn = LocalDateTime.now(),
    createdBy = -1,
    updatedOn = LocalDateTime.now(),
    updatedBy = -1,
    label = "Monthly Pay Schedule",
)

private fun mockedPayScheduleItems(
    paySchedule: PaySchedule,
    startDates: List<LocalDate>,
    endDates: List<LocalDate>,
    payDates: List<LocalDate>,
): List<PayScheduleItem> = (startDates.zip(endDates)).zip(payDates).map { entry ->
    PayScheduleItem(
        id = UUID.randomUUID(),
        payScheduleId = paySchedule.id,
        entityId = paySchedule.entityId,
        companyId = paySchedule.companyId,
        name = paySchedule.name,
        frequency = paySchedule.frequency,
        startDate = entry.first.first,
        endDate = entry.first.second,
        payDate = entry.second,
        isActive = true,
        createdOn = LocalDateTime.now(),
        createdBy = -1,
        updatedOn = LocalDateTime.now(),
        updatedBy = -1,
    )
}
