#!/bin/bash

java -javaagent:/app/elastic-apm-agent.jar \
-XX:+UseContainerSupport ${JAVA_HEAP_MAX_MEM} -Djava.security.egd=file:/dev/./urandom \
-Delastic.apm.service_name=compensation-service-${ENV_VAR} \
-Delastic.apm.server_urls=${APM_SERVER_URL} \
-Delastic.apm.environment=${ENV_VAR} \
-Delastic.apm.secret_token=${APM_TOKEN} \
-Delastic.apm.application_packages=com.multiplier \
-jar /app/application.jar
