package com.multiplier.compensation.service.skeleton

import com.multiplier.company.schema.grpc.CompanyServiceGrpc.CompanyServiceBlockingStub
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.SkeletonData
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.mock.preReqsMocking.PreReqsMocking
import com.multiplier.compensation.service.TestInterface
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructure
import com.multiplier.compensation.utils.CsvUtil
import com.multiplier.payroll.schema.grpc.schema.PayrollSchemaServiceGrpc.PayrollSchemaServiceBlockingStub
import org.junit.jupiter.api.assertAll
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@Component
class SkeletonServiceTest(
    @Autowired
    private val skeletonService: SkeletonService,
    @Autowired
    private val payrollSchemaStub: PayrollSchemaServiceBlockingStub,
    @Autowired
    private val preReqsMocking: PreReqsMocking,
    @Autowired
    private val companyServiceStub: CompanyServiceBlockingStub,
    @Autowired
    private val csvUtil: CsvUtil,
) : TestInterface {
    @Value("\${file.staticVariablePath}")
    lateinit var staticVariablePath: Resource

    private val configs: MutableMap<SkeletonType, List<SkeletonData>> = mutableMapOf()
    private val csvData by lazy {
        csvUtil.readAndProcessCSV(staticVariablePath)
    }

    val entityId: Long by lazy { csvData[0]["ENTITY_ID"]?.toLongOrNull() ?: 0L }
    val companyId: Long by lazy { csvData[0]["COMPANY_ID"]?.toLongOrNull() ?: 0L }
    val currencyCode: String by lazy { csvData[0]["CURRENCY_CODE"].orEmpty() }
    val countryCode: String by lazy { csvData[0]["COUNTRY_CODE"].orEmpty() }

    override fun initPaths(folderResource: Resource) {
        TODO("Not yet implemented")
    }

    override fun preSetup() {
        preReqsMocking.mockPayrollSchema(payrollSchemaStub)
        preReqsMocking.mockCompanyService(
            entityId,
            currencyCode,
            countryCode,
            companyId,
            companyServiceStub,
        )
        configs.clear()
    }

    override fun processing() {
        val skeletonTypes = listOf(
            SkeletonType.COMPENSATION_SETUP,
            SkeletonType.COMPENSATION_SCHEMA,
            SkeletonType.PAY_SCHEDULE,
            SkeletonType.DEDUCTION,
            SkeletonType.COMPENSATION_REVISION,
            SkeletonType.PAY_SUPPLEMENT,
        )

        for (skeletonType in skeletonTypes) {
            val skeleton: Skeleton = skeletonService.getSkeleton(
                entityId,
                skeletonType,
                customParams = mapOf(
                    BulkJsonCustomParamsUtil.BulkCustomKeys.OFFERING_TYPE to
                        OfferingType.GLOBAL_PAYROLL.name,
                ),
            )
            val skeletonDataList: List<SkeletonData> = skeleton.data
            configs[skeletonType] = skeletonDataList
        }
    }

    override fun validation() {
        configs.forEach { (skeletonType, skeletonDataList) ->
            assertEquals(getJSONSize(skeletonType), skeletonDataList.size)
            skeletonDataList.forEach { skeletonData ->
                validateCommonFields(skeletonData, skeletonType)
                validateSpecificFields(skeletonData, skeletonType)
            }
        }
    }

    private fun validateCommonFields(
        skeletonData: SkeletonData,
        skeletonType: SkeletonType,
    ) {
        assertNotNull(skeletonData.fieldId, "filedId is null for skeletonType: $skeletonType")
        assertTrue(skeletonData.fieldId.isNotEmpty(), "Field ID should not be empty, for skeletonType: $skeletonType")

        assertNotNull(skeletonData.fieldName, "filedName is null for skeletonType: $skeletonType")
        assertTrue(
            skeletonData.fieldName.isNotEmpty(),
            "Field Name should not be empty, for skeletonType: $skeletonType",
        )

        assertNotNull(skeletonData.valueType, "valueType is null, for skeletonType: $skeletonType")
        assertTrue(
            listOf(
                "STRING",
                "DOUBLE",
                "BOOLEAN",
                "INTEGER",
                "DATE",
                "SELECT",
            ).contains(skeletonData.valueType.toString()),
            "Unexpected value type: ${skeletonData.valueType}",
        )

        assertNotNull(skeletonData.description, "description is null, for skeletonType: $skeletonType")
        assertTrue(
            skeletonData.description.isNotEmpty(),
            "Description should not be empty, for skeletonType: $skeletonType",
        )

        assertNotNull(skeletonData.mandatory, "mandatory is null, for skeletonType: $skeletonType")

        skeletonData.validationRegex?.let {
            assertTrue(
                it.regex.isNotEmpty(),
                "Validation regex should not be empty if present, for skeletonType: $skeletonType",
            )
        }

        skeletonData.defaultValue?.let {
            assertTrue(it.isNotEmpty(), "Default value should not be empty if present, for skeletonType: $skeletonType")
        }
    }

    private fun validateSpecificFields(
        skeletonData: SkeletonData,
        skeletonType: SkeletonType,
    ) {
        when (skeletonData.fieldId) {
            "EMPLOYEE_FULL_NAME" -> assertAll(
                "Verify EMPLOYEE_FULL_NAME properties",
                {
                    assertEquals(
                        "Employee Full Name",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.STRING,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Employee Full Name",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertFalse(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "employeeId" -> assertAll(
                "Verify employeeId properties",
                {
                    assertEquals(
                        "Employee ID",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.STRING,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Employee Identifier",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "COMPONENT_NAME" -> assertAll(
                "Verify COMPONENT_NAME properties",
                {
                    assertEquals(
                        "Component Name",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    val expectedValueType = if (skeletonType == SkeletonType.COMPENSATION_SCHEMA) {
                        ValueType.STRING
                    } else {
                        ValueType.SELECT
                    }
                    assertEquals(
                        expectedValueType,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    val expectedDescription = if (skeletonType == SkeletonType.COMPENSATION_SCHEMA) {
                        "Name of component"
                    } else {
                        "The name of the component."
                    }
                    assertEquals(
                        expectedDescription,
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
            )

            "CONTRACT_ID" -> assertAll(
                "Verify CONTRACT_ID properties",
                {
                    assertEquals(
                        "Contract Id",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.STRING,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Contract Identifier",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "CURRENCY" -> assertAll(
                "Verify CURRENCY properties",
                {
                    assertEquals(
                        "Currency",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "The currency of the entity.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "BILLING_TYPE" -> assertAll(
                "Verify BILLING_TYPE properties",
                {
                    assertEquals(
                        "Billing Type",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Billing type.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    assertEquals(
                        "Value",
                        skeletonData.defaultValue,
                        "DefaultValue mismatch, for skeletonType: $skeletonType",
                    )
                },
            )

            "BILLING_RATE_TYPE" -> assertAll(
                "Verify BILLING_RATE_TYPE properties",
                {
                    assertEquals(
                        "Billing Rate Type",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Billing rate type.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "BILLING_RATE" -> assertAll(
                "Verify BILLING_RATE properties",
                {
                    assertEquals(
                        "Billing Rate",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.DOUBLE,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Billing Rate for compensation component.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertFalse(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    assertEquals(
                        "^\\d*\\.?\\d+\$|^\\d+\\.\\d*\$",
                        skeletonData.validationRegex?.regex,
                        "PossibleValues mismatch, for skeletonType: $skeletonType",
                    )
                },
            )

            "BILLING_FREQUENCY" -> assertAll(
                "Verify BILLING_FREQUENCY properties",
                {
                    assertEquals(
                        "Billing Frequency",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Billing Frequency for compensation component.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "PAY_SCHEDULE_NAME" -> assertAll(
                "Verify PAY_SCHEDULE_NAME properties",
                {
                    assertEquals(
                        "Pay Schedule Name",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    val expectedValueType = if (skeletonType == SkeletonType.PAY_SCHEDULE) {
                        ValueType.STRING
                    } else {
                        ValueType.SELECT
                    }
                    assertEquals(
                        expectedValueType,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    val expectedDescription = if (skeletonType == SkeletonType.PAY_SCHEDULE) {
                        "Name / Reference of Pay Schedule"
                    } else {
                        "Payment schedule name."
                    }
                    assertEquals(
                        expectedDescription,
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "IS_INSTALLMENT" -> assertAll(
                "Verify IS_INSTALLMENT properties",
                {
                    val isInstallment = if (skeletonType == SkeletonType.COMPENSATION_SETUP ||
                        skeletonType == SkeletonType.DEDUCTION ||
                        skeletonType == SkeletonType.PAY_SUPPLEMENT ||
                        skeletonType == SkeletonType.COMPENSATION_REVISION
                    ) {
                        "Is Installment?"
                    } else {
                        "Is Installment"
                    }
                    assertEquals(
                        isInstallment,
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Flag indicating whether component is an installment.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "CATEGORY_KEY" -> assertAll(
                "Verify CATEGORY_KEY properties",
                {
                    assertEquals(
                        "Category Key",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Category of component. Select from a predefined list of categories.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "IS_TAXABLE" -> assertAll(
                "Verify IS_TAXABLE properties",
                {
                    assertEquals(
                        "Is Taxable?",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Component is taxable or not.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "IS_FIXED" -> assertAll(
                "Verify IS_FIXED properties",
                {
                    assertEquals(
                        "Is Fixed?",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Select Yes for fixed and No for variable.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    assertEquals(
                        "Yes",
                        skeletonData.defaultValue,
                        "DefaultValue mismatch, for skeletonType: $skeletonType",
                    )
                },
            )

            "IS_PRORATED" -> assertAll(
                "Verify IS_PRORATED properties",
                {
                    assertEquals(
                        "Is Prorated?",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Indicates if the compensation component is prorated based on working days/hours.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    assertEquals(
                        "No",
                        skeletonData.defaultValue,
                        "DefaultValue mismatch, for skeletonType: $skeletonType",
                    )
                },
            )

            "IS_MANDATORY" -> assertAll(
                "Verify IS_MANDATORY properties",
                {
                    assertEquals(
                        "Is Mandatory?",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Mandatory components from compliance standpoint.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    assertEquals(
                        "Yes",
                        skeletonData.defaultValue,
                        "DefaultValue mismatch, for skeletonType: $skeletonType",
                    )
                },
            )

            "IS_PART_OF_BASE_PAY" -> assertAll(
                "Verify IS_PART_OF_BASE_PAY properties",
                {
                    assertEquals(
                        "Is Part of BasePay?",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Indicates if the compensation component is part of base pay or not.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    assertEquals(
                        "No",
                        skeletonData.defaultValue,
                        "DefaultValue mismatch, for skeletonType: $skeletonType",
                    )
                },
            )

            "ENTITY_COUNTRY" -> assertAll(
                "Verify ENTITY_COUNTRY properties",
                {
                    assertEquals(
                        "Entity Country",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Country where the entity is located.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "CUSTOMER_ENTITY_ID" -> assertAll(
                "Verify CUSTOMER_ENTITY_ID properties",
                {
                    assertEquals(
                        "Customer Entity ID",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.STRING,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Unique identifier for the customer entity.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "REFERENCE_PERIOD_START_DATE" -> assertAll(
                "Verify REFERENCE_PERIOD_START_DATE properties",
                {
                    assertEquals(
                        "Reference Period Start Date",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.DATE,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Start Date of one instance.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "REFERENCE_PERIOD_END_DATE" -> assertAll(
                "Verify REFERENCE_PERIOD_END_DATE properties",
                {
                    assertEquals(
                        "Reference Period End Date",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.DATE,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "End Date of that instance",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "START_DATE" -> assertAll(
                "Verify START_DATE properties",
                {
                    assertEquals(
                        "Start Date",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.DATE,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Start date of the component. For installment, it would be first installment start date",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "END_DATE" -> assertAll(
                "Verify END_DATE properties",
                {
                    assertEquals(
                        "End Date",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.DATE,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "End date of the component. Leave it blank for indefinite end date.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertFalse(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "PAY_DATE_RELATIVE_DAYS" -> assertAll(
                "Verify PAY_DATE_RELATIVE_DAYS properties",
                {
                    assertEquals(
                        "Pay Date Relative Days",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.INTEGER,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Pay Date relative days wrt Pay Date Reference Type",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "REASON_CODE" -> assertAll(
                "Verify REASON_CODE properties",
                {
                    assertEquals(
                        "Salary Change Reason Code",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Reason code for salary change.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertFalse(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "PAY_SCHEDULE_FREQUENCY" -> assertAll(
                "Verify PAY_SCHEDULE_FREQUENCY properties",
                {
                    assertEquals(
                        "Pay Schedule Frequency",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Frequency of the pay schedule.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
                {
                    skeletonData.possibleValues?.let {
                        assertTrue(
                            it.containsAll(
                                listOf(
                                    "ANNUALLY",
                                    "SEMI_ANNUALLY",
                                    "QUARTERLY",
                                    "MONTHLY",
                                    "SEMI_MONTHLY",
                                    "BI_WEEKLY",
                                    "WEEKLY",
                                    "DAILY",
                                    "ONE_TIME",
                                ),
                            ),
                            "PossibleValues mismatch, for skeletonType: $skeletonType",
                        )
                    }
                },
            )

            "PAY_DATE_REFERENCE_TYPE" -> assertAll(
                "Verify PAY_DATE_REFERENCE_TYPE properties",
                {
                    assertEquals(
                        "Pay Date Reference Type",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Reference date for calculating pay date",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "NUMBER_OF_INSTALLMENTS" -> assertAll(
                "Verify NUMBER_OF_INSTALLMENTS properties",
                {
                    assertEquals(
                        "Number of installments",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.INTEGER,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Number of installments to be paid.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertFalse(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "SCHEMA_NAME" -> assertAll(
                "Verify SCHEMA_NAME properties",
                {
                    assertEquals(
                        "Schema Name",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.STRING,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Name of schema",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertFalse(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            "TAGS" -> assertAll(
                "Verify TAGS properties",
                {
                    assertEquals(
                        "Tags",
                        skeletonData.fieldName,
                        "FieldName mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        ValueType.SELECT,
                        skeletonData.valueType,
                        "ValueType mismatch, for skeletonType: $skeletonType",
                    )
                },
                {
                    assertEquals(
                        "Indicates compensation schema tags to enable filtering on schemas.",
                        skeletonData.description,
                        "Description mismatch, for skeletonType: $skeletonType",
                    )
                },
                { assertTrue(skeletonData.mandatory, "IsMandatory mismatch, for skeletonType: $skeletonType") },
            )

            else -> throw IllegalArgumentException(
                "No match found for fieldId: ${skeletonData.fieldId}, for skeletonType: $skeletonType",
            )
        }
    }

    private fun getJSONSize(skeletonType: SkeletonType): Int {
        val configsField = SkeletonService::class.java.getDeclaredField("configs")
        configsField.isAccessible = true

        val configs = configsField.get(skeletonService) as Map<*, *>

        val skeletonStructure = configs[skeletonType] as? SkeletonStructure

        assertNotNull(skeletonStructure, "SkeletonStructure should not be null")

        return skeletonStructure.structure.size
    }
}
