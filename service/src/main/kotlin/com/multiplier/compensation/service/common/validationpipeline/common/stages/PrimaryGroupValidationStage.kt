package com.multiplier.compensation.service.common.validationpipeline.common.stages

import com.multiplier.compensation.service.common.validationpipeline.GroupValidationStage
import com.multiplier.compensation.service.common.validationpipeline.GroupValidator
import com.multiplier.compensation.service.common.validationpipeline.ValidationDraftObject
import com.multiplier.compensation.service.common.validationpipeline.ValidatorContext
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem

open class PrimaryGroupValidationStage<T : ValidatorContext, R : ValidationDraftObject> :
    GroupValidationStage<T, R> {
    private val validators = mutableListOf<GroupValidator<T, R>>()

    protected fun setValidators(vararg validators: GroupValidator<T, R>) {
        this.validators.addAll(validators)
    }

    override fun process(
        items: List<ValidationInputItem>,
        context: T,
        collector: ValidationDataCollector<R>,
    ): Boolean {
        for (validator in validators) {
            if (!validator.validate(items, context, collector)) {
                return false
            }
        }
        return true
    }
}
