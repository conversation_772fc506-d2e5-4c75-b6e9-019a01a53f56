syntax = "proto3";

package com.multiplier.compensation.grpc.schema;

import "com/multiplier/compensation/grpc/schema/common/common.proto";
import "com/multiplier/compensation/grpc/schema/payschedule/pay_schedule.proto";
import "com/multiplier/grpc/common/v1/uuid.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/datetime.proto";

option java_multiple_files = true;

message CompensationRecordsFilter {
  repeated string ids = 1;
  optional int64 entity_id = 2;
  repeated int64 contract_ids = 3;
  repeated string categories = 4;
  optional google.type.DateTime active_from = 5;
  optional google.type.DateTime active_to = 6;
  optional CompensationRecordType record_type = 7;
  optional CompensationState state = 8;
  optional google.type.DateTime active_as_on = 9;
  repeated CompensationRecordType record_types = 10;
}

enum CompensationRecordType {
  COMPENSATION_RECORD_TYPE_DRAFT = 0; // Data from Compensation Input
  COMPENSATION_RECORD_TYPE_APPLIED = 1; // Data from Core Compensation
}

enum CompensationRecordStatus {
  COMPENSATION_RECORD_STATUS_NEW = 0;
  COMPENSATION_RECORD_STATUS_PROCESSING = 1;
  COMPENSATION_RECORD_STATUS_COMPLETED = 2;
  COMPENSATION_RECORD_STATUS_ABORTED = 3;
  COMPENSATION_RECORD_STATUS_DELETED = 4;
  COMPENSATION_RECORD_STATUS_ONBOARDING_DRAFT = 5;
}

message CompensationRecordsResponse {
  repeated CompensationDetail compensationDetails = 1;
}

message CompensationDetail {
  com.multiplier.grpc.common.v1.Uuid id = 1;
  int64 entity_id = 2;
  int64 contract_id = 3;
  string compensation_name = 4;
  string compensation_category = 5;
  string currency = 6;
  BillingRateType billing_rate_type = 7;
  optional double billing_rate = 8;
  BillingFrequency billing_frequency = 9;
  string pay_schedule_name = 10;
  google.type.Date start_date = 11;
  google.type.Date end_date = 12;
  bool is_installment = 13;
  optional uint32 no_of_installments = 14;
  int32 generated_installments = 15;
  bool is_taxable = 16;
  bool is_fixed = 17;
  bool is_prorated = 18;
  bool is_mandatory = 19;
  bool is_part_of_base_pay = 20;
  CompensationRecordType record_type = 21;
  CompensationRecordStatus status = 22;
  google.protobuf.Timestamp created_on = 23;
  int64 created_by = 24;
  google.protobuf.Timestamp updated_on = 25;
  int64 updated_by = 26;
  map<string, AdditionalPropertyValue> additional_properties = 27;
  payschedule.PayScheduleFrequency pay_schedule_frequency = 28;
  optional string notes = 29;
  string label = 30;
  bool is_overtime_eligible = 31;
  bool is_part_of_ctc = 32;
}
