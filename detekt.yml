comments:
  active: true

complexity:
  active: false

coroutines:
  active: true
  InjectDispatcher:
    ignoreAnnotated:
      - org.springframework.context.annotation.Bean

empty-blocks:
  active: true

exceptions:
  active: true
  TooGenericExceptionCaught:
    active: false

naming:
  active: true

performance:
  active: true
  SpreadOperator:
    active: false

potential-bugs:
  active: true
  ImplicitDefaultLocale:
    active: false

style:
  active: true
  MaxLineLength:
    active: false
    maxLineLength: 120
    excludeCommentStatements: true
    excludeImportStatements: true
    excludePackageStatements: true
    excludeRawStrings: true
  ForbiddenComment:
    active: false
  MagicNumber:
    active: false
  ReturnCount:
    active: false
  ThrowsCount:
    active: false
  UnusedParameter:
    active: false
  UnusedPrivateProperty:
    active: true
    ignoreAnnotated:
      - io.mockk.impl.annotations.*
  VarCouldBeVal:
    ignoreAnnotated:
      - net.devh.boot.grpc.client.inject.*
      - io.mockk.impl.annotations.*
