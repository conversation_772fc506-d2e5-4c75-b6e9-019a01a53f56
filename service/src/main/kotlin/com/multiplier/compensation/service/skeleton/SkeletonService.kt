package com.multiplier.compensation.service.skeleton

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.skeleton.enricher.SkeletonEnricherService
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructure
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.Resource
import org.springframework.stereotype.Service

@Service
class SkeletonService(
    @Value("\${skeleton.structure-files-url}") private val files: Array<Resource>,
    private val skeletonEnricherService: SkeletonEnricherService,
) {
    private val configs: Map<SkeletonType, SkeletonStructure> = jacksonObjectMapper().let { mapper ->
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        mapper.findAndRegisterModules()

        files.flatMap { fetchConfig(it, mapper) }.toMap()
    }

    private fun fetchConfig(
        resource: Resource,
        mapper: ObjectMapper,
    ): List<Pair<SkeletonType, SkeletonStructure>> {
        val config = resource.inputStream.use { mapper.readValue(it, SkeletonStructure::class.java) }

        return config.skeletonType.map { it to config }
    }

    fun getSkeleton(
        entityId: Long,
        skeletonType: SkeletonType,
        customParams: Map<String, String>,
    ): Skeleton {
        val skeletonStructure: SkeletonStructure = configs[skeletonType]
            ?: throw InvalidArgumentException(
                errorCode = ValidationErrorCode.SkeletonConfigurationNotFound,
                message = "Skeleton configuration not found for type [$skeletonType]",
            )
        return skeletonEnricherService.enrichSkeleton(skeletonType, entityId, customParams, skeletonStructure)
    }

    fun getSkeletonFields(skeletonType: SkeletonType) = configs[skeletonType]?.structure?.map { it.fieldId }?.toSet()
        ?: throw InvalidArgumentException(
            errorCode = ValidationErrorCode.SkeletonConfigurationNotFound,
            message = "Skeleton configuration not found for type [$skeletonType]",
        )
}
