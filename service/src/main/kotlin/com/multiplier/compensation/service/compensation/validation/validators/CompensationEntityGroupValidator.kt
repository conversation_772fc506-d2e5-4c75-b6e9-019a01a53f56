package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.GroupValidator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class CompensationEntityGroupValidator : GroupValidator<CompensationValidatorContext, CompensationDraft> {
    override fun validate(
        inputs: List<ValidationInputItem>,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        log.info { "Validating entity id for ${inputs.size} inputs" }
        val offeringType = BulkJsonCustomParamsUtil.getOfferingType(context.customParams)
        if (offeringType == OfferingType.GLOBAL_PAYROLL) {
            log.info { "Skipping entity id validation for offering type: $offeringType" }
            return true
        }
        val contractIds = collector.drafts.values.map { it.contractId }.toSet()
        val contractDetails = context.contracts
        val allContractsMatch = contractIds.all { contractId ->
            if (isSetupValidation(context, contractId)) {
                return true
            }
            contractDetails[contractId] != null &&
                contractDetails[contractId]?.workplaceEntityId == context.entityId
        }

        if (!allContractsMatch) {
            inputs.forEach { input ->
                val contractId = input.fieldKeyValuePairs.find {
                    it.key == CommonSkeletonField.CONTRACT_ID.id
                }?.value?.toLongOrNull()
                val contract = contractDetails[contractId]
                if (contract?.workplaceEntityId != context.entityId) {
                    addValidationResult(
                        input = input,
                        field = CommonSkeletonField.ENTITY_ID,
                        resultType = ValidationResultType.ERROR,
                        message =
                            "The contract [$contractId] entity id [${contract?.workplaceEntityId}] does not match " +
                                "with the given entity id [${context.entityId}] ",
                        collector = collector,
                    )
                }
            }
            return false
        }
        return true
    }
}
