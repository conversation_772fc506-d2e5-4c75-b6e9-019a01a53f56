package com.multiplier.compensation.service.payschedule.strategy

import com.multiplier.compensation.database.repository.payschedule.PayScheduleRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@DisplayName("GlobalPayrollPayScheduleStrategy Automated Tests")
class GlobalPayrollPayScheduleStrategyTest {
    private lateinit var payScheduleRepository: PayScheduleRepository
    private lateinit var strategy: GlobalPayrollCompensationPayScheduleSelectionStrategy

    @BeforeEach
    fun setUp() {
        payScheduleRepository = mockk()
        strategy = GlobalPayrollCompensationPayScheduleSelectionStrategy(payScheduleRepository)
    }

    @Nested
    @DisplayName("Primary Salary Component Tests")
    inner class PrimarySalaryComponentTests {
        @Test
        @DisplayName("Should return eligible pay schedules for primary salary components")
        fun shouldReturnEligiblePaySchedulesForPrimarySalary() {
            // Given
            val request = createPayScheduleRequest(
                billingFrequency = BillingFrequency.MONTHLY,
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val expectedPaySchedules = listOf(createMockPaySchedule())

            every {
                payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                    entityId = request.entityId,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = false,
                )
            } returns expectedPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(expectedPaySchedules, result)
            verify {
                payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                    entityId = request.entityId,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = false,
                )
            }
        }

        @Test
        @DisplayName("Should return restricted billing frequencies for primary salary")
        fun shouldReturnRestrictedBillingFrequenciesForPrimarySalary() {
            // Given
            val request = createBillingFrequenciesRequest(
                compensationCategory = "CONTRACT_BASE_PAY",
            )

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            // Should only contain HOURLY, DAILY, MONTHLY, ANNUALLY
            assertEquals(4, result.size)
            assertTrue(result.contains(BillingFrequency.HOURLY))
            assertTrue(result.contains(BillingFrequency.DAILY))
            assertTrue(result.contains(BillingFrequency.MONTHLY))
            assertTrue(result.contains(BillingFrequency.ANNUALLY))
        }

        @ParameterizedTest
        @DisplayName("Should handle all primary salary categories")
        @ValueSource(
            strings = [
                "CONTRACT_BASE_PAY",
                "TOTAL_COST_TO_COMPANY",
                "CONTRACT_BASE_PAY_BREAKUP",
                "EMPLOYER_CONTRIBUTION",
                "EMPLOYEE_CONTRIBUTION",
            ],
        )
        fun shouldHandleAllPrimarySalaryCategories(category: String) {
            // Given
            val request = createBillingFrequenciesRequest(compensationCategory = category)

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            // Should only contain HOURLY, DAILY, MONTHLY, ANNUALLY
            assertEquals(4, result.size)
            assertTrue(result.contains(BillingFrequency.HOURLY))
            assertTrue(result.contains(BillingFrequency.DAILY))
            assertTrue(result.contains(BillingFrequency.MONTHLY))
            assertTrue(result.contains(BillingFrequency.ANNUALLY))
        }
    }

    @Nested
    @DisplayName("Additional Component Tests")
    inner class AdditionalComponentTests {
        @Test
        @DisplayName("Should return eligible pay schedules for additional components")
        fun shouldReturnEligiblePaySchedulesForAdditionalComponents() {
            // Given
            val request = createPayScheduleRequest(
                billingFrequency = BillingFrequency.MONTHLY,
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )
            val expectedPaySchedules = listOf(createMockPaySchedule())

            every {
                payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                    entityId = request.entityId,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = null,
                )
            } returns expectedPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(expectedPaySchedules, result)
        }

        @Test
        @DisplayName("Should return billing frequencies for additional components")
        fun shouldReturnBillingFrequenciesForAdditionalComponents() {
            // Given
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertFalse(result.isEmpty())
            assertTrue(result.contains(BillingFrequency.MONTHLY))
        }

        @Test
        @DisplayName("Should handle MONTHLY gross salary frequency for additional components")
        fun shouldHandleMonthlyGrossSalaryFrequency() {
            // Given
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertFalse(result.isEmpty())
            assertTrue(result.contains(BillingFrequency.MONTHLY))
        }

        @Test
        @DisplayName("Should handle BI_WEEKLY gross salary frequency for additional components")
        fun shouldHandleBiWeeklyGrossSalaryFrequency() {
            // Given
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.BI_WEEKLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertFalse(result.isEmpty())
            assertTrue(result.contains(BillingFrequency.BIWEEKLY))
        }
    }

    @Nested
    @DisplayName("Validation Tests")
    inner class ValidationTests {
        @Test
        @DisplayName("Should throw exception when billing frequency is null")
        fun shouldThrowExceptionWhenBillingFrequencyIsNull() {
            // Given
            val request = createPayScheduleRequest(billingFrequency = null)

            // When & Then
            assertThrows<InvalidArgumentException> {
                strategy.getEligiblePaySchedules(request)
            }
        }

        @Test
        @DisplayName("Should throw exception when gross salary frequency is null for additional components")
        fun shouldThrowExceptionWhenGrossSalaryFrequencyIsNullForAdditionalComponents() {
            // Given
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = null,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When & Then
            assertThrows<InvalidArgumentException> {
                strategy.getEligibleBillingFrequencies(request)
            }
        }

        @Test
        @DisplayName("Should throw exception when gross salary frequency is invalid for additional components")
        fun shouldThrowExceptionWhenGrossSalaryFrequencyIsInvalidForAdditionalComponents() {
            // Given
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.ANNUALLY, // Not supported in GP
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When & Then
            assertThrows<InvalidArgumentException> {
                strategy.getEligibleBillingFrequencies(request)
            }
        }

        @Test
        @DisplayName("Should validate successfully for primary salary components")
        fun shouldValidateSuccessfullyForPrimarySalaryComponents() {
            // Given
            val request = createBillingFrequenciesRequest(
                compensationCategory = "CONTRACT_BASE_PAY",
            )

            // When & Then - Should not throw exception
            strategy.validateBillingFrequenciesRequest(request)
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    inner class EdgeCasesAndErrorHandling {
        @Test
        @DisplayName("Should throw exception for unsupported additional component frequencies")
        fun shouldThrowExceptionForUnsupportedAdditionalComponentFrequencies() {
            // Given - Use a frequency that's definitely not in the GP mapping
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.ANNUALLY, // Not supported in GP
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When & Then
            assertThrows<InvalidArgumentException> {
                strategy.validateBillingFrequenciesRequest(request)
            }
        }

        @Test
        @DisplayName("Should handle null compensation category gracefully")
        fun shouldHandleNullCompensationCategoryGracefully() {
            // Given
            val request = createBillingFrequenciesRequest(
                compensationCategory = null,
            )

            // When & Then - Should throw exception for null category when treated as additional component
            assertThrows<InvalidArgumentException> {
                strategy.validateBillingFrequenciesRequest(request)
            }
        }
    }

    @Nested
    @DisplayName("Integration Tests")
    inner class IntegrationTests {
        @Test
        @DisplayName("Should integrate correctly with repository for primary salary")
        fun shouldIntegrateCorrectlyWithRepositoryForPrimarySalary() {
            // Given
            val request = createPayScheduleRequest(
                billingFrequency = BillingFrequency.MONTHLY,
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val mockPaySchedules = listOf(
                createMockPaySchedule(frequency = PayScheduleFrequency.MONTHLY),
                createMockPaySchedule(frequency = PayScheduleFrequency.SEMI_MONTHLY),
            )

            every {
                payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                    entityId = 123L,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = false,
                )
            } returns mockPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(mockPaySchedules, result)
            verify(exactly = 1) {
                payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                    entityId = 123L,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = false,
                )
            }
        }

        @Test
        @DisplayName("Should integrate correctly with repository for additional components")
        fun shouldIntegrateCorrectlyWithRepositoryForAdditionalComponents() {
            // Given
            val request = createPayScheduleRequest(
                billingFrequency = BillingFrequency.MONTHLY,
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )
            val mockPaySchedules = listOf(createMockPaySchedule())

            every {
                payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                    entityId = 123L,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = null,
                )
            } returns mockPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(mockPaySchedules, result)
            verify(exactly = 1) {
                payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                    entityId = 123L,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = null,
                )
            }
        }
    }

    // Helper methods for creating test data
    private fun createPayScheduleRequest(
        entityId: Long = 123L,
        countryCode: CountryCode = CountryCode.USA,
        offeringCode: OfferingCode = OfferingCode.GLOBAL_PAYROLL,
        state: String? = "CA",
        billingFrequency: BillingFrequency? = BillingFrequency.MONTHLY,
        grossSalaryPayScheduleFrequency: PayScheduleFrequency? = null,
        compensationCategory: String = "CONTRACT_BASE_PAY",
    ) = GetEligiblePayScheduleRequest(
        entityId = entityId,
        countryCode = countryCode,
        offeringCode = offeringCode,
        state = state,
        billingFrequency = billingFrequency,
        grossSalaryPayScheduleFrequency = grossSalaryPayScheduleFrequency,
        compensationCategory = compensationCategory,
    )

    private fun createBillingFrequenciesRequest(
        entityId: Long = 123L,
        countryCode: CountryCode = CountryCode.USA,
        offeringCode: OfferingCode = OfferingCode.GLOBAL_PAYROLL,
        state: String? = "CA",
        grossSalaryPayScheduleFrequency: PayScheduleFrequency? = null,
        compensationCategory: String? = "CONTRACT_BASE_PAY",
    ) = GetEligibleBillingFrequenciesRequest(
        entityId = entityId,
        countryCode = countryCode,
        offeringCode = offeringCode,
        state = state,
        grossSalaryPayScheduleFrequency = grossSalaryPayScheduleFrequency,
        compensationCategory = compensationCategory,
    )

    private fun createMockPaySchedule(
        id: java.util.UUID = java.util.UUID.randomUUID(),
        frequency: PayScheduleFrequency = PayScheduleFrequency.MONTHLY,
    ): PaySchedule = mockk {
        every { <EMAIL> } returns id
        every { <EMAIL> } returns frequency
    }
}
