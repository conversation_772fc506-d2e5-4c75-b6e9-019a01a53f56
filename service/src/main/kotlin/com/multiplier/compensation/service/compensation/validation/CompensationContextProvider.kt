package com.multiplier.compensation.service.compensation.validation

import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractOffboarding
import com.multiplier.compensation.domain.common.contract.isOffBoarding
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.grpc.client.adapter.ContractOffboardingServiceAdapter
import com.multiplier.compensation.grpc.client.adapter.ContractServiceAdapter
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil.getCountryCode
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil.getOfferingType
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.payschedule.PayScheduleService
import com.multiplier.compensation.service.skeleton.SkeletonService
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CompensationContextProvider(
    private val schemaService: CompensationSchemaService,
    private val payScheduleService: PayScheduleService,
    private val skeletonService: SkeletonService,
    private val contractOffboardingServiceAdapter: ContractOffboardingServiceAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val compensationRepository: CompensationRepository,
) {
    private val sourcesForQueryingActiveCompensations = setOf(
        RequestType.COMPENSATION_SETUP,
        RequestType.COMPENSATION_REVISION_BACKFILL,
        RequestType.COMPENSATION_BACKFILL,
    )

    fun initializeValidationContext(
        entityId: Long,
        source: RequestType,
        validationInputItems: List<ValidationInputItem>,
        contextUseCase: ContextUseCase,
        customParams: Map<String, String>,
        isContractActivationFlow: Boolean = false,
    ): CompensationValidatorContext {
        log.info { "Initializing validation context for entityId [$entityId]" }

        validateRequest(entityId, customParams)

        val inputContractIds = extractContractIdsFromInput(validationInputItems)
        val allContracts = getContracts(entityId, inputContractIds)
        return CompensationValidatorContext(
            entityId = entityId,
            customParams = customParams,
            skeleton = getSkeletonForRequestType(source, entityId, customParams),
            schemaItems = getEntitySchemaItems(entityId),
            defaultCountrySchemas = getCountrySchemas(source, customParams),
            paySchedules = getPaySchedules(entityId, customParams),
            contracts = allContracts,
            offBoardingContracts = getContractOffBoardings(allContracts.values.toSet()),
            // TODO: remove the DB call, fetch the contractIds from the validCompensations.
            activeCompContractIds = getActiveCompensationContractIds(
                source = source,
                entityId = entityId,
                contractIds = inputContractIds,
            ),
            validCompensations = getValidCompensations(
                source = source,
                entityId = entityId,
                contractIds = inputContractIds,
            ),
            contextUseCase = contextUseCase,
            contextSource = source,
            isContractActivationFlow = isContractActivationFlow,
        )
    }

    private fun extractContractIdsFromInput(validationInputItems: List<ValidationInputItem>): Set<Long> {
        log.info { "Extracting contract ids from [${validationInputItems.size}] input records." }
        return validationInputItems
            .flatMap { it.fieldKeyValuePairs }
            .filter { it.key == CommonSkeletonField.CONTRACT_ID.id }
            .mapNotNull { it.value?.toLong() }
            .toSet()
    }

    private fun getSkeletonForRequestType(
        requestType: RequestType,
        entityId: Long,
        customParams: Map<String, String>,
    ): Skeleton {
        log.info { "Fetching [$requestType] skeleton for entityId [$entityId]" }
        return skeletonService.getSkeleton(entityId, requestType.toSkeletonType(), customParams)
    }

    private fun RequestType.toSkeletonType() = when (this) {
        RequestType.COMPENSATION_SETUP -> SkeletonType.COMPENSATION_SETUP
        RequestType.COMPENSATION_REVISION -> SkeletonType.COMPENSATION_REVISION
        RequestType.PAY_SUPPLEMENT -> SkeletonType.PAY_SUPPLEMENT
        RequestType.DEDUCTION -> SkeletonType.DEDUCTION
        RequestType.COMPENSATION_BACKFILL -> SkeletonType.COMPENSATION_BACKFILL
        RequestType.COMPENSATION_REVISION_BACKFILL -> SkeletonType.COMPENSATION_REVISION_BACKFILL
    }

    private fun getEntitySchemaItems(entityId: Long): Map<CompensationSchema, Map<String, CompensationSchemaItem>> {
        log.info { "Fetching schema items for entity [$entityId]" }
        val activeSchemaList = schemaService.getSchemaByEntityIds(listOf(entityId), true)
        if (activeSchemaList.isEmpty()) {
            log.info { "No active schema found for entityId [$entityId]" }
            return emptyMap()
        }
        val schemaItemMap = activeSchemaList.associateWith { schema ->
            schema.schemaItems.associateBy { it.componentName }
        }
        log.info { "Found [${schemaItemMap.size}] schema items for entityId [$entityId]" }
        return schemaItemMap
    }

    private fun getCountrySchemas(
        requestType: RequestType,
        customParams: Map<String, String>,
    ): List<CompensationSchema> {
        if (getOfferingType(customParams) == OfferingType.EOR) {
            log.info { "Fetching schema items for entity [$customParams]" }
            val countrySchemaList = getCountryCode(customParams)?.let {
                schemaService.getDefaultCountrySchemas(
                    countryCode = it,
                    excludeInactiveItems = requestType.toSkeletonType().excludeInactiveConfigs,
                )
            }.orEmpty()
            log.info { "Found [${countrySchemaList.size}] schemas for entityId [$customParams]" }
            return countrySchemaList
        }
        return emptyList()
    }

    private fun getPaySchedules(
        entityId: Long,
        customParams: Map<String, String>,
    ): Map<String, PaySchedule> {
        log.info { "Fetching active pay schedules for entityId: $entityId" }

        val offeringType = getOfferingType(customParams)
        val countryCode = getCountryCode(customParams)
        val activePaySchedules = payScheduleService.getPaySchedules(
            entityId = entityId,
            offeringType = offeringType,
            countryCode = countryCode,
            false,
        )
        val paySchedulesByName = activePaySchedules.associateBy { it.name }
        log.info { "Fetched [${paySchedulesByName.size}] active pay schedules for entityId [$entityId]" }
        return paySchedulesByName
    }

    private fun getContracts(
        entityId: Long,
        contractIds: Set<Long>,
    ): Map<Long, Contract> = runBlocking {
        if (contractIds.isEmpty()) {
            log.info { "No contract ids were provided for entity [$entityId]." }
            return@runBlocking emptyMap()
        }
        log.info { "Fetching contract details for entityId [$entityId] and contractIds $contractIds" }
        return@runBlocking contractServiceAdapter.getContractsByIds(
            contractIds = contractIds,
        ).associateBy { it.id }
    }

    private fun getContractOffBoardings(contracts: Set<Contract>): Map<Long, ContractOffboarding> = runBlocking {
        val offBoardingContracts = contracts.filter { it.isOffBoarding }.map { it.id }.toSet()

        if (offBoardingContracts.isEmpty()) {
            log.info { "No contract ids were provided." }
            return@runBlocking emptyMap()
        }
        log.info { "Fetching offboarding details for contracts $offBoardingContracts" }

        return@runBlocking contractOffboardingServiceAdapter.getContractOffboardings(offBoardingContracts)
            .associateBy { it.contractId }
    }

    private fun getActiveCompensationContractIds(
        source: RequestType,
        entityId: Long,
        contractIds: Set<Long>,
    ): Set<Long> {
        if (source !in sourcesForQueryingActiveCompensations) {
            return emptySet()
        }

        log.info { "Filtering input contractIds with valid compensations for the entityId [$entityId]" }
        return compensationRepository.fetchValidContractIdsForEntity(entityId, contractIds).toSet()
    }

    private fun getValidCompensations(
        source: RequestType,
        entityId: Long,
        contractIds: Set<Long>,
    ): Map<Long, List<Compensation>> {
        if (source == RequestType.COMPENSATION_SETUP) {
            return emptyMap()
        }

        log.info { "Fetching valid compensations for the entityId [$entityId]" }
        return compensationRepository
            .fetchAllValidCompensationsForContractIds(entityId, contractIds)
            .groupBy { it.contractId }
    }

    private fun validateRequest(
        entityId: Long,
        customParams: Map<String, String>,
    ) {
        log.info { "Validating compensation request with entity Id [$entityId]." }

        val offeringType = getOfferingType(customParams)
        if (offeringType == OfferingType.EOR) {
            requireCondition(
                customParams.containsKey(BulkJsonCustomParamsUtil.BulkCustomKeys.COUNTRY_CODE),
                ValidationErrorCode.MissingCountryCodeInRequest,
                "Country code is mandatory for EOR offering type",
            )
        }
        requireCondition(
            entityId > 0,
            ValidationErrorCode.InvalidEntityId,
            "Invalid Entity Id",
        )
    }
}
