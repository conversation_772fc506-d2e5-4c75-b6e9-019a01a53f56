package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensationitem.dto.AdjustedItems
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import com.multiplier.compensation.service.compensationitem.update.util.extractNewImage
import com.multiplier.compensation.service.compensationitem.update.util.extractNewRecord
import com.multiplier.compensation.service.compensationitem.update.util.extractOldImage
import com.multiplier.compensation.service.compensationitem.update.util.updateContextWithAdjustments
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isCancellationArrear
import com.multiplier.compensation.service.compensationitem.util.CompensationItemUtil.isProcessedItem
import com.multiplier.compensation.service.compensationitem.util.CompensationUtil.isNewCompensationEligibleForItemGeneration
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDate

private val log = KotlinLogging.logger {}

/*
 * This adjuster adjusts the compensation items when a new compensation entry is added with changes
 * to the billing rate and/or start date, end date, no. of installments, and an active compensation record exists.
 *
 * Process:
 * 1. Invalidate unprocessed items extending beyond the updated end date of the existing record:
 *    a. Marks unprocessed items as aborted.
 * 2. Invalidates processed items extending beyond the end date of the new record:
 *    a. Supplements processed items that extend beyond the newRecord's end date with cancellation arrears.
 * 2. Generate new compensation items for the newRecord
 *    a. Marks the ones that have corresponding processed items for the existing compensation entry, as arrears.
 */
@Qualifier("billingRateUpdateAdjuster")
@Service
class BillingRateUpdateAdjuster(
    itemGenerationManager: CompensationItemGenerationManager,
    itemUpdateManager: CompensationItemUpdateManager,
    cancellationArrearManager: CancellationArrearManager,
    spillOverItemManager: SpillOverItemManager,
    consolidationManager: CompensationItemConsolidationManager,
    @Value("\${core.scheduler.compensation-item-generation.generation-period-in-days}")
    private val generationPeriodInDays: Long,
) : CompensationAdjuster(
        itemGenerationManager,
        itemUpdateManager,
        cancellationArrearManager,
        spillOverItemManager,
        consolidationManager,
    ) {
    override fun adjust(context: CompensationUpdateContext): CompensationUpdateContext {
        validateContext(context)

        return context.updateContextWithAdjustments(
            AdjustedItems(
                oldImageItems = generateOldImageItems(context),
                newImageItems = generateNewImageItems(context),
                newRecordItems = generateNewRecordItems(context),
            ),
        )
    }

    override fun generateOldImageItems(context: CompensationUpdateContext): List<CompensationItem> {
        val (newCancellationArrears, abortedCancellationArrears) = generateCancellationArrearsForEligibleItems(context)
        log.info { "Generated [${newCancellationArrears.count()}] CancellationArrears." }

        val abortedItems = markEligibleItemsAsAborted(context) + abortedCancellationArrears
        log.info { "Marked [${abortedItems.count()}] existing items as aborted." }
        return abortedItems + newCancellationArrears
    }

    override fun generateNewImageItems(context: CompensationUpdateContext): List<CompensationItem> =
        generateNewImageTruncatedItem(context)?.let { listOf(it) }.orEmpty()

    /*
     * - If the newRecord is eligible for item generation
     *   - generate new items
     *   - mark the newly generated items that overlap with the existing processed items as adjustment arrears.
     * - else skip new item generation.
     */
    override fun generateNewRecordItems(context: CompensationUpdateContext): List<CompensationItem> {
        val newRecord = extractNewRecord(context)
        val newRecordItems = if (isNewCompensationEligibleForItemGeneration(newRecord, generationPeriodInDays)) {
            super.generateItems(
                compensation = newRecord,
                toDate = LocalDate.now().plusDays(generationPeriodInDays),
            )
        } else {
            emptyList()
        }

        return super.markEligibleNewItemsAsAdjustmentArrears(
            newItems = newRecordItems,
            existingItems = context.existingItems,
            triggerCompensation = newRecord,
        )
    }

    /*
     * - When the newRecord replaces an existing compensation,
     *   - the compensation service marks the status of the newImage as ABORTED.
     *   - all existing unprocessed items should be considered to be marked as ABORTED.
     * - If the newRecord has a start date later than the existing compensation's start date,
     *   - the compensation service adjusts the newImage’s end date accordingly.
     *   - all existing items with a start date later than the newImage’s end date
     *     should be considered to be marked as ABORTED.
     *   - additionally, any existing item overlapping with the newRecord's date range and extending before the newRecord's start date
     *     should be considered to be marked as ABORTED.
     */
    private fun markEligibleItemsAsAborted(context: CompensationUpdateContext): List<CompensationItem> {
        val newImage = extractNewImage(context)
        val newRecord = extractNewRecord(context)

        if (newImage.status == CompensationStatus.ABORTED) {
            val affectedItems = context.existingItems.filter { !isCancellationArrear(it) }
            return super.markEligibleItemsAsAborted(
                triggerCompensation = newRecord,
                affectedItems = affectedItems,
            )
        }

        val newImageEndDate = requireNotNull(newImage.endDate) { "New image end date should not be null." }
        val affectedItems = context.existingItems
            .filter { it.startDate.isAfter(newImageEndDate) && !isCancellationArrear(it) }
        val abortedItems = super.markEligibleItemsAsAborted(
            triggerCompensation = newRecord,
            affectedItems = affectedItems,
        )

        val spillOverItem = super.getSpillOverItemAtCompensationStart(newRecord, context.existingItems)
        val spillOverAbortedItem = spillOverItem?.let {
            super.markEligibleItemAsAborted(
                triggerCompensation = newRecord,
                affectedItem = it,
            )
        }
        return abortedItems + listOfNotNull(spillOverAbortedItem)
    }

    /*
     * - All existing processed items with a start date later than the newRecord’s end date
     *    should be considered for cancellation arrear generation.
     * - Additionally, any existing item overlapping with the newRecord's date range and extending beyond the newRecord end date
     *     should be considered for cancellation arrear generation.
     */
    private fun generateCancellationArrearsForEligibleItems(
        context: CompensationUpdateContext,
    ): Pair<List<CompensationItem>, List<CompensationItem>> {
        val newImage = extractNewImage(context)
        val newRecord = extractNewRecord(context)

        val (initialArrears, initialToBeAbortedArrears) = generateInitialCancellationArrears(
            context,
            newImage,
            newRecord,
        )
        val (finalArrears, finalToBeAbortedArrears) = handleSpillOverItemAtNewRecordEnd(
            context,
            newImage,
            newRecord,
            initialArrears,
            initialToBeAbortedArrears,
        )
        val abortedArrears = super.markEligibleItemsAsAborted(
            triggerCompensation = newImage,
            affectedItems = finalToBeAbortedArrears,
        )

        return Pair(finalArrears, abortedArrears)
    }

    private fun generateInitialCancellationArrears(
        context: CompensationUpdateContext,
        newImage: Compensation,
        newRecord: Compensation,
    ): Pair<List<CompensationItem>, List<CompensationItem>> {
        val affectedItems = newRecord.endDate?.let { endDate ->
            context.existingItems.filter { it.startDate.isAfter(endDate) }
        }.orEmpty()

        if (affectedItems.isEmpty()) return Pair(emptyList(), emptyList())

        return super.generateCancellationArrearsForEligibleItems(
            associatedCompensation = newImage,
            triggerCompensation = newRecord,
            affectedItems = affectedItems,
            existingItems = context.existingItems,
        )
    }

    /*
     * If a spillOverItem is found, consider it for cancellation arrear generation.
     * If not, return the initial cancellation arrears.
     */
    private fun handleSpillOverItemAtNewRecordEnd(
        context: CompensationUpdateContext,
        newImage: Compensation,
        newRecord: Compensation,
        initialArrears: List<CompensationItem>,
        initialToBeAbortedArrears: List<CompensationItem>,
    ): Pair<List<CompensationItem>, List<CompensationItem>> {
        val spillOverItem = super.getSpillOverItemAtCompensationEnd(
            compensation = newRecord,
            existingItems = context.existingItems,
        ) ?: return Pair(initialArrears, initialToBeAbortedArrears)
        val dateAdjustedSpillOverItem = getDateAdjustedSpillOverItemAtNewRecordEnd(newRecord, spillOverItem)
        val (newSpillOverArrear, toBeAbortedArrears) = generateSpillOverArrear(
            newImage,
            newRecord,
            dateAdjustedSpillOverItem,
            context,
        )

        return Pair(
            initialArrears + listOfNotNull(newSpillOverArrear),
            initialToBeAbortedArrears + toBeAbortedArrears,
        )
    }

    /*
     * The spillOverItem needs to be date adjusted for cancellation arrear generation.
     * The corresponding arrear will have a start date = updated end date (newImage end date) + 1 days.
     */
    private fun getDateAdjustedSpillOverItemAtNewRecordEnd(
        newRecord: Compensation,
        spillOverItem: CompensationItem,
    ): CompensationItem {
        val newEndDate = requireNotNull(newRecord.endDate) { "New record's end date should not be null." }
        return spillOverItem.copy(
            startDate = newEndDate.plusDays(1),
        )
    }

    private fun generateSpillOverArrear(
        newImage: Compensation,
        newRecord: Compensation,
        spillOverItem: CompensationItem,
        context: CompensationUpdateContext,
    ): Pair<CompensationItem?, List<CompensationItem>> = super.generateCancellationArrearForEligibleItem(
        associatedCompensation = newImage,
        triggerCompensation = newRecord,
        affectedItem = spillOverItem,
        existingItems = context.existingItems,
    )

    /*
     * - When the newRecord replaces an existing compensation,
     *   - the compensation service marks the status of the newImage as ABORTED.
     *   - all existing unprocessed items are marked as ABORTED.
     *   - hence no items need to be considered to be retained.
     * - If the newRecord has a start date later than the existing compensation's start date,
     *   - the compensation service adjusts the newImage’s end date accordingly.
     *   - all existing unprocessed items with a start date later than the newImage’s end date are marked as ABORTED.
     *   - additionally, any existing unprocessed item overlapping with the newRecord's date range and extending before the newRecord's start date
     *     should be retained for the date range overlapping with the newImage.
     */
    private fun generateNewImageTruncatedItem(context: CompensationUpdateContext): CompensationItem? {
        val newImage = extractNewImage(context)
        val newRecord = extractNewRecord(context)

        if (newImage.status == CompensationStatus.ABORTED) return null

        val spillOverItem = super.getSpillOverItemAtCompensationStart(
            compensation = newRecord,
            existingItems = context.existingItems,
        )
        return spillOverItem?.takeIf { !isProcessedItem(it) }?.let {
            log.info {
                "Generating truncated item corresponding to old image's unprocessed spillover item," +
                    " which was aborted."
            }
            super.generateTruncatedItem(
                compensation = newImage,
                affectedItem = spillOverItem,
                schemaItem = context.schemaItem,
            )
        }
    }

    override fun validateContext(context: CompensationUpdateContext) {
        val oldImage = extractOldImage(context)
        requireNotNull(context.newImage) { "New image should not be null." }
        val newRecord = extractNewRecord(context)
        require(context.parentUpdateContext == null) { "Parent update context should be null." }
        require(!newRecord.startDate.isBefore(oldImage.startDate)) {
            "Invalid request: Attempted to modify the start date to an earlier value."
        }
    }
}
