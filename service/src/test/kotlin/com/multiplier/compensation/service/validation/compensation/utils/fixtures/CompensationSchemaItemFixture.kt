package com.multiplier.compensation.service.validation.compensation.utils.fixtures

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import java.time.LocalDateTime
import java.util.UUID

fun compensationSchemaItemFixture(
    id: UUID = UUID.randomUUID(),
    schemaId: UUID = UUID.randomUUID(),
    componentName: String = "Base Salary",
    category: String = "CONTRACT_BASE_PAY",
    isTaxable: Boolean = true,
    isProrated: Boolean = true,
    isFixed: Boolean = true,
    isActive: Boolean = true,
    isMandatory: Boolean = true,
    isPartOfBasePay: Boolean = false,
    createdOn: LocalDateTime = LocalDateTime.now(),
    createdBy: Long = -1,
    updatedOn: LocalDateTime = LocalDateTime.now(),
    updatedBy: Long = -1,
    label: String = "Base Salary",
    itemType: ItemType = ItemType.INPUT,
    validation: String? = null,
    calculation: String? = null,
    billingRateType: BillingRateType = BillingRateType.VALUE,
    isOvertimeEligible: Boolean = false,
    description: String = "Test component description",
    billingFrequency: BillingFrequency = BillingFrequency.MONTHLY,
    payScheduleId: UUID? = null,
    currency: String = "USD",
    isPartOfCtc: Boolean = true,
) = CompensationSchemaItem(
    id = id,
    schemaId = schemaId,
    componentName = componentName,
    category = category,
    isTaxable = isTaxable,
    isProrated = isProrated,
    isFixed = isFixed,
    isActive = isActive,
    isMandatory = isMandatory,
    isPartOfBasePay = isPartOfBasePay,
    createdOn = createdOn,
    createdBy = createdBy,
    updatedOn = updatedOn,
    updatedBy = updatedBy,
    label = label,
    itemType = itemType,
    validation = validation,
    calculation = calculation,
    billingRateType = billingRateType,
    isOvertimeEligible = isOvertimeEligible,
    description = description,
    billingFrequency = billingFrequency,
    payScheduleId = payScheduleId,
    currency = currency,
    isPartOfCtc = isPartOfCtc,
)
