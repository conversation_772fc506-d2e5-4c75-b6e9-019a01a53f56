package com.multiplier.compensation.domain.compensationitem

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class CompensationItemTest {
    @Test
    fun `should create valid CompensationItem with all fields set`() {
        // Given
        val id = UUID.randomUUID()
        val companyId = 1L
        val entityId = 2L
        val contractId = 3L
        val compensationId = UUID.randomUUID()
        val schemaItemId = UUID.randomUUID()
        val category = "BASE PAY"
        val currency = "USD"
        val billingRateType = BillingRateType.VALUE
        val billingRate = 5000.0
        val billingFrequency = BillingFrequency.MONTHLY
        val payScheduleId = UUID.randomUUID()
        val compensationStartDate = LocalDate.of(2025, 1, 1)
        val compensationEndDate = LocalDate.of(2025, 12, 31)
        val compensationStatus = CompensationStatus.NEW
        val startDate = LocalDate.of(2025, 1, 1)
        val endDate = LocalDate.of(2025, 12, 31)
        val isInstallment = false
        val noOfInstallments = 12
        val currentInstallment = 6
        val cutOffDate = LocalDate.of(2025, 6, 30)
        val expectedPayDate = LocalDate.of(2025, 7, 15)
        val calculatedAmount = 10000.0
        val payDate = LocalDate.of(2025, 7, 20)
        val previousId = UUID.randomUUID()
        val status = CompensationItemStatus.NEW
        val isArrear = false
        val arrearOf = null
        val arrearTriggerReference = null
        val updateTriggerReference = null
        val createdOn = LocalDateTime.of(2025, 1, 1, 12, 0)
        val createdBy = -1L
        val updatedOn = LocalDateTime.of(2025, 6, 1, 12, 0)
        val updatedBy = -1L

        // When
        val compensationItem = CompensationItem(
            id,
            companyId,
            entityId,
            contractId,
            compensationId,
            schemaItemId,
            category,
            currency,
            billingRateType,
            billingRate,
            billingFrequency,
            payScheduleId,
            compensationStartDate,
            compensationEndDate,
            compensationStatus,
            startDate,
            endDate,
            isInstallment,
            noOfInstallments,
            currentInstallment,
            cutOffDate,
            expectedPayDate,
            calculatedAmount,
            payDate,
            previousId,
            status,
            isArrear,
            arrearOf,
            arrearTriggerReference,
            updateTriggerReference,
            createdOn,
            createdBy,
            updatedOn,
            updatedBy,
        )

        // Then
        assertEquals(id, compensationItem.id)
        assertEquals(companyId, compensationItem.companyId)
        assertEquals(entityId, compensationItem.entityId)
        assertEquals(contractId, compensationItem.contractId)
        assertEquals(compensationId, compensationItem.compensationId)
        assertEquals(schemaItemId, compensationItem.schemaItemId)
        assertEquals(category, compensationItem.category)
        assertEquals(currency, compensationItem.currency)
        assertEquals(billingRateType, compensationItem.billingRateType)
        assertEquals(billingRate, compensationItem.billingRate)
        assertEquals(billingFrequency, compensationItem.billingFrequency)
        assertEquals(payScheduleId, compensationItem.payScheduleId)
        assertEquals(compensationStartDate, compensationItem.compensationStartDate)
        assertEquals(compensationEndDate, compensationItem.compensationEndDate)
        assertEquals(compensationStatus, compensationItem.compensationStatus)
        assertEquals(startDate, compensationItem.startDate)
        assertEquals(endDate, compensationItem.endDate)
        assertEquals(isInstallment, compensationItem.isInstallment)
        assertEquals(noOfInstallments, compensationItem.noOfInstallments)
        assertEquals(currentInstallment, compensationItem.currentInstallment)
        assertEquals(cutOffDate, compensationItem.cutOffDate)
        assertEquals(expectedPayDate, compensationItem.expectedPayDate)
        assertEquals(calculatedAmount, compensationItem.calculatedAmount)
        assertEquals(payDate, compensationItem.payDate)
        assertEquals(previousId, compensationItem.previousId)
        assertEquals(status, compensationItem.status)
        assertEquals(isArrear, compensationItem.isArrear)
        assertNull(compensationItem.arrearOf)
        assertNull(compensationItem.arrearTriggerReference)
        assertNull(compensationItem.updateTriggerReference)
        assertEquals(createdOn, compensationItem.createdOn)
        assertEquals(createdBy, compensationItem.createdBy)
        assertEquals(updatedOn, compensationItem.updatedOn)
        assertEquals(updatedBy, compensationItem.updatedBy)
    }

    @Test
    fun `should create CompensationItem with nullable fields set to null`() {
        // Given
        val compensationItem = CompensationItem(
            id = UUID.randomUUID(),
            companyId = 1L,
            entityId = 2L,
            contractId = 3L,
            compensationId = UUID.randomUUID(),
            schemaItemId = UUID.randomUUID(),
            category = null,
            currency = "USD",
            billingRateType = BillingRateType.VALUE,
            billingRate = 5000.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            compensationStartDate = LocalDate.of(2025, 1, 1),
            compensationEndDate = null,
            compensationStatus = CompensationStatus.NEW,
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 12, 31),
            isInstallment = true,
            noOfInstallments = null,
            currentInstallment = null,
            cutOffDate = null,
            expectedPayDate = LocalDate.of(2025, 7, 15),
            calculatedAmount = null,
            payDate = null,
            previousId = null,
            status = CompensationItemStatus.NEW,
            isArrear = false,
            arrearOf = null,
            arrearTriggerReference = null,
            updateTriggerReference = null,
            createdOn = LocalDateTime.of(2025, 1, 1, 12, 0),
            createdBy = -1L,
            updatedOn = LocalDateTime.of(2025, 6, 1, 12, 0),
            updatedBy = -1L,
        )

        // Then
        assertNull(compensationItem.category)
        assertNull(compensationItem.compensationEndDate)
        assertNull(compensationItem.noOfInstallments)
        assertNull(compensationItem.currentInstallment)
        assertNull(compensationItem.cutOffDate)
        assertNull(compensationItem.calculatedAmount)
        assertNull(compensationItem.payDate)
        assertNull(compensationItem.previousId)
        assertNull(compensationItem.arrearOf)
        assertNull(compensationItem.arrearTriggerReference)
        assertNull(compensationItem.updateTriggerReference)
    }

    @Test
    fun `should use default values for createdOn and updatedOn`() {
        // Given
        val compensationItem = CompensationItem(
            id = UUID.randomUUID(),
            companyId = 1L,
            entityId = 2L,
            contractId = 3L,
            compensationId = UUID.randomUUID(),
            schemaItemId = UUID.randomUUID(),
            category = "BASE PAY",
            currency = "USD",
            billingRateType = BillingRateType.VALUE,
            billingRate = 5000.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = UUID.randomUUID(),
            compensationStartDate = LocalDate.of(2025, 1, 1),
            compensationStatus = CompensationStatus.NEW,
            startDate = LocalDate.of(2025, 1, 1),
            endDate = LocalDate.of(2025, 12, 31),
            isInstallment = false,
            noOfInstallments = 12,
            currentInstallment = 6,
            expectedPayDate = LocalDate.of(2025, 7, 15),
            calculatedAmount = 10000.0,
            status = CompensationItemStatus.NEW,
            isArrear = false,
        )

        // Then - Verify
        assertNotNull(compensationItem.createdOn, "createdOn should be initialized by default")
        assertNotNull(compensationItem.updatedOn, "updatedOn should be initialized by default")

        assertNull(compensationItem.compensationEndDate, "compensationEndDate should be null by default")
        assertNull(compensationItem.cutOffDate, "cutOffDate should be null by default")
        assertNull(compensationItem.payDate, "payDate should be null by default")
        assertNull(compensationItem.previousId, "previousId should be null by default")
        assertNull(compensationItem.arrearOf, "arrearOf should be null by default")
        assertNull(compensationItem.updateTriggerReference, "updateTriggerReference should be null by default")
        assertNull(compensationItem.arrearTriggerReference, "arrearTriggerReference should be null by default")
    }
}
