package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateTypeValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationDraftFixture
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationSchemaItemFixture
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class BillingRateTypeValidatorTest {
    private lateinit var validator: BillingRateTypeValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>

    private val allowanceSchemaItemId = UUID.randomUUID()
    private val basePaySchemaItemId = UUID.randomUUID()
    private val ctcSchemaItemId = UUID.randomUUID()
    private val inputSchemaItemId = UUID.randomUUID()
    private val nonInputSchemaItemId = UUID.randomUUID()

    private val contractIdForDrafts = 50000L

    private val billingRatePercentageTypeKeyValuePair = KeyValuePair(
        key = CompensationSkeletonField.BILLING_RATE_TYPE.id,
        value = BillingRateType.BASE_PAY_PERCENTAGE.description,
    )

    private val billingRateValueTypeKeyValuePair = KeyValuePair(
        key = CompensationSkeletonField.BILLING_RATE_TYPE.id,
        value = BillingRateType.VALUE.description,
    )

    private val contractIdKeyValuePair = KeyValuePair(
        key = CommonSkeletonField.CONTRACT_ID.id,
        value = contractIdForDrafts.toString(),
    )

    @BeforeEach
    fun setUp() {
        validator = BillingRateTypeValidator()

        val allowanceSchemaItem = compensationSchemaItemFixture(
            id = allowanceSchemaItemId,
            category = "CONTRACT_ALLOWANCE",
            componentName = "Meal Allowance",
        ).copy(
            itemType = ItemType.INPUT,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
        )

        val basePaySchemaItem = compensationSchemaItemFixture(
            id = basePaySchemaItemId,
            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            componentName = "Base Pay",
        ).copy(
            itemType = ItemType.INPUT,
            billingRateType = BillingRateType.VALUE,
        )

        val ctcSchemaItem = compensationSchemaItemFixture(
            id = ctcSchemaItemId,
            category = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            componentName = "CTC",
        ).copy(
            itemType = ItemType.INPUT,
            billingRateType = BillingRateType.VALUE,
        )
        val inputSchemaItem = compensationSchemaItemFixture(
            id = inputSchemaItemId,
            category = "OTHER_CATEGORY",
            componentName = "Input Component",
        ).copy(
            itemType = ItemType.INPUT,
            billingRateType = BillingRateType.VALUE,
        )
        val nonInputSchemaItem = compensationSchemaItemFixture(
            id = nonInputSchemaItemId,
            category = "OTHER_CATEGORY",
            componentName = "Non Input Component",
        ).copy(
            itemType = ItemType.CALCULATED,
            billingRateType = BillingRateType.VALUE,
        )

        context = mockk(relaxed = true) {
            every { getCompensationSchemaItem(allowanceSchemaItemId) } returns allowanceSchemaItem
            every { getCompensationSchemaItem(basePaySchemaItemId) } returns basePaySchemaItem
            every { getCompensationSchemaItem(ctcSchemaItemId) } returns ctcSchemaItem
            every { getCompensationSchemaItem(inputSchemaItemId) } returns inputSchemaItem
            every { getCompensationSchemaItem(nonInputSchemaItemId) } returns nonInputSchemaItem
            every { contextUseCase } returns ContextUseCase.VALIDATE
            every { contextSource } returns RequestType.COMPENSATION_SETUP
        }

        collector =
            ValidationDataCollector(
                drafts = mutableMapOf(),
                rowValidationResult = mutableMapOf(),
                inputPlusDerivedRows = emptyMap(),
            )
    }

    @Test
    fun `validate returns false when draft is null`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )
        assertFalse(validator.validate(input, context, collector))
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `validate returns false when billingRateType is null`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = basePaySchemaItemId,
        ).copy(billingRateType = null)
        assertFalse(validator.validate(input, context, collector))
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `validate returns false when schemaItemId is null`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            billingRateType = BillingRateType.VALUE,
        ).copy(schemaItemId = null)
        assertFalse(validator.validate(input, context, collector))
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `validate returns false when schema item is not found`() {
        val unknownSchemaItemId = UUID.randomUUID()
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = unknownSchemaItemId,
            billingRateType = BillingRateType.VALUE,
        )
        every { context.getCompensationSchemaItem(any()) } returns null

        assertFalse(validator.validate(input, context, collector))
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `validate returns false when dependent billing rate type is used on parent component`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                billingRatePercentageTypeKeyValuePair,
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = basePaySchemaItemId,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            schemaCategory = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        )
        assertFalse(validator.validate(input, context, collector))
        assertTrue(
            collector.rowValidationResult[input.id]?.any {
                it.message.contains("is not allowed on the parent component")
            } == true,
        )
    }

    @Test
    fun `validate returns false when dependent billing rate type has no parent component`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                billingRatePercentageTypeKeyValuePair,
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            schemaCategory = "CONTRACT_ALLOWANCE",
        )
        // No base pay draft in collector
        assertFalse(validator.validate(input, context, collector))
        assertTrue(
            collector.rowValidationResult[input.id]?.any {
                it.message.contains("is not allowed without a parent component")
            } == true,
        )
    }

    @Test
    fun `should return false when billing rate type does not match schema item billing rate type for INPUT items`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId, // Schema item expects BASE_PAY_PERCENTAGE
            billingRateType = BillingRateType.VALUE, // But draft has VALUE
            schemaCategory = "CONTRACT_ALLOWANCE",
        )

        val result = validator.validate(input, context, collector)

        assertFalse(result)
        assertTrue {
            collector.rowValidationResult[input.id]?.any {
                it.message.contains("should match the schema item's billing rate type")
            } == true
        }
    }

    @Test
    fun `should return true when dependent billing rate type has valid parent component`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(),
        )
        collector.drafts["2"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            schemaCategory = "CONTRACT_ALLOWANCE",
        )
        // Add base pay component as parent
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = basePaySchemaItemId,
            billingRateType = BillingRateType.VALUE,
            schemaCategory = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        )

        val result = validator.validate(input, context, collector)

        assertTrue(result)
    }

    @Test
    fun `validate returns true when billing rate type matches schema item billing rate type for INPUT items`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                billingRateValueTypeKeyValuePair,
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = inputSchemaItemId, // This schema item has billingRateType = VALUE
            billingRateType = BillingRateType.VALUE, // Draft also has VALUE
            schemaCategory = "OTHER_CATEGORY",
        )
        assertTrue(validator.validate(input, context, collector))
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `validate returns true for CTC dependent billing rate type with valid parent`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CompensationSkeletonField.BILLING_RATE_TYPE.id,
                    value = BillingRateType.CTC_PERCENTAGE.description,
                ),
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["2"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.CTC_PERCENTAGE,
            schemaCategory = "CONTRACT_ALLOWANCE",
        )
        // Add CTC draft as parent
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = ctcSchemaItemId,
            billingRateType = BillingRateType.VALUE,
            schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
        )
        assertTrue(validator.validate(input, context, collector))
        assertTrue(collector.rowValidationResult[input.id].isNullOrEmpty())
    }

    @Test
    fun `validate returns true when BASE_PAY_PERCENTAGE is used on EMPLOYEE_CONTRIBUTION category`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                billingRatePercentageTypeKeyValuePair,
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            schemaCategory = CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION,
        )
        // Add parent base pay draft
        collector.drafts["2"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = basePaySchemaItemId,
            billingRateType = BillingRateType.VALUE,
            schemaCategory = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        )
        assertTrue { validator.validate(input, context, collector) }
    }

    @Test
    fun `validate returns true when BASE_PAY_PERCENTAGE is used on EMPLOYER_CONTRIBUTION category`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                billingRatePercentageTypeKeyValuePair,
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            schemaCategory = CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION,
        )
        // Add parent base pay draft
        collector.drafts["2"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = basePaySchemaItemId,
            billingRateType = BillingRateType.VALUE,
            schemaCategory = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        )
        assertTrue { validator.validate(input, context, collector) }
    }

    @Test
    fun `validate returns false when BASE_PAY_PERCENTAGE is used on invalid category`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                billingRatePercentageTypeKeyValuePair,
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            schemaCategory = CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
        )
        // Add parent base pay draft
        collector.drafts["2"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = basePaySchemaItemId,
            billingRateType = BillingRateType.VALUE,
            schemaCategory = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        )
        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]?.any {
                it.message.contains("is not allowed on this category")
            } == true
        }
    }

    @Test
    fun `validate returns false when CTC_PERCENTAGE is used on its parent component`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CompensationSkeletonField.BILLING_RATE_TYPE.id,
                    value = BillingRateType.CTC_PERCENTAGE.description,
                ),
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.CTC_PERCENTAGE,
            schemaCategory = CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
        )
        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]?.any {
                it.message.contains("is not allowed on the parent component")
            } == true
        }
    }

    @Test
    fun `validate returns false when BASE_PAY_DAYS is used without parent component`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CompensationSkeletonField.BILLING_RATE_TYPE.id,
                    value = BillingRateType.BASE_PAY_DAYS.description,
                ),
                contractIdKeyValuePair,
            ),
        )
        collector.drafts["1"] = compensationDraftFixture(
            contractId = contractIdForDrafts,
            schemaItemId = allowanceSchemaItemId,
            billingRateType = BillingRateType.BASE_PAY_DAYS,
            schemaCategory = CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
        )
        // No parent base pay draft added
        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]?.any {
                it.message.contains("is not allowed without a parent component")
            } == true
        }
    }
}
