package com.multiplier.compensation.grpc.server.service.common

import com.fasterxml.uuid.Generators
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.CompensationInputEnriched
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import java.time.LocalDate

fun compensationInputEnrichedFixture(status: CompensationInputStatus) = listOf(
    CompensationInputEnriched(
        id = Generators.timeBasedGenerator().generate(),
        companyId = 1L,
        entityId = 1L,
        contractId = 1L,
        schemaItemId = Generators.timeBasedGenerator().generate(),
        category = "BASE_PAY",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 1000.0,
        billingFrequency = BillingFrequency.MONTHLY,
        startDate = LocalDate.now(),
        isInstallment = false,
        noOfInstallments = null,
        requestType = RequestType.COMPENSATION_SETUP,
        requestId = "requestId",
        status = status,
        payScheduleName = "Monthly-1",
        payScheduleFrequency = PayScheduleFrequency.MONTHLY,
        schemaComponentName = "BASE_PAY",
        compensationName = "Base Pay",
        compensationCategory = "BASE_PAY",
        isTaxable = true,
        isFixed = true,
        isProrated = true,
        isMandatory = true,
        isPartOfBasePay = true,
        label = "Base Pay",
        isOvertimeEligible = true,
        isPartOfCtc = true,
    ),
)
