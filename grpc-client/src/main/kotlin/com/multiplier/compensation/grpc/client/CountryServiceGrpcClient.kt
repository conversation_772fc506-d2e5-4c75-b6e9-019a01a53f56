package com.multiplier.compensation.grpc.client

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.exception.DownstreamServiceException
import com.multiplier.compensation.domain.common.exception.config.DownstreamServiceErrorCode
import com.multiplier.compensation.grpc.client.adapter.CountryServiceAdapter
import com.multiplier.compensation.grpc.client.mapper.toGrpcCountryCode
import com.multiplier.country.schema.Country
import com.multiplier.country.schema.CountryServiceGrpc
import io.github.oshai.kotlinlogging.KotlinLogging
import io.grpc.StatusRuntimeException
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
@Primary
@Profile("!(test|local)")
class CountryServiceGrpcClient(
    private val stub: CountryServiceGrpc.CountryServiceBlockingStub,
) : CountryServiceAdapter {
    override suspend fun getCountryNamesByCodes(countryCodes: Set<CountryCode>): Map<CountryCode, String> {
        try {
            log.info { "Fetching country names for country codes: $countryCodes" }
            val request = Country.GetCountryNamesByCodesRequest.newBuilder()
                .addAllCountryCodes(countryCodes.map { it.toGrpcCountryCode() })
                .build()

            val response = stub.getCountryNamesByCodes(request)
            return response.countryNamesByCodesMap.entries.associate { CountryCode.valueOf(it.key) to it.value }
        } catch (exception: StatusRuntimeException) {
            throw DownstreamServiceException(
                errorCode = DownstreamServiceErrorCode.CountryServiceFailed,
                message = "Error while fetching country names for country codes: $countryCodes",
                exception = exception,
                context = mapOf("countryCodes" to countryCodes.map { it.name }),
            )
        }
    }
}

@Service
@Profile("test|local")
class MockCountryServiceGrpcClient : CountryServiceAdapter {
    override suspend fun getCountryNamesByCodes(countryCodes: Set<CountryCode>): Map<CountryCode, String> {
        log.warn {
            "Mocking getCountryNamesByCodes for $countryCodes. " +
                "This should never happen outside of local/test environments."
        }
        return countryCodes.associateWith { getCountryNameForCode(it) }
    }

    private fun getCountryNameForCode(countryCode: CountryCode): String = when (countryCode) {
        CountryCode.USA -> "United States"
        CountryCode.GBR -> "United Kingdom"
        CountryCode.CAN -> "Canada"
        CountryCode.AUS -> "Australia"
        CountryCode.IND -> "India"
        CountryCode.SGP -> "Singapore"
        CountryCode.DEU -> "Germany"
        CountryCode.FRA -> "France"
        CountryCode.JPN -> "Japan"
        CountryCode.CHN -> "China"
        else -> "Country ${countryCode.name}"
    }
}
