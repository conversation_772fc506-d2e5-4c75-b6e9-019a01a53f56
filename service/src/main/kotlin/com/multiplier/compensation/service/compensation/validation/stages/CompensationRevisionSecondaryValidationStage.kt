package com.multiplier.compensation.service.compensation.validation.stages

import com.multiplier.compensation.service.common.validationpipeline.common.stages.SecondaryValidationStage
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.validators.BillingFrequencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateInputValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateValidator
import com.multiplier.compensation.service.compensation.validation.validators.CompensationDateValidator
import com.multiplier.compensation.service.compensation.validation.validators.ContractSchemaMappingValidator
import com.multiplier.compensation.service.compensation.validation.validators.DependentComponentCurrencyValidator
import com.multiplier.compensation.service.compensation.validation.validators.InstallmentValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleTypeValidator
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleValidator
import org.springframework.stereotype.Component

@Component
class CompensationRevisionSecondaryValidationStage(
    compensationDateValidator: CompensationDateValidator,
    billingFrequencyValidator: BillingFrequencyValidator,
    billingRateTypeValidator: BillingRateTypeValidator,
    billingRateValidator: BillingRateValidator,
    installmentValidator: InstallmentValidator,
    payScheduleValidator: PayScheduleValidator,
    payScheduleTypeValidator: PayScheduleTypeValidator,
    contractSchemaMappingValidator: ContractSchemaMappingValidator,
    dependentComponentCurrencyValidator: DependentComponentCurrencyValidator,
    billingRateInputValidator: BillingRateInputValidator,
) : SecondaryValidationStage<CompensationValidatorContext, CompensationDraft>() {
    init {
        setValidators(
            compensationDateValidator,
            billingFrequencyValidator,
            billingRateTypeValidator,
            billingRateValidator,
            installmentValidator,
            payScheduleValidator,
            payScheduleTypeValidator,
            contractSchemaMappingValidator,
            dependentComponentCurrencyValidator,
            billingRateInputValidator,
        )
    }
}
