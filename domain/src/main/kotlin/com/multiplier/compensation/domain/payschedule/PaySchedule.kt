package com.multiplier.compensation.domain.payschedule

import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class PaySchedule(
    val id: UUID,
    val entityId: Long,
    val companyId: Long,
    val name: String,
    val frequency: PayScheduleFrequency,
    val configurationScope: ConfigurationScope,
    val country: CountryCode?,
    val startDateReference: LocalDate,
    val endDateReference: LocalDate,
    val relativePayDays: Long,
    val payDateReferenceType: PayDateReference,
    val isInstallment: Boolean,
    val isActive: Boolean,
    val label: String,
    val createdOn: LocalDateTime,
    val createdBy: Long,
    val updatedOn: LocalDateTime,
    val updatedBy: Long,
)
