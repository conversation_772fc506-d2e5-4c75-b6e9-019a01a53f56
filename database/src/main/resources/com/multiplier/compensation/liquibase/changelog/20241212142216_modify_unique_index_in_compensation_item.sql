-- liquibase formatted sql

-- changeset Ujwal:20241212142216_modify_unique_index_in_compensation_item dbms:postgresql

ALTER TABLE compensation.compensation_item
DROP CONSTRAINT IF EXISTS compensation_item_compensation_id_compensation_start_date_c_key;

CREATE UNIQUE INDEX compensation_item_all_fields_idx
    ON compensation.compensation_item (compensation_id, compensation_start_date, COALESCE(compensation_end_date, '9999-12-31'), COALESCE(no_of_installments, -1), start_date, end_date, calculated_amount, is_arrear, COALESCE(arrear_of, '00000000-0000-0000-0000-000000000000'), COALESCE(arrear_trigger_reference, ''), COALESCE(update_trigger_reference, ''), status);
