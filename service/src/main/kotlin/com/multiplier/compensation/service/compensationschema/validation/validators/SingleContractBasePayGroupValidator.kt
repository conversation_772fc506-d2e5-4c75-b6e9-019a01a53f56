package com.multiplier.compensation.service.compensationschema.validation.validators

import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaDraft
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaValidationContext
import org.springframework.stereotype.Component

@Component
class SingleContractBasePayGroupValidator : Validator<CompensationSchemaValidationContext, CompensationSchemaDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationSchemaValidationContext,
        collector: ValidationDataCollector<CompensationSchemaDraft>,
    ): Boolean {
        collector.drafts[input.id] ?: return false

        val basePayDraftCount = collector.drafts.values.
    }
}
