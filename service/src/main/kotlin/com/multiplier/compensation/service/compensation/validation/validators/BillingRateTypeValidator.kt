package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants.isCategoryAllowedForPercentageBasePay
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.DependencyConfiguration
import org.springframework.stereotype.Component

@Component
class BillingRateTypeValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val billingRateType = draft.billingRateType ?: return false

        draft.schemaItemId ?: return false
        val schemaItem = context.getCompensationSchemaItem(draft.schemaItemId)
        schemaItem ?: return false

        // Validate that dependent billing rate types are not used on their parent components
        // and that they have their required parent component
        if (DependencyConfiguration.isDependentBillingRateType(billingRateType)) {
            val parentCategory = DependencyConfiguration.getParentCategory(billingRateType)
            if (parentCategory == null) {
                addValidationResult(
                    input = input,
                    field = CompensationSkeletonField.BILLING_RATE_TYPE,
                    resultType = ValidationResultType.ERROR,
                    message = "Parent component not found for ${billingRateType.description}",
                    collector = collector,
                )
                return false
            }

            // Check if dependent billing rate type is used on its parent component
            if (draft.schemaCategory == parentCategory) {
                addValidationResult(
                    input = input,
                    field = CompensationSkeletonField.BILLING_RATE_TYPE,
                    resultType = ValidationResultType.ERROR,
                    message = "Billing rate type ${billingRateType.description} " +
                        "is not allowed on the parent component",
                    collector = collector,
                )
                return false
            }

            // Check if the required parent component exists
            val parentDraft = findRelatedParentForCurrentDraft(
                currentDraft = draft,
                drafts = collector.drafts.values.toList(),
                context = context,
                parentCategory = parentCategory,
            )

            if (parentDraft == null) {
                addValidationResult(
                    input = input,
                    field = CompensationSkeletonField.BILLING_RATE_TYPE,
                    resultType = ValidationResultType.ERROR,
                    message = "Billing rate type ${billingRateType.description} " +
                        "is not allowed without a parent component",
                    collector = collector,
                )
                return false
            }
        }

        // Validate that billing rate type matches the schema item's billing rate type
        if (schemaItem.itemType == ItemType.INPUT &&
            schemaItem.billingRateType != null &&
            schemaItem.billingRateType != billingRateType
        ) {
            addValidationResult(
                input = input,
                field = CompensationSkeletonField.BILLING_RATE_TYPE,
                resultType = ValidationResultType.ERROR,
                message = "Billing rate type ${billingRateType.description} should match the " +
                    "schema item's billing rate type",
                collector = collector,
            )
            return false
        }

        if (draft.billingRateType == BillingRateType.BASE_PAY_PERCENTAGE &&
            !isCategoryAllowedForPercentageBasePay(draft.schemaCategory)
        ) {
            addValidationResult(
                input = input,
                field = CompensationSkeletonField.BILLING_RATE_TYPE,
                resultType = ValidationResultType.ERROR,
                message = "Billing rate type ${billingRateType.description} " +
                    "is not allowed on this category ${draft.schemaCategory}",
                collector = collector,
            )
            return false
        }

        return true
    }
}
