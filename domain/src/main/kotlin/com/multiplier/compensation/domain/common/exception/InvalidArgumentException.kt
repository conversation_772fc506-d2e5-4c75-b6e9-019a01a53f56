package com.multiplier.compensation.domain.common.exception

import com.multiplier.common.exception.ErrorCode
import com.multiplier.common.exception.MplBusinessException

class InvalidArgumentException(
    errorCode: ErrorCode,
    message: String,
    exception: Throwable? = null,
    context: Map<String, Any?> = emptyMap(),
) : MplBusinessException(
        errorCode = errorCode,
        message = message,
        cause = exception,
        context = context,
    )

fun requireCondition(
    condition: <PERSON><PERSON><PERSON>,
    errorCode: ErrorCode,
    message: String,
    exception: Throwable? = null,
    context: Map<String, Any?> = emptyMap(),
) {
    if (!condition) {
        throw InvalidArgumentException(
            errorCode = errorCode,
            message = message,
            exception = exception,
            context = context,
        )
    }
}
