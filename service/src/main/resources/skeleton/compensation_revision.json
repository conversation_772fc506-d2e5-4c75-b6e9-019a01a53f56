{"skeletonType": ["COMPENSATION_REVISION"], "keys": ["ENTITY_ID", "CONTRACT_ID", "COMPANY_ID"], "structure": [{"fieldId": "employeeId", "fieldName": "Employee ID", "valueType": "STRING", "validationRegex": {"regex": ".*", "errorMessage": "Should be text"}, "description": "Employee Identifier", "mandatory": true}, {"fieldId": "EMPLOYEE_FULL_NAME", "fieldName": "Employee Full Name", "valueType": "STRING", "description": "Employee Full Name", "mandatory": false}, {"fieldId": "COMPONENT_NAME", "fieldName": "Component Name", "valueType": "SELECT", "description": "The name of the component.", "possibleValues": [{"op": "ENTITY_COMPONENT_NAME_ENRICHER"}], "mandatory": true}, {"fieldId": "CURRENCY", "fieldName": "<PERSON><PERSON><PERSON><PERSON>", "valueType": "SELECT", "description": "The currency of the entity.", "possibleValues": [{"op": "ENTITY_CURRENCY_ENRICHER"}], "mandatory": true}, {"fieldId": "BILLING_RATE_TYPE", "fieldName": "Billing Rate Type", "valueType": "SELECT", "description": "Billing rate type.", "possibleValues": [{"op": "BILLING_RATE_TYPE_ENRICHER"}], "defaultValue": "Value", "mandatory": true}, {"fieldId": "BILLING_RATE", "fieldName": "Billing Rate", "valueType": "DOUBLE", "description": "Billing Rate for compensation component.", "validationRegex": {"regex": "^-?\\d+(\\.\\d+)?([eE][-+]?\\d+)?$", "errorMessage": "Should be a decimal number"}, "mandatory": false}, {"fieldId": "BILLING_FREQUENCY", "fieldName": "Billing Frequency", "valueType": "SELECT", "description": "Billing Frequency for compensation component.", "possibleValues": [{"op": "BILLING_FREQUENCY_ENRICHER"}], "mandatory": true}, {"fieldId": "PAY_SCHEDULE_NAME", "fieldName": "Pay Schedule Name", "valueType": "SELECT", "description": "Payment schedule name.", "possibleValues": [{"op": "ENTITY_PAY_SCHEDULE_ENRICHER"}], "mandatory": true}, {"fieldId": "IS_INSTALLMENT", "fieldName": "Is Installment?", "description": "Flag indicating whether component is an installment.", "valueType": "SELECT", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "mandatory": true}, {"fieldId": "START_DATE", "fieldName": "Start Date", "valueType": "DATE", "description": "Start date of the component. For installment, it would be first installment start date. Format - YYYY-MM-DD", "mandatory": true}, {"fieldId": "END_DATE", "fieldName": "End Date", "valueType": "DATE", "description": "End date of the component. Leave it blank for indefinite end date. Format - YYYY-MM-DD", "mandatory": false}, {"fieldId": "NUMBER_OF_INSTALLMENTS", "fieldName": "Number of installments", "description": "Number of installments to be paid.", "valueType": "INTEGER", "mandatory": false}, {"fieldId": "REASON_CODE", "fieldName": "Salary Change Reason Code", "description": "Reason code for salary change.", "valueType": "SELECT", "possibleValues": [{"op": "REASON_CODE_ENRICHER"}], "mandatory": false}, {"fieldId": "NOTES", "fieldName": "Notes", "description": "Notes for the component.", "valueType": "STRING", "mandatory": false}]}