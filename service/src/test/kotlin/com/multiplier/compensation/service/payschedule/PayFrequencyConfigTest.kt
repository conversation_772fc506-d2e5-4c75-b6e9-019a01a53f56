package com.multiplier.compensation.service.payschedule

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.database.repository.payschedule.CountryPayScheduleConfigRepository
import com.multiplier.compensation.database.repository.payschedule.PayScheduleItemRepository
import com.multiplier.compensation.database.repository.payschedule.PayScheduleRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.payschedule.schedulegeneration.PayScheduleIntervalGenerator
import com.multiplier.compensation.service.payschedule.strategy.CompensationPayScheduleSelectionStrategyFactory
import com.multiplier.compensation.service.payschedule.strategy.EorCompensationPayScheduleSelectionStrategy
import com.multiplier.compensation.service.payschedule.strategy.GlobalPayrollCompensationPayScheduleSelectionStrategy
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.jooq.DSLContext
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@Suppress("UNCHECKED_CAST")
@ExtendWith(MockKExtension::class)
class PayFrequencyConfigTest {
    private lateinit var dsl: DSLContext
    private lateinit var payScheduleRepository: PayScheduleRepository
    private lateinit var timeBasedEpochGenerator: TimeBasedEpochGenerator
    private lateinit var transactional: Transactional
    private lateinit var payScheduleService: PayScheduleService
    private lateinit var countryPayScheduleConfigRepository: CountryPayScheduleConfigRepository
    private lateinit var payScheduleItemsGenerator: PayScheduleItemsGenerator
    private lateinit var payScheduleItemRepository: PayScheduleItemRepository
    private lateinit var payScheduleIntervalGenerator: PayScheduleIntervalGenerator
    private lateinit var compensationPayScheduleSelectionStrategyFactory:
        CompensationPayScheduleSelectionStrategyFactory

    @BeforeEach
    fun setUp() {
        dsl = mockk(relaxed = true)
        payScheduleItemRepository = mockk()
        payScheduleIntervalGenerator = mockk()
        payScheduleRepository = mockk(relaxed = true)
        timeBasedEpochGenerator = mockk(relaxed = true)
        transactional = mockk(relaxed = true)
        countryPayScheduleConfigRepository = mockk(relaxed = true)
        compensationPayScheduleSelectionStrategyFactory = mockk(relaxed = true)

        // Create real strategy instances
        val eorStrategy = EorCompensationPayScheduleSelectionStrategy(
            payScheduleRepository,
            countryPayScheduleConfigRepository,
        )
        val gpStrategy = GlobalPayrollCompensationPayScheduleSelectionStrategy(
            payScheduleRepository,
        )

        // Mock the strategy factory to return real strategies
        every { compensationPayScheduleSelectionStrategyFactory.getStrategy(OfferingCode.EOR) } returns eorStrategy
        every { compensationPayScheduleSelectionStrategyFactory.getStrategy(OfferingCode.GLOBAL_PAYROLL) } returns
            gpStrategy

        payScheduleItemsGenerator = PayScheduleItemsGenerator(
            payScheduleIntervalGenerator,
            timeBasedEpochGenerator,
        )

        payScheduleService = PayScheduleService(
            payScheduleItemsGenerator,
            payScheduleItemRepository,
            payScheduleRepository,
            transactional,
            compensationPayScheduleSelectionStrategyFactory,
        )
    }

    @Test
    fun `getEligibleBillingFrequency for gross salary should return the correct billing frequency`() {
        val expectedBillingFrequency = BillingFrequency.MONTHLY

        val request = GetEligibleBillingFrequenciesRequest(
            entityId = 1L,
            countryCode = CountryCode.USA,
            state = null,
            offeringCode = OfferingCode.EOR,
            grossSalaryPayScheduleFrequency = null,
            compensationCategory = "CONTRACT_BASE_PAY",
        )

        every {
            countryPayScheduleConfigRepository.getAllBillingFrequenciesByCountryAndState(
                request.countryCode!!,
                request.state,
            )
        } returns listOf(expectedBillingFrequency)
        val result = payScheduleService.getEligibleBillingFrequencies(request)
        assert(result.isNotEmpty())
        assertEquals(result.first(), expectedBillingFrequency)
    }

    @Test
    fun `getEligibleBillingFrequency for additional component should return correct billing frequency`() {
        val expectedBillingFrequencies = listOf(
            BillingFrequency.ANNUALLY,
            BillingFrequency.SEMIANNUALLY,
            BillingFrequency.QUARTERLY,
            BillingFrequency.MONTHLY,
            BillingFrequency.ONETIME,
            BillingFrequency.DAILY,
            BillingFrequency.BIWEEKLY,
        )

        val request = GetEligibleBillingFrequenciesRequest(
            entityId = 1L,
            countryCode = CountryCode.USA,
            state = null,
            offeringCode = OfferingCode.EOR,
            grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
            compensationCategory = "",
        )
        val result = payScheduleService.getEligibleBillingFrequencies(request)
        assert(result.isNotEmpty())
        assertEquals(result, expectedBillingFrequencies)
    }

    @Test
    fun `getEligiblePaySchedules for gross salary should return the correct pay schedules`() {
        val expectedPaySchedules = createPaySchedules

        val request = GetEligiblePayScheduleRequest(
            entityId = 1L,
            countryCode = CountryCode.USA,
            state = "XYZ",
            offeringCode = OfferingCode.EOR,
            grossSalaryPayScheduleFrequency = null,
            compensationCategory = "TOTAL_COST_TO_COMPANY",
            billingFrequency = BillingFrequency.MONTHLY,
        )

        every {
            countryPayScheduleConfigRepository.getAllPayScheduleFrequenciesByCountryStateAndBillingFrequency(
                state = null,
                country = request.countryCode!!,
                billingFrequency = request.billingFrequency,
            )
        } returns listOf(PayScheduleFrequency.MONTHLY)

        every {
            payScheduleRepository.findAllEligiblePaySchedules(
                request = request.copy(),
                scope = ConfigurationScope.COUNTRY,
                frequencies = listOf(PayScheduleFrequency.MONTHLY),
                isInstallment = false,
                excludeInactiveSchedules = true,
            )
        } returns expectedPaySchedules

        val result = payScheduleService.getEligiblePaySchedules(request)
        assert(result.isNotEmpty())
        assertEquals(result[1L]?.first()?.entityId, expectedPaySchedules.first().entityId)
        assertEquals(result[1L]?.first()?.companyId, expectedPaySchedules.first().companyId)
        assertEquals(result[1L]?.first()?.name, expectedPaySchedules.first().name)
        assertEquals(result[1L]?.first()?.frequency, expectedPaySchedules.first().frequency)
        assertEquals(result[1L]?.first()?.configurationScope, expectedPaySchedules.first().configurationScope)
        assertEquals(result[1L]?.first()?.country, expectedPaySchedules.first().country)
        assertEquals(result[1L]?.first()?.startDateReference, expectedPaySchedules.first().startDateReference)
        assertEquals(result[1L]?.first()?.endDateReference, expectedPaySchedules.first().endDateReference)
        assertEquals(result[1L]?.first()?.relativePayDays, expectedPaySchedules.first().relativePayDays)
        assertEquals(result[1L]?.first()?.payDateReferenceType, expectedPaySchedules.first().payDateReferenceType)
        assertEquals(result[1L]?.first()?.isInstallment, expectedPaySchedules.first().isInstallment)
        assertEquals(result[1L]?.first()?.isActive, expectedPaySchedules.first().isActive)
        assertEquals(result[1L]?.first()?.label, expectedPaySchedules.first().label)
    }

    @Test
    fun `getEligiblePaySchedules for additional components should return the correct pay schedules`() {
        val expectedPaySchedules = createPaySchedules

        val request = GetEligiblePayScheduleRequest(
            entityId = 1L,
            countryCode = CountryCode.USA,
            state = null,
            offeringCode = OfferingCode.EOR,
            grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
            compensationCategory = CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE,
            billingFrequency = BillingFrequency.MONTHLY,
        )

        every {
            payScheduleRepository.findAllEligiblePaySchedules(
                request = request,
                scope = ConfigurationScope.COUNTRY,
                frequencies = listOf(PayScheduleFrequency.MONTHLY),
                isInstallment = null,
                excludeInactiveSchedules = true,
            )
        } returns expectedPaySchedules

        val result = payScheduleService.getEligiblePaySchedules(request)
        assertTrue { result.isNotEmpty() }
        assertEquals(result[1L]?.first()?.entityId, expectedPaySchedules.first().entityId)
        assertEquals(result[1L]?.first()?.companyId, expectedPaySchedules.first().companyId)
        assertEquals(result[1L]?.first()?.name, expectedPaySchedules.first().name)
        assertEquals(result[1L]?.first()?.frequency, expectedPaySchedules.first().frequency)
        assertEquals(result[1L]?.first()?.country, expectedPaySchedules.first().country)
        assertEquals(result[1L]?.first()?.startDateReference, expectedPaySchedules.first().startDateReference)
        assertEquals(result[1L]?.first()?.endDateReference, expectedPaySchedules.first().endDateReference)
        assertEquals(result[1L]?.first()?.relativePayDays, expectedPaySchedules.first().relativePayDays)
        assertEquals(result[1L]?.first()?.payDateReferenceType, expectedPaySchedules.first().payDateReferenceType)
        assertEquals(result[1L]?.first()?.isInstallment, expectedPaySchedules.first().isInstallment)
        assertEquals(result[1L]?.first()?.isActive, expectedPaySchedules.first().isActive)
        assertEquals(result[1L]?.first()?.label, expectedPaySchedules.first().label)
    }

    @Test
    fun `should return restricted billing frequencies when offering type is GP and category is CONTRACT_BASE_PAY`() {
        val request = GetEligibleBillingFrequenciesRequest(
            entityId = 1L,
            countryCode = CountryCode.USA,
            state = null,
            offeringCode = OfferingCode.GLOBAL_PAYROLL,
            grossSalaryPayScheduleFrequency = null,
            compensationCategory = "CONTRACT_BASE_PAY",
        )
        val result = payScheduleService.getEligibleBillingFrequencies(request)
        assertTrue { result.isNotEmpty() }
        // Should only contain HOURLY, DAILY, MONTHLY, ANNUALLY
        assertEquals(4, result.size)
        assertTrue { result.contains(BillingFrequency.HOURLY) }
        assertTrue { result.contains(BillingFrequency.DAILY) }
        assertTrue { result.contains(BillingFrequency.MONTHLY) }
        assertTrue { result.contains(BillingFrequency.ANNUALLY) }
        // Should not contain other frequencies
        assertFalse { result.contains(BillingFrequency.ONETIME) }
        assertFalse { result.contains(BillingFrequency.WEEKLY) }
        assertFalse { result.contains(BillingFrequency.BIWEEKLY) }
        assertFalse { result.contains(BillingFrequency.SEMIMONTHLY) }
        assertFalse { result.contains(BillingFrequency.QUARTERLY) }
        assertFalse { result.contains(BillingFrequency.SEMIANNUALLY) }
    }

    @Test
    fun `should return restricted billing frequencies for all primary salary categories in GP`() {
        val primarySalaryCategories = listOf(
            CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
            CategoryConstants.CATEGORY_TOTAL_COST_TO_COMPANY,
            CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
            CategoryConstants.CATEGORY_EMPLOYER_CONTRIBUTION,
            CategoryConstants.CATEGORY_EMPLOYEE_CONTRIBUTION,
        )

        primarySalaryCategories.forEach { category ->
            val request = GetEligibleBillingFrequenciesRequest(
                entityId = 1L,
                countryCode = CountryCode.USA,
                state = null,
                offeringCode = OfferingCode.GLOBAL_PAYROLL,
                grossSalaryPayScheduleFrequency = null,
                compensationCategory = category,
            )
            val result = payScheduleService.getEligibleBillingFrequencies(request)
            // Should only contain HOURLY, DAILY, MONTHLY, ANNUALLY for primary salary components
            assertEquals(4, result.size, "Expected 4 restricted billing frequencies for category: $category")
            assertTrue { result.contains(BillingFrequency.HOURLY) }
            assertTrue { result.contains(BillingFrequency.DAILY) }
            assertTrue { result.contains(BillingFrequency.MONTHLY) }
            assertTrue { result.contains(BillingFrequency.ANNUALLY) }
        }
    }

    @Test
    fun `should return pay schedules when offering code is GP for additional components`() {
        val expectedPaySchedules = createPaySchedules
        val request = GetEligiblePayScheduleRequest(
            entityId = 1L,
            countryCode = CountryCode.USA,
            state = null,
            offeringCode = OfferingCode.GLOBAL_PAYROLL,
            grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
            compensationCategory = "", // Non-primary salary category
            billingFrequency = BillingFrequency.MONTHLY,
        )
        // For additional components, GP strategy uses the GP mapping
        // MONTHLY -> MONTHLY should return [MONTHLY]
        val expectedFrequencies = listOf(PayScheduleFrequency.MONTHLY)

        every {
            payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
                entityId = request.entityId,
                scope = ConfigurationScope.COMPANY,
                frequencies = expectedFrequencies,
                isInstallment = null,
            )
        } returns expectedPaySchedules
        val result = payScheduleService.getEligiblePaySchedules(request)
        assertTrue { result.isNotEmpty() }
        assertEquals(result[1L]?.first()?.entityId, expectedPaySchedules.first().entityId)
        assertEquals(result[1L]?.first()?.companyId, expectedPaySchedules.first().companyId)
        assertEquals(result[1L]?.first()?.name, expectedPaySchedules.first().name)
        assertEquals(result[1L]?.first()?.frequency, expectedPaySchedules.first().frequency)
        assertEquals(result[1L]?.first()?.country, expectedPaySchedules.first().country)
        assertEquals(result[1L]?.first()?.startDateReference, expectedPaySchedules.first().startDateReference)
        assertEquals(result[1L]?.first()?.endDateReference, expectedPaySchedules.first().endDateReference)
        assertEquals(result[1L]?.first()?.relativePayDays, expectedPaySchedules.first().relativePayDays)
        assertEquals(result[1L]?.first()?.payDateReferenceType, expectedPaySchedules.first().payDateReferenceType)
        assertEquals(result[1L]?.first()?.isInstallment, expectedPaySchedules.first().isInstallment)
        assertEquals(result[1L]?.first()?.isActive, expectedPaySchedules.first().isActive)
        assertEquals(result[1L]?.first()?.label, expectedPaySchedules.first().label)
    }
}

private val createPaySchedules: List<com.multiplier.compensation.domain.payschedule.PaySchedule> = listOf(
    com.multiplier.compensation.domain.payschedule.PaySchedule(
        id = UUID.randomUUID(),
        entityId = 1L,
        companyId = 100L,
        name = "Monthly Pay Schedule",
        frequency = PayScheduleFrequency.MONTHLY,
        configurationScope = ConfigurationScope.COMPANY,
        country = CountryCode.USA,
        startDateReference = LocalDate.of(2023, 1, 1),
        endDateReference = LocalDate.of(2023, 12, 31),
        relativePayDays = 30L,
        payDateReferenceType = PayDateReference.COMPENSATION_END_DATE,
        isInstallment = false,
        isActive = true,
        label = "Monthly Pay Schedule",
        createdOn = LocalDateTime.of(2023, 1, 1, 0, 0),
        createdBy = 101L,
        updatedOn = LocalDateTime.of(2023, 1, 1, 0, 0),
        updatedBy = 102L,
    ),
)
