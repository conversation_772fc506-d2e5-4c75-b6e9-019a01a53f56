package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_BASE_PAY
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.PayScheduleValidator
import com.multiplier.compensation.service.payschedule.PayScheduleService
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationDraftFixture
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationSchemaItemFixture
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class PayScheduleValidatorTest {
    private lateinit var validator: PayScheduleValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private lateinit var payScheduleService: PayScheduleService

    private val basePaySchemaItemId = UUID.randomUUID()
    private val otherSchemaItemId = UUID.randomUUID()

    private val weeklyPayScheduleId = UUID.randomUUID()
    private val monthlyPayScheduleId = UUID.randomUUID()

    private val contractIdForDrafts = 50000L

    private val componentInTestName = "Base Pay Breakup 1"
    private val componentKeyValuePair = KeyValuePair(
        key = CompensationSkeletonField.COMPONENT_NAME.id,
        value = componentInTestName,
    )

    private val basePayDraft = compensationDraftFixture(
        contractId = contractIdForDrafts,
        schemaItemId = basePaySchemaItemId,
        billingRateType = BillingRateType.VALUE,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = monthlyPayScheduleId,
    )

    private val contractIdKeyValuePair = KeyValuePair(
        key = CommonSkeletonField.CONTRACT_ID.id,
        value = contractIdForDrafts.toString(),
    )

    @BeforeEach
    fun setUp() {
        payScheduleService = mockk(relaxed = true)
        validator = PayScheduleValidator(payScheduleService)

        val schemaItemMap = mapOf(
            "Base Pay" to compensationSchemaItemFixture(
                id = basePaySchemaItemId,
                category = CATEGORY_CONTRACT_BASE_PAY,
                componentName = "Base Pay",
            ),
            componentInTestName to compensationSchemaItemFixture(
                id = otherSchemaItemId,
                category = CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                componentName = componentInTestName,
            ),
        )
        context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = mapOf(
                CompensationSchema(
                    id = UUID.randomUUID(),
                    entityId = 1L,
                    country = CountryCode.USA,
                    companyId = 1L,
                    isDefault = true,
                    name = "Default Schema",
                    isActive = true,
                    createdOn = LocalDateTime.now(),
                    createdBy = 1L,
                    updatedOn = LocalDateTime.now(),
                    updatedBy = 1L,
                    schemaItems = schemaItemMap.values.toList(),
                    tags = listOf("GLOBAL_PAYROLL"),
                    configurationScope = ConfigurationScope.COMPANY,
                    description = "Test schema description",
                ) to schemaItemMap,
            ),
            paySchedules = mapOf(
                "WEEKLY_SCHEDULE" to mockk<PaySchedule> {
                    every { id } returns weeklyPayScheduleId
                    every { isInstallment } returns false
                    every { frequency } returns PayScheduleFrequency.WEEKLY
                },
                "MONTHLY_SCHEDULE" to mockk<PaySchedule> {
                    every { id } returns monthlyPayScheduleId
                    every { isInstallment } returns false
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                },
            ),
            contracts = mapOf(
                contractIdForDrafts to Contract(
                    id = contractIdForDrafts,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ACTIVE,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 1L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_SETUP,
        )

        collector =
            ValidationDataCollector(
                drafts = mutableMapOf(),
                rowValidationResult = mutableMapOf(),
                inputPlusDerivedRows = emptyMap(),
            )

        collector.drafts["1"] = basePayDraft
    }

    @Test
    fun `should return false when draft schema item not found`() {
        val input = ValidationInputItem(
            id = "2",
            fieldKeyValuePairs = listOf(),
        )
        collector.drafts[input.id] = input.toCompensationDraft(context)
        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `should return false when base pay draft not found`() {
        val input = ValidationInputItem(
            id = "2",
            fieldKeyValuePairs = listOf(),
        )
        collector.drafts[input.id] = input.toCompensationDraft(context)
        collector.drafts.remove("1")
        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `should return false when pay schedule ID does not match with base pay`() {
        val input = ValidationInputItem(
            id = "2",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                componentKeyValuePair,
                KeyValuePair(
                    key = CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
                    value = "WEEKLY_SCHEDULE",
                ),
            ),
        )

        collector.drafts[input.id] = input.toCompensationDraft(context)

        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CompensationSkeletonField.PAY_SCHEDULE_NAME.name &&
                    it.message == "Pay Schedule for $componentInTestName should match " +
                    "Pay schedule of Base Pay"
            }
        }
    }

    @Test
    fun `should return true when pay schedule IDs match`() {
        val input = ValidationInputItem(
            id = "2",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                componentKeyValuePair,
                KeyValuePair(
                    key = CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
                    value = "MONTHLY_SCHEDULE",
                ),
            ),
        )

        collector.drafts[input.id] = input.toCompensationDraft(context)

        val eligiblePaySchedule = mockk<PaySchedule> {
            every { id } returns monthlyPayScheduleId
            every { frequency } returns PayScheduleFrequency.MONTHLY
            every { isInstallment } returns false
        }

        every {
            payScheduleService.getEligiblePaySchedules(any())
        } returns mapOf(1L to listOf(eligiblePaySchedule))

        assertTrue { validator.validate(input, context, collector) }
    }
}
