package com.multiplier.compensation.grpc.server.mapper.bulkupload

import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputRequest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ValidateBulkRequestMapperTest {
    @Test
    fun `should convert ValidateUpsertInputBulkRequest to CompensationBulkValidationRequest`() {
        // Given
        val mockInputRequest = mockk<ValidateUpsertInputRequest>()
        every { mockInputRequest.inputId } returns "input-1"
        every { mockInputRequest.dataMap } returns mapOf("key1" to "value1")
        every { mockInputRequest.keys } returns mockk {
            every { entityId } returns 1L
            every { companyId } returns 2L
            every { contractId } returns 3L
        }

        val mockBulkRequest = mockk<ValidateUpsertInputBulkRequest>()
        every { mockBulkRequest.useCase } returns "COMPENSATION_SETUP"
        every { mockBulkRequest.inputsList } returns listOf(mockInputRequest)
        every { mockBulkRequest.jsonCustomParams } returns """{"key1": "value1"}"""

        // When
        val result = mockBulkRequest.toCompensationBulkValidationInput()

        // Then
        assertEquals(1L, result.entityId)
        assertEquals(RequestType.COMPENSATION_SETUP, result.source)
        assertEquals(1, result.items.size)

        val firstItem = result.items.first()
        assertEquals("input-1", firstItem.id)

        val fieldMap = firstItem.fieldKeyValuePairs.associate { it.key to it.value }
        assertEquals("value1", fieldMap["key1"])
        assertEquals("1", fieldMap[CommonSkeletonField.ENTITY_ID.name])
        assertEquals("2", fieldMap[CommonSkeletonField.COMPANY_ID.name])
        assertEquals("3", fieldMap[CommonSkeletonField.CONTRACT_ID.name])
    }

    @Test
    fun `should convert ValidateUpsertInputBulkRequest to CompensationBulkValidationRequest for backfill`() {
        val mockInputRequest = mockk<ValidateUpsertInputRequest>()
        every { mockInputRequest.inputId } returns "input-2"
        every { mockInputRequest.dataMap } returns mapOf("keyA" to "valueA")
        every { mockInputRequest.keys } returns mockk {
            every { entityId } returns 10L
            every { companyId } returns 20L
        }

        val mockBulkRequest = mockk<ValidateUpsertInputBulkRequest>()
        every { mockBulkRequest.useCase } returns "COMPENSATION_BACKFILL"
        every { mockBulkRequest.inputsList } returns listOf(mockInputRequest)
        every { mockBulkRequest.jsonCustomParams } returns """{"key1": "value1"}"""

        val result = mockBulkRequest.toCompensationBulkBackFillValidationInput()

        assertEquals(10L, result.entityId)
        assertEquals(RequestType.COMPENSATION_BACKFILL, result.source)
        assertEquals(1, result.items.size)

        val firstItem = result.items.first()
        assertEquals("input-2", firstItem.id)

        val fieldMap = firstItem.fieldKeyValuePairs.associate { it.key to it.value }
        assertEquals("valueA", fieldMap["keyA"])
        assertEquals("10", fieldMap[CommonSkeletonField.ENTITY_ID.name])
        assertEquals("20", fieldMap[CommonSkeletonField.COMPANY_ID.name])
    }

    @Test
    fun `should convert ValidateUpsertInputBulkRequest to CompensationSchemaBulkValidationRequest`() {
        val mockInputRequest = mockk<ValidateUpsertInputRequest>()
        every { mockInputRequest.inputId } returns "input-3"
        every { mockInputRequest.dataMap } returns mapOf("keyB" to "valueB")
        every { mockInputRequest.keys } returns mockk {
            every { entityId } returns 15L
            every { companyId } returns 25L
            every { countryCode } returns "US"
        }

        val mockBulkRequest = mockk<ValidateUpsertInputBulkRequest>()
        every { mockBulkRequest.inputsList } returns listOf(mockInputRequest)
        every { mockBulkRequest.jsonCustomParams } returns """{"key1": "value1"}"""

        val result = mockBulkRequest.toCompensationSchemaBulkValidationInput()

        assertEquals(15L, result.entityId)
        assertEquals(1, result.items.size)

        val firstItem = result.items.first()
        assertEquals("input-3", firstItem.id)

        val fieldMap = firstItem.fieldKeyValuePairs.associate { it.key to it.value }
        assertEquals("valueB", fieldMap["keyB"])
        assertEquals("15", fieldMap[CommonSkeletonField.ENTITY_ID.name])
        assertEquals("25", fieldMap[CommonSkeletonField.COMPANY_ID.name])
        assertEquals("US", fieldMap[CommonSkeletonField.COUNTRY_CODE.name])
    }

    @Test
    fun `should convert ValidateUpsertInputBulkRequest to PayScheduleBulkValidationInput`() {
        val mockInputRequest = mockk<ValidateUpsertInputRequest>()
        every { mockInputRequest.inputId } returns "input-1"
        every { mockInputRequest.dataMap } returns mapOf("payType" to "monthly")
        every { mockInputRequest.keys } returns mockk {
            every { entityId } returns 10L
            every { companyId } returns 20L
            every { countryCode } returns "USA"
        }

        val mockBulkRequest = mockk<ValidateUpsertInputBulkRequest>()
        every { mockBulkRequest.inputsList } returns listOf(mockInputRequest)
        every { mockBulkRequest.jsonCustomParams } returns """{"key1": "value1"}"""

        val result = mockBulkRequest.toPayScheduleBulkValidationInput()

        assertEquals(10L, result.entityId)
        assertEquals(1, result.items.size)

        val firstItem = result.items.first()
        assertEquals("input-1", firstItem.id)

        val fieldMap = firstItem.fieldKeyValuePairs.associate { it.key to it.value }
        assertEquals("monthly", fieldMap["payType"])
        assertEquals("10", fieldMap[CommonSkeletonField.ENTITY_ID.name])
        assertEquals("20", fieldMap[CommonSkeletonField.COMPANY_ID.name])
        assertEquals("USA", fieldMap[CommonSkeletonField.COUNTRY_CODE.name])
    }
}
