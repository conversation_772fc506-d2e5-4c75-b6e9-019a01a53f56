package com.multiplier.compensation.service.compensation

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNotEqualTo
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.common.contract.Contract
import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.common.contract.ContractType
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.compensation.dto.CompensationBulkInput
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.CompensationContextProvider
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensationinput.CompensationInputService
import com.multiplier.compensation.service.compensationitem.CompensationItemPublisher
import com.multiplier.compensation.service.compensationitem.CompensationItemService
import com.multiplier.compensation.service.compensationitem.dto.BulkCompensationItemUpdateRequest
import com.multiplier.compensation.service.compensationitem.dto.BulkCompensationItemUpdateResponse
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUpdateResponse
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationlog.CompensationLogService
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationSchemaItemFixture
import com.multiplier.transaction.database.jooq.transaction.DeferredTransaction
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.mockk.Awaits
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.UUID

class BulkCompensationRevisionServiceTest {
    private lateinit var contextProvider: CompensationContextProvider
    private lateinit var compensationLogService: CompensationLogService
    private lateinit var validationService: CompensationValidationService
    private lateinit var compensationRepository: CompensationRepository
    private lateinit var transactional: Transactional
    private lateinit var timeBasedEpochGenerator: TimeBasedEpochGenerator
    private lateinit var compensationItemService: CompensationItemService
    private lateinit var compensationInputService: CompensationInputService
    private lateinit var compensationItemPublisher: CompensationItemPublisher
    private lateinit var bulkCompensationRevisionService: BulkCompensationRevisionService
    private lateinit var transactionContext: TransactionContext
    private lateinit var deferredTransaction: DeferredTransaction<Unit, Unit>
    private lateinit var mockIndividualResponses: CompensationItemUpdateResponse

    private val basePaySchemaItemId = UUID.randomUUID()
    private val basePayBreakdownSchemaItemId = UUID.randomUUID()

    // Schema item IDs for zero billing rate tests
    private val derivedSchemaItemId = UUID.randomUUID()
    private val fixedSchemaItemId = UUID.randomUUID()
    private val calculatedSchemaItemId = UUID.randomUUID()
    private val inputSchemaItemId = UUID.randomUUID()

    private val contractIdForDrafts1 = 50000L
    private val contractIdForDrafts2 = 50001L
    private val payScheduleIdMock = UUID.randomUUID()

    @BeforeEach
    fun setUp() {
        contextProvider = mockk()
        compensationLogService = mockk()
        validationService = mockk()
        compensationRepository = mockk()
        transactional = mockk()
        timeBasedEpochGenerator = mockk()
        compensationItemService = mockk()
        compensationInputService = mockk()
        compensationItemPublisher = mockk()
        transactionContext = mockk()
        deferredTransaction = mockk()
        bulkCompensationRevisionService = BulkCompensationRevisionService(
            contextProvider,
            compensationLogService,
            validationService,
            compensationRepository,
            transactional,
            timeBasedEpochGenerator,
            compensationItemService,
            compensationInputService,
            compensationItemPublisher,
        )
        every { timeBasedEpochGenerator.generate() } returns UUID.randomUUID()
        mockIndividualResponses = CompensationItemUpdateResponse(
            oldImage = null,
            updatedNewImage = null,
            updatedNewRecord = null,
            adjustedItems = mockk(),
            pendingTransaction = null,
        )
        every { compensationRepository.updateAll(any(), any()) } just Awaits
        every { compensationRepository.saveAll(any(), any()) } just Awaits
        every { compensationInputService.generateCompensationInput(any(), any(), any()) } returns deferredTransaction
        every { deferredTransaction.invoke(transactionContext) } just Runs
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            deferredTransaction.invoke((transactionContext))
        }
        every { compensationItemPublisher.publishInBackground(any()) } just Runs
    }

    @Test
    fun `should update compensation term successfully`() {
        val input = createCompensationBulkInput()
        val context = createCompensationValidatorContext()
        val eligibleDraftsMap = createEligibleDraftsMapWithUpdatedTerm()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify(exactly = 1) {
            compensationInputService.generateCompensationInput(
                context,
                input,
                eligibleDraftsMap.values.toList(),
            )
        }
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            2,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )
        assertEquals(
            LocalDate.of(2025, 1, 1),
            firstCapturedRequest.captured.individualRequests[0].newRecord!!.startDate,
            "Mismatch in start date",
        )
        assertEquals(
            LocalDate.of(2025, 12, 31),
            firstCapturedRequest.captured.individualRequests[0].newRecord!!.endDate,
            "Mismatch in end date",
        )
    }

    @Test
    fun `should update compensation billing rate to non-zero successfully`() {
        val input = createCompensationBulkInput()
        val context = createCompensationValidatorContext()
        val eligibleDraftsMap = createEligibleDraftsMapWithUpdatedBillingRate()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationInputService.generateCompensationInput(context, input, eligibleDraftsMap.values.toList()) }
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            2,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )
        assertEquals(
            250.0,
            firstCapturedRequest.captured.individualRequests[0].newRecord!!.billingRate,
            "Mismatch in billing rate",
        )
    }

    @Test
    fun `should update compensation billing rate to zero successfully`() {
        val input = createCompensationBulkInput()
        val context = createCompensationValidatorContext()
        val eligibleDraftsMap = createEligibleDraftsMapWithZeroBillingRate()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationInputService.generateCompensationInput(context, input, eligibleDraftsMap.values.toList()) }
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            1,
            capturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )
        assertEquals(
            0.0,
            capturedRequest.captured.individualRequests[0].newRecord!!.billingRate,
            "Mismatch in billing rate",
        )
    }

    @Test
    fun `should add new compensation successfully`() {
        val input = createCompensationBulkInput()
        val context = createCompensationValidatorContext().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(createMockedBasePayCompensation()),
            ),
        )
        val eligibleDraftsMap = createEligibleDraftsMapWithNewCompensation()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationInputService.generateCompensationInput(context, input, eligibleDraftsMap.values.toList()) }
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }
        println(firstCapturedRequest)
        assertEquals(
            1,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )

        val newCompensationRequest = firstCapturedRequest.captured.individualRequests[0]
        assertEquals(
            eligibleDraftsMap["comp1"]?.schemaItemId,
            newCompensationRequest.newRecord!!.schemaItemId,
            "Mismatch in schema item ID",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.billingRate,
            newCompensationRequest.newRecord!!.billingRate,
            "Mismatch in billing rate",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.startDate,
            newCompensationRequest.newRecord!!.startDate,
            "Mismatch in start date",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.endDate,
            newCompensationRequest.newRecord!!.endDate,
            "Mismatch in end date",
        )
    }

    @Test
    fun `should update parent compensation with dependents successfully`() {
        val input = createCompensationBulkInput()
        val context = createCompensationValidatorContext()
        val eligibleDraftsMap = createEligibleDraftsMapWithParentAndDependents()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            2,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )

        val parentRequest = firstCapturedRequest.captured.individualRequests[0]
        assertEquals(
            eligibleDraftsMap["comp1"]?.schemaItemId,
            parentRequest.newRecord!!.schemaItemId,
            "Mismatch in parent schema item ID",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.billingRate,
            parentRequest.newRecord!!.billingRate,
            "Mismatch in parent billing rate",
        )
    }

    @Test
    fun `should update parent and dependents explicitly successfully`() {
        val input = createCompensationBulkInput()
        val context = createCompensationValidatorContext()
        val eligibleDraftsMap = createEligibleDraftsMapWithParentAndExplicitDependents()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationInputService.generateCompensationInput(context, input, eligibleDraftsMap.values.toList()) }
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        val compensationRequest = firstCapturedRequest.captured.individualRequests[1]
        assertEquals(
            eligibleDraftsMap["comp1"]?.schemaItemId,
            compensationRequest.parentUpdateRequest!!.newRecord!!.schemaItemId,
            "Mismatch in dependent parent schema item ID",
        )
        assertEquals(50.0, compensationRequest.oldImage!!.billingRate, "Mismatch in dependent old billing rate")
        assertEquals(50.0, compensationRequest.newImage!!.billingRate, "Mismatch in dependent new billing rate")
    }

    @Test
    fun `should update parent compensation with dependents successfully with no dependents impacted`() {
        val input = createCompensationBulkInput()
        val context = getValidationContextWithNoDependentsImpacted()
        val eligibleDraftsMap = createEligibleDraftsMapWithParentAndDependents()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            1,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )

        val parentRequest = firstCapturedRequest.captured.individualRequests[0]
        assertEquals(
            eligibleDraftsMap["comp1"]?.schemaItemId,
            parentRequest.newRecord!!.schemaItemId,
            "Mismatch in parent schema item ID",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.billingRate,
            parentRequest.newRecord!!.billingRate,
            "Mismatch in parent billing rate",
        )
    }

    @Test
    fun `should update parent compensation with dependents successfully with dependents impacted`() {
        val input = createCompensationBulkInput()
        val context = getValidationContextWithDependentsImpacted()
        val eligibleDraftsMap = createEligibleDraftsMapWithParentAndDependents()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            2,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )

        val parentRequest = firstCapturedRequest.captured.individualRequests[0]
        assertEquals(
            eligibleDraftsMap["comp1"]?.schemaItemId,
            parentRequest.newRecord!!.schemaItemId,
            "Mismatch in parent schema item ID",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.billingRate,
            parentRequest.newRecord!!.billingRate,
            "Mismatch in parent billing rate",
        )
    }

    @Test
    fun `should update parent compensation with dependents successfully with partial dependents impacted`() {
        val input = createCompensationBulkInput()
        val context = getValidatorContextWithPartialDependentsImpacted()
        val eligibleDraftsMap = createEligibleDraftsMapWithParentAndDependents()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            3,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )

        val parentRequest = firstCapturedRequest.captured.individualRequests[0]
        assertEquals(
            eligibleDraftsMap["comp1"]?.schemaItemId,
            parentRequest.newRecord!!.schemaItemId,
            "Mismatch in parent schema item ID",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.billingRate,
            parentRequest.newRecord!!.billingRate,
            "Mismatch in parent billing rate",
        )
    }

    @Test
    fun `should update parent compensation with dependents successfully with partial overlap dependents impacted`() {
        val input = createCompensationBulkInput()
        val context = getValidatorContextWithPartialOverlapDependentsImpacted()
        val eligibleDraftsMap = createEligibleDraftsMapWithParentAndDependents()
        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }
        val firstCapturedRequest = slot<BulkCompensationItemUpdateRequest>()
        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        var isFirstCapture = true

        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } answers {
            if (isFirstCapture) {
                firstCapturedRequest.captured = capturedRequest.captured
                isFirstCapture = false
            }
            mockBulkResponse
        }

        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        assertEquals(
            3,
            firstCapturedRequest.captured.individualRequests.size,
            "Mismatch in captured request items size",
        )

        val parentRequest = firstCapturedRequest.captured.individualRequests[0]
        assertEquals(
            eligibleDraftsMap["comp1"]?.schemaItemId,
            parentRequest.newRecord!!.schemaItemId,
            "Mismatch in parent schema item ID",
        )
        assertEquals(
            eligibleDraftsMap["comp1"]?.billingRate,
            parentRequest.newRecord!!.billingRate,
            "Mismatch in parent billing rate",
        )
    }

    @Test
    fun `should handle zero billing rate for INPUT compensation with same start date - marks as DELETED`() {
        // Given
        val input = createCompensationBulkInput()
        val existingCompensation = TestCompensationBuilder()
            .withBillingRate(100.0)
            .withSchemaItemId(inputSchemaItemId)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withPayScheduleId(payScheduleIdMock)
            .build()

        val context = createCompensationValidatorContextWithInputSchemaItem().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(existingCompensation),
            ),
        )

        val eligibleDraftsMap = mapOf(
            "comp1" to createBaseCompensationDraft().copy(
                billingRate = 0.0, // Zero billing rate
                startDate = LocalDate.of(2025, 1, 1), // Same start date as existing
                schemaItemId = inputSchemaItemId,
            ),
        )

        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }

        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } returns mockBulkResponse

        // When
        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        // Then
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        val updateRequest = capturedRequest.captured.individualRequests[0]

        // Should mark existing compensation as DELETED
        assertThat(updateRequest.oldImage).isNotNull()
        assertThat(updateRequest.newImage).isNotNull()
        assertThat(updateRequest.newRecord).isNull()
        assertThat(updateRequest.newImage!!.status).isEqualTo(CompensationStatus.DELETED)
        assertThat(updateRequest.oldImage!!.id).isEqualTo(existingCompensation.id)
    }

    @Test
    fun `should handle zero billing rate for INPUT compensation with different start date - creates new record`() {
        // Given
        val input = createCompensationBulkInput()
        val existingCompensation = TestCompensationBuilder()
            .withBillingRate(100.0)
            .withSchemaItemId(inputSchemaItemId)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withPayScheduleId(payScheduleIdMock)
            .build()

        val context = createCompensationValidatorContextWithInputSchemaItem().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(existingCompensation),
            ),
        )

        val eligibleDraftsMap = mapOf(
            "comp1" to createBaseCompensationDraft().copy(
                billingRate = 0.0, // Zero billing rate
                startDate = LocalDate.of(2025, 2, 1), // Different start date
                schemaItemId = inputSchemaItemId,
            ),
        )

        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }

        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } returns mockBulkResponse

        // When
        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        // Then
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        val updateRequest = capturedRequest.captured.individualRequests[0]

        // Should handle as billing rate difference (not zero billing rate deletion)
        // This will either create new record or update existing based on date overlap
        assertThat(updateRequest.newRecord).isNotNull()
        assertThat(updateRequest.newRecord!!.billingRate).isEqualTo(0.0)
        assertThat(updateRequest.newRecord!!.startDate).isEqualTo(LocalDate.of(2025, 2, 1))
    }

    @Test
    fun `should handle zero billing rate for FIXED derived compensation - creates new record`() {
        // Given
        val input = createCompensationBulkInput()
        val existingCompensation = TestCompensationBuilder()
            .withBillingRate(100.0)
            .withSchemaItemId(fixedSchemaItemId)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withPayScheduleId(payScheduleIdMock)
            .build()

        val context = createCompensationValidatorContextWithFixedSchemaItem().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(existingCompensation),
            ),
        )

        val eligibleDraftsMap = mapOf(
            "comp1" to createBaseCompensationDraft().copy(
                billingRate = 0.0, // Zero billing rate
                startDate = LocalDate.of(2025, 1, 1), // Same start date as existing
                schemaItemId = fixedSchemaItemId,
            ),
        )

        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }

        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } returns mockBulkResponse

        // When
        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        // Then
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        val updateRequest = capturedRequest.captured.individualRequests[0]

        // Should create new record for FIXED derived compensation (not mark as deleted)
        // This goes through handleBillingRateDifference, not handleZeroBillingRate
        assertThat(updateRequest.newRecord).isNotNull()
        assertThat(updateRequest.newRecord!!.billingRate).isEqualTo(0.0)
        assertThat(updateRequest.newRecord!!.schemaItemId).isEqualTo(fixedSchemaItemId)
        // The old image should be present (existing compensation being updated)
        assertThat(updateRequest.oldImage).isNotNull()
    }

    @Test
    fun `should handle zero billing rate for CALCULATED derived compensation - creates new record`() {
        // Given
        val input = createCompensationBulkInput()
        val existingCompensation = TestCompensationBuilder()
            .withBillingRate(100.0)
            .withSchemaItemId(calculatedSchemaItemId)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withPayScheduleId(payScheduleIdMock)
            .build()

        val context = createCompensationValidatorContextWithCalculatedSchemaItem().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(existingCompensation),
            ),
        )

        val eligibleDraftsMap = mapOf(
            "comp1" to createBaseCompensationDraft().copy(
                billingRate = 0.0, // Zero billing rate
                startDate = LocalDate.of(2025, 1, 1), // Same start date as existing
                schemaItemId = calculatedSchemaItemId,
            ),
        )

        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }

        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } returns mockBulkResponse

        // When
        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        // Then
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        val updateRequest = capturedRequest.captured.individualRequests[0]

        // Should create new record for CALCULATED derived compensation (not mark as deleted)
        // This goes through handleBillingRateDifference, not handleZeroBillingRate
        assertThat(updateRequest.newRecord).isNotNull()
        assertThat(updateRequest.newRecord!!.billingRate).isEqualTo(0.0)
        assertThat(updateRequest.newRecord!!.schemaItemId).isEqualTo(calculatedSchemaItemId)
        // The old image should be present (existing compensation being updated)
        assertThat(updateRequest.oldImage).isNotNull()
    }

    @Test
    fun `should handle zero billing rate for INPUT compensation when existing also has zero billing rate - marks as DELETED`() {
        // Given - This tests the actual zero billing rate path
        val input = createCompensationBulkInput()
        val existingCompensation = TestCompensationBuilder()
            .withBillingRate(0.0) // Existing also has zero billing rate
            .withSchemaItemId(inputSchemaItemId)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withPayScheduleId(payScheduleIdMock)
            .build()

        val context = createCompensationValidatorContextWithInputSchemaItem().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(existingCompensation),
            ),
        )

        val eligibleDraftsMap = mapOf(
            "comp1" to createBaseCompensationDraft().copy(
                billingRate = 0.0, // Zero billing rate
                startDate = LocalDate.of(2025, 1, 1), // Same start date as existing
                schemaItemId = inputSchemaItemId,
            ),
        )

        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }

        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } returns mockBulkResponse

        // When
        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        // Then
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        val updateRequest = capturedRequest.captured.individualRequests[0]

        // Should mark existing compensation as DELETED (this is the actual zero billing rate path)
        assertThat(updateRequest.oldImage).isNotNull()
        assertThat(updateRequest.newImage).isNotNull()
        assertThat(updateRequest.newRecord).isNull()
        assertThat(updateRequest.newImage!!.status).isEqualTo(CompensationStatus.DELETED)
        assertThat(updateRequest.oldImage!!.id).isEqualTo(existingCompensation.id)
    }

    @Test
    fun `should NOT handle zero billing rate for DERIVED compensation even when existing has zero billing rate - creates new record`() {
        // Given - This tests that derived compensations never go through zero billing rate deletion
        val input = createCompensationBulkInput()
        val existingCompensation = TestCompensationBuilder()
            .withBillingRate(0.0) // Existing also has zero billing rate
            .withSchemaItemId(derivedSchemaItemId)
            .withStartDate(LocalDate.of(2025, 1, 1))
            .withPayScheduleId(payScheduleIdMock)
            .build()

        val context = createCompensationValidatorContextWithDerivedSchemaItem().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(existingCompensation),
            ),
        )

        val eligibleDraftsMap = mapOf(
            "comp1" to createBaseCompensationDraft().copy(
                billingRate = 0.0, // Zero billing rate
                startDate = LocalDate.of(2025, 1, 1), // Same start date as existing
                schemaItemId = derivedSchemaItemId,
            ),
        )

        val auditInputById = createAuditInputById()
        val operationStatus = OperationStatus.SUCCESS

        val mockBulkResponse = mockk<BulkCompensationItemUpdateResponse> {
            every { individualResponses } returns listOf(mockIndividualResponses)
            every { pendingTransaction } returns null
        }

        val capturedRequest = slot<BulkCompensationItemUpdateRequest>()
        every {
            compensationItemService.previewBulkCompensationItemUpdate(capture(capturedRequest))
        } returns mockBulkResponse

        // When
        bulkCompensationRevisionService.execute(input, context, eligibleDraftsMap, operationStatus, auditInputById)

        // Then
        verify { compensationItemService.previewBulkCompensationItemUpdate(any()) }
        verify { compensationItemPublisher.publishInBackground(any()) }

        // Should NOT mark as deleted, even though both have zero billing rate and same start date
        // Because isDerivedCompensation returns true, it should not go through handleZeroBillingRate
        // Since billing rates are the same (0.0), this should be treated as no change
        // However, the service might still generate an update request, but it should NOT be a deletion
        if (capturedRequest.captured.individualRequests.isNotEmpty()) {
            val updateRequest = capturedRequest.captured.individualRequests[0]
            // If an update is generated, it should NOT be a deletion
            assertThat(updateRequest.newImage?.status).isNotEqualTo(CompensationStatus.DELETED)
        }
        // The main point is that derived compensations should never be deleted via zero billing rate logic
    }

    private fun getValidationContextWithNoDependentsImpacted(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(
                    createMockedBasePayCompensation(),
                    createBasePayDependentCompensation().copy(
                        startDate = LocalDate.of(2024, 1, 1),
                        endDate = LocalDate.of(2024, 12, 31),
                    ),
                ),
            ),
        )

    private fun getValidationContextWithDependentsImpacted(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(
                    createMockedBasePayCompensation(),
                    createBasePayDependentCompensation().copy(
                        startDate = LocalDate.of(2025, 1, 1),
                        endDate = LocalDate.of(2025, 12, 31),
                    ),
                ),
            ),
        )

    private fun getValidatorContextWithPartialDependentsImpacted(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(
                    createMockedBasePayCompensation(),
                    createBasePayDependentCompensation().copy(
                        startDate = LocalDate.of(2025, 1, 1),
                        endDate = LocalDate.of(2025, 12, 31),
                    ),
                    createBasePayDependentCompensation().copy(
                        startDate = LocalDate.of(2024, 1, 1),
                        endDate = LocalDate.of(2024, 12, 31),
                    ),
                    createBasePayDependentCompensation().copy(
                        endDate = LocalDate.of(2025, 1, 1),
                    ),
                ),
            ),
        )

    private fun getValidatorContextWithPartialOverlapDependentsImpacted(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(
                    createMockedBasePayCompensation(),
                    createBasePayDependentCompensation().copy(
                        startDate = LocalDate.of(2025, 1, 1),
                        endDate = LocalDate.of(2025, 12, 31),
                    ),
                    createBasePayDependentCompensation().copy(
                        startDate = LocalDate.of(2025, 6, 1),
                        endDate = LocalDate.of(2025, 12, 31),
                    ),
                ),
            ),
        )

    private fun createBaseCompensationDraft(): CompensationDraft = CompensationDraft(
        companyId = 1,
        employeeId = "emp1",
        contractId = contractIdForDrafts1,
        schemaItemId = basePaySchemaItemId,
        schemaCategory = "CONTRACT_BASE_PAY",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 200.0,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = payScheduleIdMock,
        startDate = LocalDate.of(2025, 1, 1),
        endDate = LocalDate.of(2025, 12, 31),
        isInstallment = false,
        noOfInstallments = null,
        reasonCode = null,
    )

    private fun createEligibleDraftsMapWithParentAndExplicitDependents(): Map<String, CompensationDraft> = mapOf(
        "comp1" to createBaseCompensationDraft(),
    )

    private fun createEligibleDraftsMapWithParentAndDependents(): Map<String, CompensationDraft> = mapOf(
        "comp1" to createBaseCompensationDraft(),
        "comp2" to createBaseCompensationDraft().copy(
            schemaItemId = basePayBreakdownSchemaItemId,
            schemaCategory = "EMPLOYEE_DEDUCTION",
            billingRateType = BillingRateType.BASE_PAY_PERCENTAGE,
            billingRate = 50.0,
        ),
    )

    private fun createEligibleDraftsMapWithNewCompensation(): Map<String, CompensationDraft> = mapOf(
        "comp1" to createBaseCompensationDraft(),
    )

    private fun createEligibleDraftsMapWithZeroBillingRate(): Map<String, CompensationDraft> = mapOf(
        "comp1" to createBaseCompensationDraft().copy(
            billingRate = 0.0, // Updated billing rate to zero
        ),
    )

    private fun createEligibleDraftsMapWithUpdatedBillingRate(): Map<String, CompensationDraft> = mapOf(
        "comp1" to createBaseCompensationDraft().copy(
            billingRate = 250.0, // Updated billing rate
        ),
    )

    private fun createEligibleDraftsMapWithUpdatedTerm(): Map<String, CompensationDraft> = mapOf(
        "comp1" to createBaseCompensationDraft().copy(
            startDate = LocalDate.of(2025, 1, 1), // Updated start date
            endDate = LocalDate.of(2025, 12, 31), // Updated end date
        ),
    )

    private fun createCompensationBulkInput(): CompensationBulkInput = CompensationBulkInput(
        entityId = 1,
        commitPartially = false,
        requestType = RequestType.COMPENSATION_REVISION,
        requestId = UUID.randomUUID().toString(),
        rowItems = emptyList(),
        customParams = mapOf(
            BulkJsonCustomParamsUtil.BulkCustomKeys.OFFERING_TYPE to
                OfferingType.GLOBAL_PAYROLL.name,
        ),
    )

    private fun createCompensationValidatorContext(): CompensationValidatorContext {
        val basePayCompensation = createMockedBasePayCompensation()
        val dependentCompensation = createBasePayDependentCompensation()
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Default Schema"
                    every { isDefault } returns true
                    every { tags } returns listOf("GLOBAL_PAYROLL")
                    every { schemaItems } returns listOf(
                        compensationSchemaItemFixture(
                            id = basePaySchemaItemId,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Base Pay",
                        ),
                        compensationSchemaItemFixture(
                            id = basePayBreakdownSchemaItemId,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                            componentName = "Breakdown",
                        ),
                    )
                } to mapOf(
                    "Base Pay" to compensationSchemaItemFixture(
                        id = basePaySchemaItemId,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Base Pay",
                    ),
                    "Breakdown" to compensationSchemaItemFixture(
                        id = basePayBreakdownSchemaItemId,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP,
                        componentName = "Breakdown",
                    ),
                ),
            ),
            paySchedules = mapOf(
                "MONTHLY_SCHEDULE" to mockk<PaySchedule> {
                    every { id } returns payScheduleIdMock
                    every { isInstallment } returns false
                    every { frequency } returns PayScheduleFrequency.MONTHLY
                },
            ),
            contracts = mapOf(
                contractIdForDrafts1 to Contract(
                    id = contractIdForDrafts1,
                    companyId = 1L,
                    memberId = 1L,
                    employeeId = "EMP1",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
                contractIdForDrafts2 to Contract(
                    id = contractIdForDrafts2,
                    companyId = 1L,
                    memberId = 2L,
                    employeeId = "EMP2",
                    status = ContractStatus.ONBOARDING,
                    startOn = LocalDate.now(),
                    currency = "USD",
                    countryCode = CountryCode.USA,
                    workplaceEntityId = 100L,
                    stateCode = "CA",
                    contractType = ContractType.HR_MEMBER,
                ),
            ),
            activeCompContractIds = emptySet(),
            validCompensations = mapOf(
                contractIdForDrafts1 to listOf(basePayCompensation, dependentCompensation),
            ),
            contextUseCase = ContextUseCase.UPSERT,
            contextSource = RequestType.COMPENSATION_REVISION,
        )
        return context
    }

    private fun createAuditInputById(): Map<String, UUID> = mapOf(
        "comp1" to UUID.randomUUID(),
        "comp2" to UUID.randomUUID(),
    )

    private fun createMockedBasePayCompensation(): Compensation = TestCompensationBuilder()
        .withBillingRateType(BillingRateType.VALUE)
        .withBillingRate(100.0)
        .withSchemaItemId(basePayBreakdownSchemaItemId)
        .withPayScheduleId(payScheduleIdMock)
        .build()

    private fun createBasePayDependentCompensation(): Compensation = TestCompensationBuilder()
        .withBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE)
        .withBillingRate(50.0)
        .withSchemaItemId(basePayBreakdownSchemaItemId)
        .withCategory(CategoryConstants.CATEGORY_CONTRACT_BASE_PAY_BREAKUP)
        .withPayScheduleId(payScheduleIdMock)
        .build()

    private fun createCompensationValidatorContextWithInputSchemaItem(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Test Schema"
                    every { isDefault } returns true
                    every { tags } returns listOf("GLOBAL_PAYROLL")
                    every { schemaItems } returns listOf(
                        compensationSchemaItemFixture(
                            id = inputSchemaItemId,
                            itemType = ItemType.INPUT,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Input Base Pay",
                        ),
                        // Include the base pay schema item that createBaseCompensationDraft() uses
                        compensationSchemaItemFixture(
                            id = basePaySchemaItemId,
                            itemType = ItemType.INPUT,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Base Pay",
                        ),
                    )
                } to mapOf(
                    "Input Base Pay" to compensationSchemaItemFixture(
                        id = inputSchemaItemId,
                        itemType = ItemType.INPUT,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Input Base Pay",
                    ),
                    "Base Pay" to compensationSchemaItemFixture(
                        id = basePaySchemaItemId,
                        itemType = ItemType.INPUT,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Base Pay",
                    ),
                ),
            ),
        )

    private fun createCompensationValidatorContextWithDerivedSchemaItem(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Test Schema"
                    every { isDefault } returns true
                    every { tags } returns listOf("GLOBAL_PAYROLL")
                    every { schemaItems } returns listOf(
                        compensationSchemaItemFixture(
                            id = derivedSchemaItemId,
                            itemType = ItemType.FIXED, // Derived type
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Derived Base Pay",
                        ),
                        // Include the base pay schema item that createBaseCompensationDraft() uses
                        compensationSchemaItemFixture(
                            id = basePaySchemaItemId,
                            itemType = ItemType.INPUT,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Base Pay",
                        ),
                    )
                } to mapOf(
                    "Derived Base Pay" to compensationSchemaItemFixture(
                        id = derivedSchemaItemId,
                        itemType = ItemType.FIXED, // Derived type
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Derived Base Pay",
                    ),
                    "Base Pay" to compensationSchemaItemFixture(
                        id = basePaySchemaItemId,
                        itemType = ItemType.INPUT,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Base Pay",
                    ),
                ),
            ),
        )

    private fun createCompensationValidatorContextWithFixedSchemaItem(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Test Schema"
                    every { isDefault } returns true
                    every { tags } returns listOf("GLOBAL_PAYROLL")
                    every { schemaItems } returns listOf(
                        compensationSchemaItemFixture(
                            id = fixedSchemaItemId,
                            itemType = ItemType.FIXED,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Fixed Base Pay",
                        ),
                        // Include the base pay schema item that createBaseCompensationDraft() uses
                        compensationSchemaItemFixture(
                            id = basePaySchemaItemId,
                            itemType = ItemType.INPUT,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Base Pay",
                        ),
                    )
                } to mapOf(
                    "Fixed Base Pay" to compensationSchemaItemFixture(
                        id = fixedSchemaItemId,
                        itemType = ItemType.FIXED,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Fixed Base Pay",
                    ),
                    "Base Pay" to compensationSchemaItemFixture(
                        id = basePaySchemaItemId,
                        itemType = ItemType.INPUT,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Base Pay",
                    ),
                ),
            ),
        )

    private fun createCompensationValidatorContextWithCalculatedSchemaItem(): CompensationValidatorContext =
        createCompensationValidatorContext().copy(
            schemaItems = mapOf(
                mockk<CompensationSchema> {
                    every { id } returns UUID.randomUUID()
                    every { name } returns "Test Schema"
                    every { isDefault } returns true
                    every { tags } returns listOf("GLOBAL_PAYROLL")
                    every { schemaItems } returns listOf(
                        compensationSchemaItemFixture(
                            id = calculatedSchemaItemId,
                            itemType = ItemType.CALCULATED,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Calculated Base Pay",
                        ),
                        // Include the base pay schema item that createBaseCompensationDraft() uses
                        compensationSchemaItemFixture(
                            id = basePaySchemaItemId,
                            itemType = ItemType.INPUT,
                            category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                            componentName = "Base Pay",
                        ),
                    )
                } to mapOf(
                    "Calculated Base Pay" to compensationSchemaItemFixture(
                        id = calculatedSchemaItemId,
                        itemType = ItemType.CALCULATED,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Calculated Base Pay",
                    ),
                    "Base Pay" to compensationSchemaItemFixture(
                        id = basePaySchemaItemId,
                        itemType = ItemType.INPUT,
                        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
                        componentName = "Base Pay",
                    ),
                ),
            ),
        )
}
