package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.compensationschema.SchemaTags
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import org.springframework.stereotype.Component

@Component
class SchemaTagsEnricher : Enricher {
    override fun enrich(
        skeletonType: SkeletonType,
        entityId: Long,
        customParams: Map<String, String>,
        skeletonData: SkeletonStructureData,
    ): List<String> = SchemaTags.entries.map { it.name }
}
