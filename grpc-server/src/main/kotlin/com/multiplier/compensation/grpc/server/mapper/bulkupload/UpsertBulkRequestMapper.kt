package com.multiplier.compensation.grpc.server.mapper.bulkupload

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil.possibleCustomKeys
import com.multiplier.compensation.service.common.dto.RowItem
import com.multiplier.compensation.service.compensation.dto.CompensationBulkInput
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaBulkUpsertRequest
import com.multiplier.compensation.service.payschedule.dto.PayScheduleBulkCreationInput
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertRequest

fun UpsertBulkRequest.toCompensationSchemaBulkCreationInput() = CompensationSchemaBulkUpsertRequest(
    entityId = extractEntity(),
    commitPartially = false,
    customParams = extractCustomParams(this.inputsList),
    rowItems = this.inputsList.map { it.toCompensationSchemaRowItem() },
)

fun UpsertBulkRequest.toPayScheduleBulkCreationInput() = PayScheduleBulkCreationInput(
    entityId = extractEntity(),
    commitPartially = false,
    customParams = extractCustomParams(this.inputsList),
    rowItems = this.inputsList.map { it.toPayScheduleRowItem() },
)

fun UpsertBulkRequest.toCompensationBulkCreationInput(timeBasedEpochGenerator: TimeBasedEpochGenerator) =
    CompensationBulkInput(
        entityId = extractEntity(),
        commitPartially = false,
        requestType = RequestType.valueOf(this.useCase),
        requestId = timeBasedEpochGenerator.generate().toString(),
        customParams = extractCustomParams(this.inputsList),
        rowItems = this.inputsList.map { it.toCompensationRowItem() },
    )

fun UpsertBulkRequest.extractEntity(): Long {
    val entityIds = inputsList.asSequence().mapNotNull { it.dataMap["ENTITY_ID"] }.toSet()
    requireCondition(
        entityIds.size == 1,
        ValidationErrorCode.MultipleEntitiesFound,
        "Expected single entity in input but found [${entityIds.size}]",
    )
    return entityIds.first().toLong()
}

private fun extractCustomParams(inputsList: List<UpsertRequest>): Map<String, String> =
    inputsList.first().dataMap.filter { (key, _) ->
        key in possibleCustomKeys
    }.toMap()

private fun UpsertRequest.toCompensationSchemaRowItem() = RowItem(
    id = this.inputId,
    keyValuePairs = this.dataMap.toKeyValuePairs(),
)

private fun UpsertRequest.toPayScheduleRowItem() = RowItem(
    id = this.inputId,
    keyValuePairs = this.dataMap.toKeyValuePairs(),
)

private fun UpsertRequest.toCompensationRowItem() = RowItem(
    id = this.inputId,
    keyValuePairs = this.dataMap.toKeyValuePairs(),
)
