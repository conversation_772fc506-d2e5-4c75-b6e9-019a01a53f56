package com.multiplier.compensation.service.payschedule.mapper

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency

val allPossiblePayScheduleFrequencyToBillingFrequencies = mapOf(
    PayScheduleFrequency.ANNUALLY to setOf(
        BillingFrequency.ANNUALLY,
    ),
    PayScheduleFrequency.SEMI_ANNUALLY to setOf(
        BillingFrequency.ANNUALLY,
        BillingFrequency.SEMIANNUALLY,
    ),
    PayScheduleFrequency.QUARTERLY to setOf(
        BillingFrequency.ANNUALLY,
        BillingFrequency.SEMIANNUALLY,
        BillingFrequency.QUARTERLY,
    ),
    PayScheduleFrequency.MONTHLY to setOf(
        BillingFrequency.ANNUALLY,
        BillingFrequency.SEMIANNUALLY,
        BillingFrequency.QUARTERLY,
        BillingFrequency.MONTHLY,
        BillingFrequency.SEMIMONTHLY,
        BillingFrequency.DAILY,
        BillingFrequency.HOURLY,
    ),
    PayScheduleFrequency.SEMI_MONTHLY to setOf(
        BillingFrequency.ANNUALLY,
        BillingFrequency.SEMIANNUALLY,
        BillingFrequency.QUARTERLY,
        BillingFrequency.MONTHLY,
        BillingFrequency.SEMIMONTHLY,
        BillingFrequency.HOURLY,
    ),
    PayScheduleFrequency.BI_WEEKLY to setOf(
        BillingFrequency.ANNUALLY,
        BillingFrequency.MONTHLY,
        BillingFrequency.BIWEEKLY,
        BillingFrequency.HOURLY,
    ),
    PayScheduleFrequency.WEEKLY to setOf(
        BillingFrequency.ANNUALLY,
        BillingFrequency.MONTHLY,
        BillingFrequency.WEEKLY,
        BillingFrequency.HOURLY,
    ),
    PayScheduleFrequency.ONE_TIME to setOf(
        BillingFrequency.ONETIME,
    ),
)
