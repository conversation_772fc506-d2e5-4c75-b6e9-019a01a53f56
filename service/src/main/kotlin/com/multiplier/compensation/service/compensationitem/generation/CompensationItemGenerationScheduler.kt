package com.multiplier.compensation.service.compensationitem.generation

import com.fasterxml.uuid.Generators
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemGenerationRequest
import com.multiplier.compensation.service.compensationitem.facades.CompensationItemGenerationFacade
import com.multiplier.transaction.spring.scheduled
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PreDestroy
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

private val log = KotlinLogging.logger {}
private val uuidGenerator = Generators.timeBasedEpochGenerator()!!

@Service
class CompensationItemGenerationScheduler(
    private val generationFacade: CompensationItemGenerationFacade,
    @Value("\${core.scheduler.compensation-item-generation.job-name}")
    private val jobName: String,
    @Value("\${core.scheduler.compensation-item-generation.enabled}")
    private val enabled: Boolean,
    @Value("\${core.scheduler.compensation-item-generation.generation-period-in-days}")
    private val generationPeriodInDays: Long,
    @Value("\${core.scheduler.compensation-item-generation.lock-atmost-for}")
    private val lockAtMostFor: String,
    dispatcher: CoroutineDispatcher,
) : CoroutineScope {
    var exceptionHandler: ((Throwable) -> Unit)? = null
    private val job = SupervisorJob()
    override val coroutineContext = dispatcher + job

    @Scheduled(cron = "\${core.scheduler.compensation-item-generation.frequency}")
    @SchedulerLock(
        name = "\${core.scheduler.compensation-item-generation.job-name}",
        lockAtLeastFor = "\${core.scheduler.compensation-item-generation.lock-atleast-for}",
        lockAtMostFor = "\${core.scheduler.compensation-item-generation.lock-atmost-for}",
    )
    fun generateCompensationItems() {
        scheduled {
            if (!enabled) {
                log.warn { "Scheduler is not enabled for [$jobName] job. Please check the application configuration." }
                return@scheduled
            }
            executeJob()
        }
    }

    fun executeJob() {
        val request = buildRequest()
        val jobId = uuidGenerator.generate()

        logJobStart(jobId)
        runBlocking {
            val lockTimeoutMillis = Duration.parse(lockAtMostFor).toMillis()
            executeJobWithTimeout(lockTimeoutMillis, request, jobId)
        }
        logJobEnd(jobId)
    }

    private fun buildRequest(): CompensationItemGenerationRequest {
        val toDate = LocalDate.now().plusDays(generationPeriodInDays)
        val shouldPersist = true
        return CompensationItemGenerationRequest(toDate, shouldPersist)
    }

    private suspend fun executeJobWithTimeout(
        lockTimeoutMillis: Long,
        request: CompensationItemGenerationRequest,
        jobId: UUID,
    ) = try {
        withTimeout(lockTimeoutMillis) {
            generationFacade.generateCompensationItems(request, jobId)
        }
    } catch (e: TimeoutCancellationException) {
        exceptionHandler?.invoke(e)
        logTimeoutError(e, jobId, lockTimeoutMillis)
    }

    private fun logJobStart(jobId: UUID) = log.info {
        "Starting execution of the [$jobName] job with jobId [$jobId] at [${LocalDateTime.now()}]."
    }

    private fun logTimeoutError(
        e: Throwable,
        jobId: UUID,
        lockTimeoutMillis: Long,
    ) = log.error {
        "Execution of [$jobName] job with jobId [$jobId] timed out after [$lockTimeoutMillis] milliseconds " +
            "and was canceled: ${e.message}."
    }

    private fun logJobEnd(jobId: UUID) = log.info {
        "Finished execution of the [$jobName] job with jobId [$jobId] at [${LocalDateTime.now()}]."
    }

    @PreDestroy
    fun onShutdown() {
        log.info { "Shutting down the scheduler for [$jobName] job." }
        job.cancel()
    }
}

@Configuration
class CoroutineDispatcherConfig {
    @Bean
    fun dispatcher(): CoroutineDispatcher = Dispatchers.Default
}
