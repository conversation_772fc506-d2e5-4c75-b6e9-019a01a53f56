package com.multiplier.compensation.database.repository.compensationitem

import com.multiplier.common.exception.toSystemException
import com.multiplier.compensation.database.mapper.compensationItem.applyAudit
import com.multiplier.compensation.database.mapper.compensationItem.applyUpdateOnly
import com.multiplier.compensation.database.mapper.compensationItem.toAudit
import com.multiplier.compensation.database.mapper.compensationItem.toCompensationItemEnriched
import com.multiplier.compensation.database.mapper.compensationItem.toDomain
import com.multiplier.compensation.database.mapper.compensationItem.toRecord
import com.multiplier.compensation.database.tables.records.CompensationItemRecord
import com.multiplier.compensation.database.tables.references.COMPENSATION_
import com.multiplier.compensation.database.tables.references.COMPENSATION_ITEM
import com.multiplier.compensation.database.tables.references.COMPENSATION_ITEM_AUD
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA_ITEM
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import com.multiplier.compensation.database.util.CompensationItemConditionBuilder
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.CompensationItemsFilter
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationitem.CompensationItemEnriched
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import io.github.oshai.kotlinlogging.KotlinLogging
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

private val log = KotlinLogging.logger {}

@Service
class CompensationItemRepository(
    private val dsl: DSLContext,
) {
    fun saveNew(
        item: CompensationItem,
        transactionContext: TransactionContext,
    ) {
        val record = item.toRecord().applyAudit(transactionContext.audit)
        transactionContext.trx.executeInsert(record)
        record.audit(transactionContext)
    }

    fun saveAll(
        items: Collection<CompensationItem>,
        transactionContext: TransactionContext,
    ) {
        if (items.isEmpty()) {
            log.info { "No compensation items to save." }
            return
        }

        val records = items.map {
            it.toRecord().applyAudit(transactionContext.audit)
        }

        log.info { "Saving ${items.size} compensation items" }
        transactionContext.trx.batchInsert(records).execute()
        records.audit(transactionContext)
    }

    fun updateAll(
        items: Collection<CompensationItem>,
        transactionContext: TransactionContext,
    ) {
        if (items.isEmpty()) {
            log.info { "No compensation items to update." }
            return
        }
        log.info { "Updating [${items.size}] compensation items." }
        transactionContext.trx.transaction { configuration ->
            val newTransaction = transactionContext.copy(trx = DSL.using(configuration))

            items.forEach { item ->
                update(item, newTransaction)
            }
        }
    }

    fun update(
        item: CompensationItem,
        transactionContext: TransactionContext,
    ) {
        val updatedRecord = item.toRecord().applyAudit(transactionContext.audit).applyUpdateOnly()

        val condition = COMPENSATION_ITEM.ID.eq(item.id)
            .and(COMPENSATION_ITEM.UPDATED_ON.eq(item.updatedOn))

        val updateCount = transactionContext.trx.executeUpdate(updatedRecord, condition)

        if (updateCount == 0) {
            throw ValidationErrorCode.ConditionalCheckFailed.toSystemException(
                message = "CompensationItem record with id [${item.id}] and updatedOn [${item.updatedOn}] " +
                    "either does not exist or has been modified by another transaction.",
                context = mapOf("compensationItemId" to item.id),
            )
        }
        updatedRecord.audit(transactionContext)
    }

    fun getEnrichedCompensationItems(
        filter: CompensationItemsFilter,
        transactionContext: TransactionContext,
    ): List<CompensationItemEnriched> {
        val conditions = CompensationItemConditionBuilder.buildConditions(filter)

        return transactionContext.trx
            .select(
                COMPENSATION_ITEM.ID,
                COMPENSATION_ITEM.COMPANY_ID,
                COMPENSATION_ITEM.ENTITY_ID,
                COMPENSATION_ITEM.CONTRACT_ID,
                COMPENSATION_ITEM.COMPENSATION_ID,
                COMPENSATION_ITEM.SCHEMA_ITEM_ID,
                COMPENSATION_ITEM.CATEGORY,
                COMPENSATION_ITEM.CURRENCY,
                COMPENSATION_ITEM.BILLING_RATE_TYPE,
                COMPENSATION_ITEM.BILLING_RATE,
                COMPENSATION_ITEM.BILLING_FREQUENCY,
                COMPENSATION_ITEM.PAY_SCHEDULE_ID,
                COMPENSATION_ITEM.COMPENSATION_START_DATE,
                COMPENSATION_ITEM.COMPENSATION_END_DATE,
                COMPENSATION_ITEM.COMPENSATION_STATUS,
                COMPENSATION_ITEM.START_DATE,
                COMPENSATION_ITEM.END_DATE,
                COMPENSATION_ITEM.IS_INSTALLMENT,
                COMPENSATION_ITEM.NO_OF_INSTALLMENTS,
                COMPENSATION_ITEM.CURRENT_INSTALLMENT,
                COMPENSATION_ITEM.CUT_OFF_DATE,
                COMPENSATION_ITEM.EXPECTED_PAY_DATE,
                COMPENSATION_ITEM.CALCULATED_AMOUNT,
                COMPENSATION_ITEM.PREVIOUS_ID,
                COMPENSATION_ITEM.STATUS,
                COMPENSATION_ITEM.IS_ARREAR,
                COMPENSATION_ITEM.ARREAR_OF,
                COMPENSATION_ITEM.ARREAR_TRIGGER_REFERENCE,
                COMPENSATION_ITEM.UPDATE_TRIGGER_REFERENCE,
                COMPENSATION_ITEM.CREATED_ON,
                COMPENSATION_ITEM.CREATED_BY,
                COMPENSATION_ITEM.UPDATED_ON,
                COMPENSATION_ITEM.UPDATED_BY,
                COMPENSATION_ITEM.PAY_DATE,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
                COMPENSATION_.NOTES,
            )
            .from(COMPENSATION_ITEM)
            .leftJoin(PAY_SCHEDULE).on(COMPENSATION_ITEM.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
            .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_ITEM.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
            .leftJoin(COMPENSATION_).on(COMPENSATION_ITEM.COMPENSATION_ID.eq(COMPENSATION_.ID))
            .where(conditions)
            .fetch()
            .map { it.toCompensationItemEnriched() }
    }

    fun getAllValidCompensationItems(
        compensationId: UUID,
        transactionContext: TransactionContext,
    ): List<CompensationItem> = transactionContext.trx
        .selectFrom(COMPENSATION_ITEM)
        .where(
            COMPENSATION_ITEM.COMPENSATION_ID.eq(compensationId)
                .and(
                    COMPENSATION_ITEM.STATUS.ne(CompensationItemStatus.ABORTED.name),
                ),
        ).fetch()
        .map { it.toDomain() }

    fun findById(
        id: UUID,
        transactionContext: TransactionContext,
    ): CompensationItem? = transactionContext.trx
        .selectFrom(COMPENSATION_ITEM)
        .where(COMPENSATION_ITEM.ID.eq(id))
        .fetchOne()
        ?.toDomain()

    fun findByIds(
        ids: Collection<UUID>,
        transactionContext: TransactionContext,
    ): List<CompensationItem> = transactionContext.trx
        .selectFrom(COMPENSATION_ITEM)
        .where(COMPENSATION_ITEM.ID.`in`(ids))
        .fetch()
        .mapNotNull { it.toDomain() }

    fun fetchCompensationItemsForAmountCalculation(
        delayInMinutes: Long,
        batchSize: Int,
    ): List<CompensationItem> {
        val delay = LocalDateTime.now().minusMinutes(delayInMinutes)

        return dsl.selectFrom(COMPENSATION_ITEM)
            .where(COMPENSATION_ITEM.CALCULATED_AMOUNT.isNull)
            .and(COMPENSATION_ITEM.STATUS.notIn(CompensationItemStatus.ABORTED.name))
            .and(COMPENSATION_ITEM.UPDATED_ON.le(delay))
            .orderBy(COMPENSATION_ITEM.UPDATED_ON.desc())
            .limit(batchSize)
            .fetch()
            .map { it.toDomain() }
    }

    fun findIfArrearsExistForCompensationItem(
        id: UUID,
        transactionContext: TransactionContext,
    ): Boolean = transactionContext.trx
        .selectCount()
        .from(COMPENSATION_ITEM)
        .where(
            COMPENSATION_ITEM.ARREAR_OF.eq(id)
                .and(COMPENSATION_ITEM.STATUS.notIn(CompensationItemStatus.ABORTED.name)),
        )
        .fetchOne(0, Int::class.java)?.let { it > 0 }
        ?: false

    fun findEarliestCutoffForCompensation(
        compensationId: UUID,
        transactionContext: TransactionContext,
    ): LocalDate? = transactionContext.trx
        .select(DSL.min(COMPENSATION_ITEM.CUT_OFF_DATE))
        .from(COMPENSATION_ITEM)
        .where(
            COMPENSATION_ITEM.COMPENSATION_ID.eq(compensationId)
                .and(COMPENSATION_ITEM.STATUS.notIn(CompensationItemStatus.ABORTED.name)),
        )
        .fetchOne(0, LocalDate::class.java)

    fun findLastCompensationItem(
        compensationId: UUID,
        transactionContext: TransactionContext,
    ): CompensationItem? = transactionContext.trx
        .selectFrom(COMPENSATION_ITEM)
        .where(
            COMPENSATION_ITEM.COMPENSATION_ID.eq(compensationId)
                .and(COMPENSATION_ITEM.STATUS.notIn(CompensationItemStatus.ABORTED.name)),
        )
        .orderBy(COMPENSATION_ITEM.START_DATE.desc(), COMPENSATION_ITEM.UPDATED_ON.desc())
        .limit(1)
        .fetchOne()
        ?.toDomain()

    /**
     * Finds the last pay date for a given compensation.
     *
     * Returns:
     * - null if any compensation item has a null pay date
     * - null if all compensation items are aborted
     * - maximum pay date if all compensation items have non-null pay dates
     *
     * The query uses a CASE expression to:
     * 1. Check if any pay dates are null using COUNT
     * 2. Return the maximum pay date only when all pay dates are populated
     *
     * @param compensationId UUID of the core compensation
     * @param transactionContext Database transaction context
     * @return LocalDate? The last pay date or null based on conditions
     */
    fun findLastPayDateForCompensation(
        compensationId: UUID,
        transactionContext: TransactionContext,
    ): LocalDate? = transactionContext.trx
        .select(
            DSL.case_()
                .`when`(
                    DSL.count(DSL.`when`(COMPENSATION_ITEM.PAY_DATE.isNull, 1)).eq(0),
                    DSL.max(COMPENSATION_ITEM.PAY_DATE),
                )
                .else_(DSL.inline<LocalDate?>(null)),
        ).from(COMPENSATION_ITEM)
        .where(
            COMPENSATION_ITEM.COMPENSATION_ID.eq(compensationId)
                .and(COMPENSATION_ITEM.STATUS.notIn(CompensationItemStatus.ABORTED.name)),
        )
        .fetchOne(0, LocalDate::class.java)

    /**
     * Finds the compensation item with the most recent non-null cut_off_date
     * that is strictly before the current date, for a given compensation ID.
     * Excludes items with ABORTED status.
     */
    fun findLatestPastCutoffItem(
        compensationId: UUID,
        transactionContext: TransactionContext,
    ): CompensationItem? = transactionContext.trx
        .selectFrom(COMPENSATION_ITEM)
        .where(COMPENSATION_ITEM.COMPENSATION_ID.eq(compensationId))
        .and(COMPENSATION_ITEM.CUT_OFF_DATE.isNotNull)
        .and(COMPENSATION_ITEM.CUT_OFF_DATE.lt(LocalDate.now()))
        .and(COMPENSATION_ITEM.STATUS.ne(CompensationItemStatus.ABORTED.name))
        .orderBy(COMPENSATION_ITEM.CUT_OFF_DATE.desc())
        .limit(1)
        .fetchOne()
        ?.toDomain()

    fun findAllActiveVariablePayComponents(
        companyId: Long,
        endDate: LocalDate,
        transactionContext: TransactionContext,
    ): List<CompensationItemEnriched> = transactionContext.trx
        .select(
            // COMPENSATION_ITEM columns
            COMPENSATION_ITEM.ID,
            COMPENSATION_ITEM.COMPANY_ID,
            COMPENSATION_ITEM.ENTITY_ID,
            COMPENSATION_ITEM.CONTRACT_ID,
            COMPENSATION_ITEM.COMPENSATION_ID,
            COMPENSATION_ITEM.SCHEMA_ITEM_ID,
            COMPENSATION_ITEM.CATEGORY,
            COMPENSATION_ITEM.CURRENCY,
            COMPENSATION_ITEM.BILLING_RATE_TYPE,
            COMPENSATION_ITEM.BILLING_RATE,
            COMPENSATION_ITEM.BILLING_FREQUENCY,
            COMPENSATION_ITEM.PAY_SCHEDULE_ID,
            COMPENSATION_ITEM.COMPENSATION_START_DATE,
            COMPENSATION_ITEM.COMPENSATION_END_DATE,
            COMPENSATION_ITEM.COMPENSATION_STATUS,
            COMPENSATION_ITEM.START_DATE,
            COMPENSATION_ITEM.END_DATE,
            COMPENSATION_ITEM.IS_INSTALLMENT,
            COMPENSATION_ITEM.NO_OF_INSTALLMENTS,
            COMPENSATION_ITEM.CURRENT_INSTALLMENT,
            COMPENSATION_ITEM.CUT_OFF_DATE,
            COMPENSATION_ITEM.EXPECTED_PAY_DATE,
            COMPENSATION_ITEM.CALCULATED_AMOUNT,
            COMPENSATION_ITEM.PAY_DATE,
            COMPENSATION_ITEM.PREVIOUS_ID,
            COMPENSATION_ITEM.STATUS,
            COMPENSATION_ITEM.IS_ARREAR,
            COMPENSATION_ITEM.ARREAR_OF,
            COMPENSATION_ITEM.ARREAR_TRIGGER_REFERENCE,
            COMPENSATION_ITEM.UPDATE_TRIGGER_REFERENCE,
            COMPENSATION_ITEM.CREATED_ON,
            COMPENSATION_ITEM.CREATED_BY,
            COMPENSATION_ITEM.UPDATED_ON,
            COMPENSATION_ITEM.UPDATED_BY,
            PAY_SCHEDULE.PAY_SCHEDULE_NAME,
            PAY_SCHEDULE.FREQUENCY,
            COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
            COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
            COMPENSATION_SCHEMA_ITEM.IS_FIXED,
            COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
            COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
            COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
            COMPENSATION_SCHEMA_ITEM.CATEGORY,
            COMPENSATION_SCHEMA_ITEM.LABEL,
        )
        .from(COMPENSATION_ITEM)
        .leftJoin(PAY_SCHEDULE).on(COMPENSATION_ITEM.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
        .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_ITEM.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
        .where(
            COMPENSATION_ITEM.STATUS.eq(CompensationItemStatus.DRAFT.name)
                .and(COMPENSATION_ITEM.COMPANY_ID.eq(companyId))
                .and(COMPENSATION_ITEM.EXPECTED_PAY_DATE.lessOrEqual(endDate))
                .and(COMPENSATION_SCHEMA_ITEM.IS_FIXED.eq(false)),
        )
        .fetch()
        .map { it.toCompensationItemEnriched() }

    fun findAllUniqueActiveCompanyIdsForDateRange(
        endDate: LocalDate,
        transactionContext: TransactionContext,
    ): List<Long> = transactionContext.trx
        .selectDistinct(COMPENSATION_ITEM.COMPANY_ID)
        .from(COMPENSATION_ITEM)
        .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_ITEM.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
        .where(
            COMPENSATION_ITEM.STATUS.eq(CompensationItemStatus.DRAFT.name)
                .and(COMPENSATION_ITEM.EXPECTED_PAY_DATE.lessOrEqual(endDate))
                .and(COMPENSATION_SCHEMA_ITEM.IS_FIXED.eq(false)),
        )
        .fetch()
        .map { it.value1() }

    private fun Collection<CompensationItemRecord>.audit(transactionContext: TransactionContext) =
        transactionContext.trx.batchInsert(this.map { it.toAudit(transactionContext.audit) }).execute()

    private fun CompensationItemRecord.audit(transactionContext: TransactionContext) =
        transactionContext.trx.insertInto(COMPENSATION_ITEM_AUD)
            .set(this.toAudit(transactionContext.audit))
            .execute()
}
