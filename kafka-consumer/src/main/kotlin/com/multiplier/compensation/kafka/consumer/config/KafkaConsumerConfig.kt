package com.multiplier.compensation.kafka.consumer.config

import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.kafka.consumer.consumer.CompensationItemUpdateConsumer
import com.multiplier.messaging.api.consumer.registerConsumers
import com.multiplier.messaging.payrollinputservice.CompensationItemUpdateTopic
import com.multiplier.messaging.payrollinputservice.DeductionItemUpdateTopic
import com.multiplier.messaging.payrollinputservice.PaySupplementItemUpdateTopic
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * Configuration class for Kafka consumers.
 *
 * @property compensationItemUpdateConsumer Consumer for compensation item updates.
 */
@Configuration
class KafkaConsumerConfig(
    private val compensationItemUpdateConsumer: CompensationItemUpdateConsumer,
) {
    @Bean
    fun kafkaListeners() = registerConsumers {
        onMessage(CompensationItemUpdateTopic) { message ->
            compensationItemUpdateConsumer.consume(message, RequestType.COMPENSATION_SETUP)
        }

        onMessage(DeductionItemUpdateTopic) { message ->
            compensationItemUpdateConsumer.consume(message, RequestType.DEDUCTION)
        }

        onMessage(PaySupplementItemUpdateTopic) { message ->
            compensationItemUpdateConsumer.consume(message, RequestType.PAY_SUPPLEMENT)
        }

        /*onMessage(SalaryRevisionUpdateTopic) { message ->
            compensationItemUpdateConsumer.consume(message, RequestType.COMPENSATION_REVISION)
        }*/
    }
}
