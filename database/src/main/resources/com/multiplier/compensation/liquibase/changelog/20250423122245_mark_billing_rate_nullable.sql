-- liquibase formatted sql

-- changeset <PERSON>eh<PERSON>lani:20250423122245_mark_billing_rate_nullable

-- Make billing_rate nullable in compensation.compensation
ALTER TABLE compensation.compensation
ALTER COLUMN billing_rate DROP NOT NULL;

-- Make billing_rate nullable in compensation.compensation_aud
ALTER TABLE compensation.compensation_aud
ALTER COLUMN billing_rate DROP NOT NULL;

-- Make billing_rate nullable in compensation.compensation_input
ALTER TABLE compensation.compensation_input
ALTER COLUMN billing_rate DROP NOT NULL;

-- Make billing_rate nullable in compensation.compensation_input_aud
ALTER TABLE compensation.compensation_input_aud
ALTER COLUMN billing_rate DROP NOT NULL;

-- Make billing_rate nullable in compensation.compensation_item
ALTER TABLE compensation.compensation_item
ALTER COLUMN billing_rate DROP NOT NULL;

-- Make billing_rate nullable in compensation.compensation_item_aud
ALTER TABLE compensation.compensation_item_aud
ALTER COLUMN billing_rate DROP NOT NULL;
