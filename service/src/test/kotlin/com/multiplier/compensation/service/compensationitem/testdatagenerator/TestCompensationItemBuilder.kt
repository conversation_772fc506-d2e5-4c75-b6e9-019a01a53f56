package com.multiplier.compensation.service.compensationitem.testdatagenerator

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import java.time.LocalDate
import java.util.UUID

class TestCompensationItemBuilder {
    private companion object TestData {
        val DEFAULT_COMPENSATION: Compensation = TestCompensationBuilder().build()
        val DEFAULT_START_DATE: LocalDate = LocalDate.of(2024, 1, 1)
        val DEFAULT_END_DATE: LocalDate = DEFAULT_START_DATE.plusMonths(1)
        val DEFAULT_CUT_OFF_DATE: LocalDate? = null
        val DEFAULT_EXPECTED_PAY_DATE: LocalDate = DEFAULT_END_DATE.plusDays(15)
        val DEFAULT_PAY_DATE: LocalDate = DEFAULT_EXPECTED_PAY_DATE
        const val DEFAULT_IS_ARREAR = false
        const val DEFAULT_CALCULATED_AMOUNT: Double = 100.0
        val DEFAULT_ARREAR_OF: UUID? = null
        val DEFAULT_ARREAR_TRIGGER_REFERENCE: String? = null
        val DEFAULT_UPDATE_TRIGGER_REFERENCE: String? = null
        val DEFAULT_CURRENT_INSTALLMENT: Int? = null
        val DEFAULT_COMPENSATION_ITEM_STATUS = CompensationItemStatus.NEW
    }

    private var compensation: Compensation = DEFAULT_COMPENSATION
    private var startDate: LocalDate = DEFAULT_START_DATE
    private var endDate: LocalDate = DEFAULT_END_DATE
    private var cutOffDate: LocalDate? = DEFAULT_CUT_OFF_DATE
    private var calculatedAmount: Double? = DEFAULT_CALCULATED_AMOUNT
    private var isArrear: Boolean = DEFAULT_IS_ARREAR
    private var arrearOf: UUID? = DEFAULT_ARREAR_OF
    private var arrearTriggerReference: String? = DEFAULT_ARREAR_TRIGGER_REFERENCE
    private var updateTriggerReference: String? = DEFAULT_UPDATE_TRIGGER_REFERENCE
    private var currentInstallment: Int? = DEFAULT_CURRENT_INSTALLMENT
    private var status: CompensationItemStatus = DEFAULT_COMPENSATION_ITEM_STATUS

    fun withCompensation(compensation: Compensation) = apply { this.compensation = compensation }

    fun withStartDate(date: LocalDate) = apply { this.startDate = date }

    fun withEndDate(date: LocalDate) = apply { this.endDate = date }

    fun withCutOffDate(date: LocalDate?) = apply { this.cutOffDate = date }

    fun withCalculatedAmount(calculatedAmount: Double?) = apply { this.calculatedAmount = calculatedAmount }

    fun withIsArrear(isArrear: Boolean) = apply { this.isArrear = isArrear }

    fun withArrearOf(id: UUID?) = apply { this.arrearOf = id }

    fun withArrearTriggerReference(arrearTriggerReference: String) = apply {
        this.arrearTriggerReference = arrearTriggerReference
    }

    fun withUpdateTriggerReference(updateTriggerReference: String?) = apply {
        this.updateTriggerReference = updateTriggerReference
    }

    fun withCurrentInstallment(currentInstallment: Int?) = apply { this.currentInstallment = currentInstallment }

    fun withStatus(status: CompensationItemStatus) = apply { this.status = status }

    fun build() = CompensationItem(
        id = UUID.randomUUID(),
        companyId = compensation.companyId,
        entityId = compensation.entityId,
        contractId = compensation.contractId,
        compensationId = compensation.id,
        schemaItemId = compensation.schemaItemId,
        category = compensation.category,
        currency = compensation.currency,
        billingRateType = compensation.billingRateType,
        billingRate = compensation.billingRate,
        billingFrequency = compensation.billingFrequency,
        payScheduleId = compensation.payScheduleId,
        compensationStartDate = compensation.startDate,
        compensationEndDate = compensation.endDate,
        compensationStatus = compensation.status,
        startDate = startDate,
        endDate = endDate,
        isInstallment = compensation.isInstallment,
        noOfInstallments = compensation.noOfInstallments,
        currentInstallment = currentInstallment,
        cutOffDate = cutOffDate,
        expectedPayDate = DEFAULT_EXPECTED_PAY_DATE,
        calculatedAmount = calculatedAmount,
        payDate = DEFAULT_PAY_DATE,
        previousId = null,
        status = status,
        isArrear = isArrear,
        arrearOf = arrearOf,
        arrearTriggerReference = arrearTriggerReference,
        updateTriggerReference = updateTriggerReference,
    )
}
