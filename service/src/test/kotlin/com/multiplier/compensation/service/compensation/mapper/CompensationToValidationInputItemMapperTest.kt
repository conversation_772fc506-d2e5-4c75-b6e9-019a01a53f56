package com.multiplier.compensation.service.compensation.mapper

import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationFixture
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.util.UUID

class CompensationToValidationInputItemMapperTest {
    @Test
    fun `toRowItem should handle non-null billingRate correctly`() {
        // Given
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()
        val billingRate = 1000.0

        val compensation = compensationFixture(
            schemaItemId = schemaItemId,
            payScheduleId = payScheduleId,
            billingRate = billingRate,
        )

        val schemaItems = mapOf(
            schemaItemId.toString() to mockk<CompensationSchemaItem> {
                every { componentName } returns "Test Component"
            },
        )

        val paySchedules = mapOf(
            payScheduleId.toString() to mockk<PaySchedule> {
                every { name } returns "Monthly"
            },
        )

        // When
        val result = compensation.toRowItem(schemaItems, paySchedules)

        // Then
        val billingRateKeyValuePair = result.keyValuePairs.find {
            it.key == CompensationSkeletonField.BILLING_RATE.id
        }

        assertEquals(billingRate.toString(), billingRateKeyValuePair?.value)
    }

    @Test
    fun `toRowItem should handle null billingRate correctly`() {
        // Given
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()

        // Create a compensation with billing rate
        val compensation = compensationFixture(
            schemaItemId = schemaItemId,
            payScheduleId = payScheduleId,
        )

        // Create a modified version with null billing rate using copy
        val compensationWithNullRate = compensation.copy(billingRate = null)

        val schemaItems = mapOf(
            schemaItemId.toString() to mockk<CompensationSchemaItem> {
                every { componentName } returns "Test Component"
            },
        )

        val paySchedules = mapOf(
            payScheduleId.toString() to mockk<PaySchedule> {
                every { name } returns "Monthly"
            },
        )

        // When
        val result = compensationWithNullRate.toRowItem(schemaItems, paySchedules)

        // Then
        val billingRateKeyValuePair = result.keyValuePairs.find {
            it.key == CompensationSkeletonField.BILLING_RATE.id
        }

        assertNull(billingRateKeyValuePair?.value)
    }

    @Test
    fun `toRowItem should map basic fields correctly`() {
        // Given
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()
        val entityId = 12345L
        val contractId = 67890L
        val companyId = 54321L

        val compensation = compensationFixture(
            schemaItemId = schemaItemId,
            payScheduleId = payScheduleId,
            entityId = entityId,
            contractId = contractId,
            companyId = companyId,
        )

        val componentName = "Base Salary"
        val payScheduleName = "Monthly"

        val schemaItems = mapOf(
            schemaItemId.toString() to mockk<CompensationSchemaItem> {
                every { <EMAIL> } returns componentName
            },
        )

        val paySchedules = mapOf(
            payScheduleId.toString() to mockk<PaySchedule> {
                every { <EMAIL> } returns payScheduleName
            },
        )

        // When
        val result = compensation.toRowItem(schemaItems, paySchedules)

        // Then
        val keyValuePairs = result.keyValuePairs.associateBy { it.key }

        assertEquals(entityId.toString(), keyValuePairs[CommonSkeletonField.ENTITY_ID.id]?.value)
        assertEquals(contractId.toString(), keyValuePairs[CommonSkeletonField.CONTRACT_ID.id]?.value)
        assertEquals(companyId.toString(), keyValuePairs[CommonSkeletonField.COMPANY_ID.id]?.value)
        assertEquals(componentName, keyValuePairs[CompensationSkeletonField.COMPONENT_NAME.id]?.value)
        assertEquals(payScheduleName, keyValuePairs[CompensationSkeletonField.PAY_SCHEDULE_NAME.id]?.value)
    }

    @Test
    fun `toRowItem should handle missing schema item and pay schedule`() {
        // Given
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()

        val compensation = compensationFixture(
            schemaItemId = schemaItemId,
            payScheduleId = payScheduleId,
        )

        // Empty maps for schema items and pay schedules
        val schemaItems = emptyMap<String, CompensationSchemaItem>()
        val paySchedules = emptyMap<String, PaySchedule>()

        // When
        val result = compensation.toRowItem(schemaItems, paySchedules)

        // Then
        val componentNamePair = result.keyValuePairs.find { it.key == CompensationSkeletonField.COMPONENT_NAME.id }
        val payScheduleNamePair = result.keyValuePairs.find { it.key == CompensationSkeletonField.PAY_SCHEDULE_NAME.id }

        assertNotNull(componentNamePair)
        assertNotNull(payScheduleNamePair)
        assertNull(componentNamePair?.value)
        assertNull(payScheduleNamePair?.value)
    }

    @Test
    fun `toRowItem should handle installment flag correctly`() {
        // Given
        val schemaItemId = UUID.randomUUID()
        val payScheduleId = UUID.randomUUID()

        // Create a compensation with installment flag set to true
        val compensation = compensationFixture(
            schemaItemId = schemaItemId,
            payScheduleId = payScheduleId,
            isInstallment = true,
        )

        val schemaItems = mapOf(
            schemaItemId.toString() to mockk<CompensationSchemaItem> {
                every { componentName } returns "Bonus"
            },
        )

        val paySchedules = mapOf(
            payScheduleId.toString() to mockk<PaySchedule> {
                every { name } returns "Quarterly"
            },
        )

        // When
        val result = compensation.toRowItem(schemaItems, paySchedules)

        // Then
        val isInstallmentPair = result.keyValuePairs.find { it.key == CompensationSkeletonField.IS_INSTALLMENT.id }

        assertNotNull(isInstallmentPair)
        assertEquals("Yes", isInstallmentPair?.value)
    }
}
