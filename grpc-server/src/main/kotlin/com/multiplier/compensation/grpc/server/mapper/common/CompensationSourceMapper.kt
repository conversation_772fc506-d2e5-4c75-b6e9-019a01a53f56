package com.multiplier.compensation.grpc.server.mapper.common

import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.grpc.schema.Common.CompensationRequestType as Grpc

fun RequestType.toGrpc() = when (this) {
    RequestType.COMPENSATION_SETUP, RequestType.COMPENSATION_BACKFILL ->
        Grpc.COMPENSATION_REQUEST_TYPE_COMPENSATION_SETUP
    RequestType.COMPENSATION_REVISION, RequestType.COMPENSATION_REVISION_BACKFILL,
    -> Grpc.COMPENSATION_REQUEST_TYPE_COMPENSATION_REVISION
    RequestType.PAY_SUPPLEMENT -> Grpc.COMPENSATION_REQUEST_TYPE_COMPENSATION_SETUP
    RequestType.DEDUCTION -> Grpc.COMPENSATION_REQUEST_TYPE_COMPENSATION_REVISION
}
