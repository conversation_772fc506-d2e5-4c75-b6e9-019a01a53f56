package com.multiplier.compensation.database.repository.payschedule

import com.multiplier.compensation.database.mapper.payschedule.applyAudit
import com.multiplier.compensation.database.mapper.payschedule.applyDelete
import com.multiplier.compensation.database.mapper.payschedule.toDatabase
import com.multiplier.compensation.database.mapper.payschedule.toDomain
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE_ITEM
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.payschedule.PayScheduleItem
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.UUID

@Service
class PayScheduleItemRepository(
    private val dsl: DSLContext,
    private val payScheduleRepository: PayScheduleRepository,
) {
    fun findAllByPayScheduleIdAndDateRangeBetween(
        payScheduleId: UUID,
        from: LocalDate,
        to: LocalDate,
    ): Collection<PayScheduleItem> {
        val paySchedule = payScheduleRepository.findById(payScheduleId)
            ?: throw InvalidArgumentException(
                errorCode = ValidationErrorCode.InvalidPaySchedule,
                message = "Pay schedule with id [$payScheduleId] not found",
                context = mapOf("payScheduleId" to payScheduleId),
            )

        return dsl.selectFrom(PAY_SCHEDULE_ITEM)
            .where(PAY_SCHEDULE_ITEM.PAY_SCHEDULE_ID.eq(payScheduleId))
            .and(PAY_SCHEDULE_ITEM.END_DATE.ge(from))
            .and(PAY_SCHEDULE_ITEM.START_DATE.le(to))
            .orderBy(PAY_SCHEDULE_ITEM.START_DATE)
            .fetch().map { it.toDomain(paySchedule) }
    }

    fun findAllByPayScheduleIdsAndDateRangeBetween(
        payScheduleIds: List<UUID>,
        from: LocalDate,
        to: LocalDate,
    ): Collection<PayScheduleItem> {
        val payScheduleById = payScheduleRepository.findAllByIds(payScheduleIds)
            .associateBy { it.id }

        return dsl.selectFrom(PAY_SCHEDULE_ITEM)
            .where(PAY_SCHEDULE_ITEM.PAY_SCHEDULE_ID.`in`(payScheduleById.keys))
            .and(PAY_SCHEDULE_ITEM.END_DATE.ge(from))
            .and(PAY_SCHEDULE_ITEM.START_DATE.le(to))
            .orderBy(PAY_SCHEDULE_ITEM.START_DATE)
            .fetch { payScheduleItemRecord ->
                payScheduleById[payScheduleItemRecord.payScheduleId]
                    ?.let { payScheduleItemRecord.toDomain(it) }
            }
    }

    fun saveAll(
        payScheduleItems: Collection<PayScheduleItem>,
        transactionContext: TransactionContext,
    ) {
        val records = payScheduleItems.map { it.toDatabase().applyAudit(transactionContext.audit) }
        transactionContext.trx.batchInsert(records).execute()
    }

    fun deleteAll(
        payScheduleItems: Collection<PayScheduleItem>,
        transactionContext: TransactionContext,
    ) {
        val records = payScheduleItems.map {
            it.toDatabase().applyAudit(transactionContext.audit).applyDelete()
        }
        transactionContext.trx.batchUpdate(records).execute()
    }
}
