package com.multiplier.compensation.service.compensationitem.update.processors

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.adjusters.ImplicitBillingRateUpdateAdjuster
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.assertEquals

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ImplicitBillingRateUpdateProcessorTest {
    private companion object TestData {
        val compensation = mockk<Compensation>()

        fun contextBuilder() = TestCompensationUpdateContextBuilder()
            .withOldImage(compensation)
            .withNewImage(compensation)
            .withNewRecord(compensation)
    }

    private val implicitUpdateAdjuster = mockk<ImplicitBillingRateUpdateAdjuster>()
    private lateinit var implicitUpdateProcessor: ImplicitBillingRateUpdateProcessor

    @BeforeEach
    fun setup() {
        implicitUpdateProcessor = ImplicitBillingRateUpdateProcessor(implicitUpdateAdjuster)
    }

    @ParameterizedTest
    @MethodSource("provideTestCasesWithInvalidContext")
    fun `should throw exception when invalid context`(
        context: CompensationUpdateContext,
        expectedMessage: String,
    ) {
        val exception = assertThrows<IllegalArgumentException> { implicitUpdateProcessor.process(context) }
        assertEquals(expectedMessage, exception.message)
        verify(exactly = 0) { implicitUpdateAdjuster.adjust(any()) }
    }

    fun provideTestCasesWithInvalidContext(): List<Arguments> = listOf(
        Arguments.of(
            contextBuilder()
                .withOldImage(null)
                .withNewRecord(null)
                .withParentUpdateContext(mockk())
                .build(),
            "Old image should not be null.",
        ),
        Arguments.of(
            contextBuilder()
                .withNewImage(null)
                .withNewRecord(null)
                .withParentUpdateContext(mockk())
                .build(),
            "New image should not be null.",
        ),
        Arguments.of(
            contextBuilder()
                .withOldImage(mockk())
                .withNewImage(mockk())
                .withNewRecord(null)
                .withParentUpdateContext(mockk())
                .build(),
            "Old image and New image should be identical.",
        ),
        Arguments.of(
            contextBuilder()
                .withParentUpdateContext(mockk())
                .build(),
            "New record should be null.",
        ),
        Arguments.of(
            contextBuilder()
                .withNewRecord(null)
                .build(),
            "Parent update context should not be null.",
        ),
        Arguments.of(
            contextBuilder()
                .withNewRecord(null)
                .withParentUpdateContext(contextBuilder().withOldImage(null).build())
                .build(),
            "Parent update context's old image should not be null.",
        ),
        Arguments.of(
            contextBuilder()
                .withNewRecord(null)
                .withParentUpdateContext(contextBuilder().withNewImage(null).build())
                .build(),
            "Parent update context's new image should not be null.",
        ),
        Arguments.of(
            contextBuilder()
                .withNewRecord(null)
                .withParentUpdateContext(contextBuilder().withNewRecord(null).build())
                .build(),
            "Parent update context's new record should not be null.",
        ),
    )

    @Test
    fun `should return context when valid request`() {
        val initialContext = contextBuilder()
            .withNewRecord(null)
            .withParentUpdateContext(contextBuilder().build())
            .build()

        every {
            implicitUpdateAdjuster.adjust(initialContext)
        } returns initialContext

        implicitUpdateProcessor.process(initialContext)

        verify(exactly = 1) { implicitUpdateAdjuster.adjust(initialContext) }
    }
}
