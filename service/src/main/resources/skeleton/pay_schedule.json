{"skeletonType": ["PAY_SCHEDULE"], "keys": ["ENTITY_ID", "COMPANY_ID", "COUNTRY_CODE"], "structure": [{"fieldId": "PAY_SCHEDULE_NAME", "fieldName": "Pay Schedule Name", "valueType": "STRING", "validationRegex": {"regex": ".*", "errorMessage": "Should be text"}, "description": "Name / Reference of Pay Schedule", "mandatory": true}, {"fieldId": "PAY_SCHEDULE_FREQUENCY", "fieldName": "Pay Schedule Frequency", "valueType": "SELECT", "description": "Frequency of the pay schedule.", "possibleValues": [{"op": "PAY_SCHEDULE_FREQUENCY_ENRICHER"}], "mandatory": true}, {"fieldId": "REFERENCE_PERIOD_START_DATE", "fieldName": "Reference Period Start Date", "valueType": "DATE", "description": "Start Date of one instance. Format - YYYY-MM-DD", "mandatory": true}, {"fieldId": "REFERENCE_PERIOD_END_DATE", "fieldName": "Reference Period End Date", "valueType": "DATE", "description": "End Date of that instance. Format - YYYY-MM-DD", "mandatory": true}, {"fieldId": "PAY_DATE_RELATIVE_DAYS", "fieldName": "Pay Date Relative Days", "valueType": "INTEGER", "description": "Pay Date relative days wrt Pay Date Reference Type", "mandatory": true}, {"fieldId": "PAY_DATE_REFERENCE_TYPE", "fieldName": "Pay Date Reference Type", "valueType": "SELECT", "description": "Reference date for calculating pay date", "possibleValues": [{"op": "PAY_DATE_REFERENCE_ENRICHER"}], "mandatory": true}, {"fieldId": "IS_INSTALLMENT", "fieldName": "Is Installment", "description": "Flag indicating whether component is an installment.", "valueType": "SELECT", "possibleValues": [{"value": "Yes"}, {"value": "No"}], "mandatory": true}, {"fieldId": "LABEL", "fieldName": "Label", "valueType": "STRING", "validationRegex": {"regex": ".*", "errorMessage": "Should be text"}, "description": "Label for the Pay Schedule.", "mandatory": true}]}