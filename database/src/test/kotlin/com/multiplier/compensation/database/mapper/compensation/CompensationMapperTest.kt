package com.multiplier.compensation.database.mapper.compensation

import com.multiplier.compensation.database.enums.BillingFrequency
import com.multiplier.compensation.database.enums.BillingRateType
import com.multiplier.compensation.database.enums.CompensationStatus
import com.multiplier.compensation.database.enums.PayScheduleFrequency
import com.multiplier.compensation.database.tables.records.CompensationRecord
import com.multiplier.compensation.database.tables.references.COMPENSATION_
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA_ITEM
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.jooq.Record
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull

class CompensationMapperTest {
    @Test
    fun `test toDomain mapping`() {
        // Mock input record
        val compensationRecord = mockk<CompensationRecord> {
            every { id } returns UUID.randomUUID()
            every { companyId } returns 1L
            every { entityId } returns 2L
            every { contractId } returns 3L
            every { schemaItemId } returns UUID.randomUUID()
            every { category } returns "BASE_PAY"
            every { currency } returns "USD"
            every { billingRateType } returns BillingRateType.VALUE
            every { billingRate } returns 100.0
            every { billingFrequency } returns BillingFrequency.MONTHLY
            every { payScheduleId } returns UUID.randomUUID()
            every { startDate } returns LocalDate.of(2023, 1, 1)
            every { endDate } returns LocalDate.of(2023, 12, 31)
            every { processingFrom } returns LocalDateTime.of(2023, 1, 1, 0, 0)
            every { processingTo } returns LocalDateTime.of(2023, 12, 31, 23, 59)
            every { isInstallment } returns true
            every { noOfInstallments } returns 12
            every { status } returns CompensationStatus.NEW
            every { notes } returns "Test notes"
            every { generatedInstallments } returns 10
            every { processedUntilDate } returns LocalDate.of(2023, 6, 1)
            every { createdOn } returns LocalDateTime.of(2023, 1, 1, 12, 0)
            every { createdBy } returns -1
            every { updatedOn } returns LocalDateTime.of(2023, 5, 1, 12, 0)
            every { updatedBy } returns -1
            every { previousId } returns UUID.randomUUID()
        }

        // Perform mapping
        val result = compensationRecord.toDomain()

        // Validate result
        assertEquals(compensationRecord.id, result.id)
        assertEquals(compensationRecord.companyId, result.companyId)
        assertEquals(compensationRecord.entityId, result.entityId)
        assertEquals(compensationRecord.contractId, result.contractId)
        assertEquals(compensationRecord.schemaItemId, result.schemaItemId)
        assertEquals(compensationRecord.category, result.category)
        assertEquals(compensationRecord.currency, result.currency)
        assertEquals(com.multiplier.compensation.domain.common.BillingRateType.VALUE, result.billingRateType)
        assertEquals(compensationRecord.billingRate, result.billingRate)
        assertEquals(com.multiplier.compensation.domain.common.BillingFrequency.MONTHLY, result.billingFrequency)
        assertEquals(compensationRecord.payScheduleId, result.payScheduleId)
        assertEquals(compensationRecord.startDate, result.startDate)
        assertEquals(compensationRecord.endDate, result.endDate)
        assertEquals(compensationRecord.processingFrom?.toLocalDate(), result.processingFrom)
        assertEquals(compensationRecord.processingTo?.toLocalDate(), result.processingTo)
        assertEquals(compensationRecord.isInstallment, result.isInstallment)
        assertEquals(compensationRecord.noOfInstallments, result.noOfInstallments)
        assertEquals(com.multiplier.compensation.domain.compensation.enums.CompensationStatus.NEW, result.status)
        assertEquals(compensationRecord.generatedInstallments, result.generatedInstallments)
        assertEquals(compensationRecord.processedUntilDate, result.processedUntilDate)
        assertEquals(compensationRecord.notes, result.notes)
        assertEquals(compensationRecord.createdOn, result.createdOn)
        assertEquals(compensationRecord.createdBy, result.createdBy)
        assertEquals(compensationRecord.updatedOn, result.updatedOn)
        assertEquals(compensationRecord.updatedBy, result.updatedBy)
        assertEquals(compensationRecord.previousId, result.previousId)
    }

    @Test
    fun `test toDomain mapping with null fields`() {
        // Mock input record with null values
        val compensationRecord = mockk<CompensationRecord> {
            every { id } returns UUID.randomUUID()
            every { companyId } returns 1L
            every { entityId } returns 2L
            every { contractId } returns 3L
            every { schemaItemId } returns UUID.randomUUID()
            every { currency } returns "USD"
            every { category } returns null
            every { endDate } returns null
            every { previousId } returns null
            every { billingRate } returns 0.0
            every { status } returns CompensationStatus.NEW
            every { isInstallment } returns false
            every { noOfInstallments } returns null
            every { billingRateType } returns BillingRateType.VALUE
            every { billingFrequency } returns BillingFrequency.MONTHLY
            every { payScheduleId } returns UUID.randomUUID()
            every { startDate } returns LocalDate.of(2023, 1, 1)
            every { processingFrom } returns LocalDateTime.of(2023, 1, 1, 0, 0)
            every { processingTo } returns LocalDateTime.of(2023, 12, 31, 23, 59)
            every { generatedInstallments } returns 10
            every { processedUntilDate } returns LocalDate.of(2023, 6, 1)
            every { notes } returns null
            every { createdOn } returns LocalDateTime.of(2023, 1, 1, 12, 0)
            every { createdBy } returns -1
            every { updatedOn } returns LocalDateTime.of(2023, 5, 1, 12, 0)
            every { updatedBy } returns -1
        }

        // Perform mapping
        val result = compensationRecord.toDomain()

        // Validate result
        assertEquals(compensationRecord.id, result.id)
        assertEquals(compensationRecord.companyId, result.companyId)
        assertEquals(compensationRecord.entityId, result.entityId)
        assertEquals(compensationRecord.contractId, result.contractId)
        assertEquals(compensationRecord.schemaItemId, result.schemaItemId)
        assertNull(result.category)
        assertNull(result.endDate)
        assertNull(result.previousId)
        assertEquals(0.0, result.billingRate)
        assertEquals(false, result.isInstallment)
        assertNull(result.noOfInstallments)
        assertNull(result.notes)
    }

    @Test
    fun `should map Record to CompensationRecordsEnriched`() {
        val record = mockk<Record>(relaxed = true)

        every { record[COMPENSATION_.ID] } returns UUID.randomUUID()
        every { record[COMPENSATION_.COMPANY_ID] } returns 2L
        every { record[COMPENSATION_.ENTITY_ID] } returns 3L
        every { record[COMPENSATION_.CONTRACT_ID] } returns 4L
        every { record[COMPENSATION_.CATEGORY] } returns "Category"
        every { record[COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME] } returns "Component Name"
        every { record[COMPENSATION_.CURRENCY] } returns "USD"
        every { record[COMPENSATION_.BILLING_RATE_TYPE] } returns BillingRateType.BASE_PAY_PERCENTAGE
        every { record[COMPENSATION_.BILLING_RATE] } returns 100.0
        every { record[COMPENSATION_.BILLING_FREQUENCY] } returns BillingFrequency.MONTHLY
        every { record[PAY_SCHEDULE.PAY_SCHEDULE_NAME] } returns "Bi-Weekly"
        every { record[PAY_SCHEDULE.FREQUENCY] } returns PayScheduleFrequency.MONTHLY
        every { record[COMPENSATION_.START_DATE] } returns LocalDate.of(2021, 1, 1)
        every { record[COMPENSATION_.END_DATE] } returns LocalDate.of(2021, 12, 31)
        every { record[COMPENSATION_.PROCESSING_FROM] } returns LocalDateTime.of(2021, 1, 1, 0, 0)
        every { record[COMPENSATION_.PROCESSING_TO] } returns LocalDateTime.of(2021, 12, 31, 23, 59)
        every { record[COMPENSATION_.IS_INSTALLMENT] } returns false
        every { record[COMPENSATION_.NO_OF_INSTALLMENTS] } returns 12
        every { record[COMPENSATION_.GENERATED_INSTALLMENTS] } returns 6
        every { record[COMPENSATION_SCHEMA_ITEM.IS_TAXABLE] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.IS_FIXED] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.IS_PRORATED] } returns false
        every { record[COMPENSATION_SCHEMA_ITEM.IS_MANDATORY] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY] } returns true
        every { record[COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC] } returns true
        every { record[COMPENSATION_.PREVIOUS_ID] } returns UUID.randomUUID()
        every { record[COMPENSATION_.STATUS] } returns CompensationStatus.NEW
        every { record[COMPENSATION_.NOTES] } returns "Notes"
        every { record[COMPENSATION_.CREATED_BY] } returns 7L
        every { record[COMPENSATION_.CREATED_ON] } returns LocalDateTime.now()
        every { record[COMPENSATION_.UPDATED_BY] } returns 8L
        every { record[COMPENSATION_.UPDATED_ON] } returns LocalDateTime.now()
        every { record[COMPENSATION_SCHEMA_ITEM.LABEL] } returns "Label"
        every { record[COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE] } returns false

        val enrichedRecord = record.toCompensationRecordsEnriched()

        assertEquals(2L, enrichedRecord.companyId)
        assertEquals(3L, enrichedRecord.entityId)
        assertEquals(4L, enrichedRecord.contractId)
        assertEquals("Category", enrichedRecord.category)
        assertEquals("Component Name", enrichedRecord.schemaComponentName)
        assertEquals("USD", enrichedRecord.currency)
        assertEquals(100.0, enrichedRecord.billingRate)
        assertEquals("Bi-Weekly", enrichedRecord.payScheduleName)
        assertEquals(false, enrichedRecord.isInstallment)
        assertEquals(12, enrichedRecord.noOfInstallments)
        assertEquals(6, enrichedRecord.generatedInstallments)
        assertEquals(true, enrichedRecord.isTaxable)
        assertEquals(true, enrichedRecord.isFixed)
        assertEquals(false, enrichedRecord.isProrated)
        assertEquals(true, enrichedRecord.isMandatory)
        assertEquals(true, enrichedRecord.isPartOfBasePay)
        assertEquals(7L, enrichedRecord.createdBy)
        assertEquals(8L, enrichedRecord.updatedBy)
        assertEquals("Notes", enrichedRecord.notes)
        assertEquals("Label", enrichedRecord.label)
        assertEquals(false, enrichedRecord.isOvertimeEligible)
    }

    @Test
    fun `applyUpdateOnly should call changed with false for all specified fields`() {
        // Mock the CompensationRecord
        val mockRecord = mockk<CompensationRecord>(relaxed = true)

        // Call the function to test
        mockRecord.applyUpdateOnly()

        // Verify that `changed` was called with `false` for all specified fields
        verify { mockRecord.changed(COMPENSATION_.ID, false) }
        verify { mockRecord.changed(COMPENSATION_.COMPANY_ID, false) }
        verify { mockRecord.changed(COMPENSATION_.ENTITY_ID, false) }
        verify { mockRecord.changed(COMPENSATION_.CONTRACT_ID, false) }
        verify { mockRecord.changed(COMPENSATION_.SCHEMA_ITEM_ID, false) }
        verify { mockRecord.changed(COMPENSATION_.CURRENCY, false) }
        verify { mockRecord.changed(COMPENSATION_.BILLING_RATE, false) }
        verify { mockRecord.changed(COMPENSATION_.BILLING_RATE_TYPE, false) }
        verify { mockRecord.changed(COMPENSATION_.BILLING_FREQUENCY, false) }
        verify { mockRecord.changed(COMPENSATION_.PAY_SCHEDULE_ID, false) }
        verify { mockRecord.changed(COMPENSATION_.IS_INSTALLMENT, false) }
        verify { mockRecord.changed(COMPENSATION_.PREVIOUS_ID, false) }
        verify { mockRecord.changed(COMPENSATION_.CREATED_ON, false) }
        verify { mockRecord.changed(COMPENSATION_.CREATED_BY, false) }

        // Verify no other interactions
        io.mockk.confirmVerified(mockRecord)
    }
}
