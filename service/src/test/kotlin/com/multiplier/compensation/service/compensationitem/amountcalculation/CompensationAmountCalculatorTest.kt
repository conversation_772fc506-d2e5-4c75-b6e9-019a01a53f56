package com.multiplier.compensation.service.compensationitem.amountcalculation

import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompensationAmountCalculatorTest {
    private lateinit var calculator: CompensationItemAmountCalculator
    private lateinit var compensationRepository: CompensationRepository

    @BeforeEach
    fun setUp() {
        compensationRepository = mockk()
        calculator = CompensationItemAmountCalculator(compensationRepository)
    }

    @Test
    fun `calculate should return billingRate for VALUE type`() {
        val compensation = TestCompensationBuilder()
            .withBillingRateType(BillingRateType.VALUE)
            .withBillingRate(1000.0)
            .build()
        val compensationItem = TestCompensationItemBuilder()
            .withCompensation(compensation)
            .build()

        val result = calculator.calculate(compensationItem)

        assertEquals(1000.0, result)
        verify(exactly = 0) { compensationRepository.fetchBasePayComponentsForContract(any(), any(), any()) }
    }

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideCompensationCalculationTestCases")
    fun `calculate should compute correct amount for different scenarios`(
        basePays: List<Compensation>,
        expectedAmount: Double,
        description: String,
    ) {
        val compensation = TestCompensationBuilder()
            .withBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE)
            .withBillingRate(20.0)
            .build()
        val compensationItem = TestCompensationItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 10))
            .withEndDate(LocalDate.of(2024, 1, 25))
            .withCompensation(compensation)
            .build()

        every {
            compensationRepository.fetchBasePayComponentsForContract(any(), any(), any())
        } returns basePays

        val result = calculator.calculate(compensationItem)

        assertEquals(expectedAmount, result, description)
        verify { compensationRepository.fetchBasePayComponentsForContract(any(), any(), any()) }
    }

    fun provideCompensationCalculationTestCases(): List<Arguments> = listOf(
        noBasePayComponents(),
        singleFullyOverlappingBasePay(),
        singlePartiallyOverlappingBasePay(),
        multipleOverlappingBasePays(),
        basePayStartsAfterCompensationItem(),
        basePayEndsBeforeCompensationItem(),
        basePayWithDifferentRatesForDifferentPeriods(),
    )

    private fun noBasePayComponents() = Arguments.of(
        emptyList<Compensation>(),
        0.0,
        "No base pay components should result in 0.0",
    )

    private fun singleFullyOverlappingBasePay() = Arguments.of(
        listOf(
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2024, 1, 1))
                .withEndDate(LocalDate.of(2024, 1, 31))
                .withBillingRate(5000.0)
                .build(),
        ),
        1000.0,
        "Single fully overlapping base pay",
    )

    private fun singlePartiallyOverlappingBasePay() = Arguments.of(
        listOf(
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2024, 1, 10))
                .withEndDate(LocalDate.of(2024, 1, 20))
                .withBillingRate(5000.0)
                .build(),
        ),
        687.5,
        "Single partially overlapping base pay",
    )

    private fun multipleOverlappingBasePays() = Arguments.of(
        listOf(
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2024, 1, 1))
                .withEndDate(LocalDate.of(2024, 1, 31))
                .withBillingRate(5000.0)
                .build(),
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2024, 1, 15))
                .withEndDate(LocalDate.of(2024, 2, 15))
                .withBillingRate(6000.0)
                .build(),
        ),
        1825.0,
        "Multiple overlapping base pay components",
    )

    private fun basePayStartsAfterCompensationItem() = Arguments.of(
        listOf(
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2024, 2, 1))
                .withEndDate(LocalDate.of(2024, 2, 28))
                .withBillingRate(5000.0)
                .build(),
        ),
        0.0,
        "Base pay starts after compensation item → No overlap",
    )

    private fun basePayEndsBeforeCompensationItem() = Arguments.of(
        listOf(
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2023, 12, 1))
                .withEndDate(LocalDate.of(2023, 12, 31))
                .withBillingRate(5000.0)
                .build(),
        ),
        0.0,
        "Base pay ends before compensation item → No overlap",
    )

    private fun basePayWithDifferentRatesForDifferentPeriods() = Arguments.of(
        listOf(
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2024, 1, 1))
                .withEndDate(LocalDate.of(2024, 1, 10))
                .withBillingRate(4000.0)
                .build(),
            TestCompensationBuilder()
                .withStartDate(LocalDate.of(2024, 1, 11))
                .withEndDate(LocalDate.of(2024, 1, 31))
                .withBillingRate(6000.0)
                .build(),
        ),
        1175.0,
        "Base pay with different rates for different periods",
    )
}
