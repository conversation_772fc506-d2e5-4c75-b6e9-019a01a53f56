package com.multiplier.compensation.grpc.server.mapper.bulkupload

import com.multiplier.compensation.service.compensation.extrapolation.DERIVED_INPUT_ROW_ID_PREFIX

/**
 * Filters out extrapolated inputs from a data map.
 * Extrapolated inputs have IDs in the format "derived_${sourceItemId}_${index}".
 *
 * @param dataMap Map where keys are input IDs and values are the data
 * @return Filtered map containing only original (non-extrapolated) inputs
 */
fun <T> filterValidationErrorsFromExtrapolatedInputs(dataMap: Map<String, T>): Map<String, T> =
    dataMap.filterKeys { inputId ->
        !isExtrapolatedInput(inputId)
    }

/**
 * Filters out extrapolated inputs from a list of items that have an inputId property.
 *
 * @param items List of items with inputId property
 * @param inputIdExtractor Function to extract inputId from each item
 * @return Filtered list containing only original (non-extrapolated) inputs
 */
fun <T> filterExtrapolatedInputs(
    items: List<T>,
    inputIdExtractor: (T) -> String,
): List<T> = items.filter { item ->
    !isExtrapolatedInput(inputIdExtractor(item))
}

/**
 * Checks if an input ID represents an extrapolated/derived input.
 * Extrapolated inputs have IDs in the format "derived_${sourceItemId}_${index}".
 *
 * @param inputId The input ID to check
 * @return true if the input is extrapolated, false otherwise
 */
private fun isExtrapolatedInput(inputId: String): Boolean = inputId.startsWith(DERIVED_INPUT_ROW_ID_PREFIX)
