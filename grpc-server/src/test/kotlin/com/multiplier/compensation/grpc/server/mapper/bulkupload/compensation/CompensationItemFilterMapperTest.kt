package com.multiplier.compensation.grpc.server.mapper.bulkupload.compensation

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.grpc.schema.CompensationItemsFilter
import com.multiplier.compensation.grpc.server.mapper.compensation.toDomain
import com.multiplier.compensation.grpc.server.mapper.compensation.toDto
import com.multiplier.grpc.common.toDateTime
import com.multiplier.grpc.common.v1.toGrpc
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus as domainItemStatus
import com.multiplier.compensation.grpc.schema.CompensationItemStatus as gRPCItemStatus

class CompensationItemFilterMapperTest {
    @Test
    fun `toDto converts all fields correctly`() {
        // Given
        val uuid1 = UUID.randomUUID()
        val uuid2 = UUID.randomUUID()

        val grpcFilter = CompensationItemsFilter.newBuilder()
            .addAllIds(listOf(uuid1.toGrpc(), uuid2.toGrpc()))
            .addAllContractIds(listOf(123L, 456L))
            .addAllStatuses(listOf(gRPCItemStatus.NEW, gRPCItemStatus.PAID))
            .addAllIncludedCategories(listOf("cat1", "cat2"))
            .addAllExcludedCategories(listOf("cat3"))
            .setUpdatedFromExclusive(LocalDate.of(2025, 1, 1).atStartOfDay().toDateTime())
            .setUpdatedToInclusive(LocalDate.of(2025, 12, 31).atStartOfDay().toDateTime())
            .build()

        // When
        val result = grpcFilter.toDto()

        // Then
        assertTrue(result.ids.containsAll(listOf(uuid1, uuid2)))
        assertTrue(result.contractIds.containsAll(listOf(123L, 456L)))
        assertTrue(result.statuses.containsAll(listOf(domainItemStatus.NEW, domainItemStatus.PAID)))
        assertTrue(result.includedCategories.containsAll(listOf("cat1", "cat2")))
        assertTrue(result.excludedCategories.containsAll(listOf("cat3")))
        assertEquals(result.updatedFromExclusive, LocalDateTime.of(2025, 1, 1, 0, 0))
        assertEquals(result.updatedToInclusive, LocalDateTime.of(2025, 12, 31, 0, 0))
    }

    @Test
    fun `toDto filters out UNDEFINED statuses`() {
        // Given
        val grpcFilter = CompensationItemsFilter.newBuilder()
            .addAllStatuses(listOf(gRPCItemStatus.NEW, gRPCItemStatus.UNDEFINED, gRPCItemStatus.PAID))
            .build()

        // When
        val result = grpcFilter.toDto()

        // Then
        assertTrue(result.statuses.containsAll(listOf(domainItemStatus.NEW, domainItemStatus.PAID)))
    }

    @Test
    fun `toDto handles empty fields`() {
        // Given
        val grpcFilter = CompensationItemsFilter.getDefaultInstance()

        // When
        val result = grpcFilter.toDto()

        // Then
        assertTrue(result.ids.isEmpty())
        assertTrue(result.contractIds.isEmpty())
        assertTrue(result.statuses.isEmpty())
        assertTrue(result.includedCategories.isEmpty())
        assertTrue(result.excludedCategories.isEmpty())
        assertEquals(result.updatedFromExclusive, null)
        assertEquals(result.updatedToInclusive, null)
    }

    @ParameterizedTest
    @EnumSource(
        value = gRPCItemStatus::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["UNDEFINED", "UNRECOGNIZED"],
    )
    fun `toDomain converts valid statuses correctly`(status: gRPCItemStatus) {
        // When
        val result = status.toDomain()

        // Then
        val expectedDomainStatus = when (status) {
            gRPCItemStatus.NEW -> domainItemStatus.NEW
            gRPCItemStatus.INPUT_LOCKED -> domainItemStatus.INPUT_LOCKED
            gRPCItemStatus.PROCESSING -> domainItemStatus.PROCESSING
            gRPCItemStatus.PAID -> domainItemStatus.PAID
            gRPCItemStatus.COMPLETED -> domainItemStatus.COMPLETED
            gRPCItemStatus.ABORTED -> domainItemStatus.ABORTED
            gRPCItemStatus.DRAFT -> domainItemStatus.DRAFT
            gRPCItemStatus.REVOKED -> domainItemStatus.REVOKED
            else -> fail("Unexpected status: $status")
        }

        assertEquals(expectedDomainStatus, result, "Conversion mismatch for gRPCItemStatus: $status")
    }

    @Test
    fun `toDomain throws for UNDEFINED status`() {
        // When & Then
        val exception = assertThrows<InvalidArgumentException> {
            gRPCItemStatus.UNDEFINED.toDomain()
        }

        assertEquals(ValidationErrorCode.InvalidCompensationItemStatus, exception.errorCode)
        assertTrue(exception.message.contains("UNDEFINED"), "Exception message should contain 'UNDEFINED'")
    }
}
