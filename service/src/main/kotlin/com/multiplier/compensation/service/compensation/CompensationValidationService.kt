package com.multiplier.compensation.service.compensation

import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.compensation.dto.CompensationBulkValidationRequest
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.extrapolation.CompensationExtrapolationManager
import com.multiplier.compensation.service.compensation.validation.CompensationBackFillValidationPipeline
import com.multiplier.compensation.service.compensation.validation.CompensationContextProvider
import com.multiplier.compensation.service.compensation.validation.CompensationRevisionBackFillValidationPipeline
import com.multiplier.compensation.service.compensation.validation.CompensationRevisionValidationPipeline
import com.multiplier.compensation.service.compensation.validation.CompensationValidationPipeline
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.DeductionValidationPipeline
import com.multiplier.compensation.service.compensation.validation.PaySupplementValidationPipeline
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val logger = KotlinLogging.logger {}

// TODO: Inject pipelines of base type using qualifiers
@Service
class CompensationValidationService(
    private val contextProvider: CompensationContextProvider,
    private val compensationValidationPipeline: CompensationValidationPipeline,
    private val compensationRevisionValidationPipeline: CompensationRevisionValidationPipeline,
    private val paySupplementValidationPipeline: PaySupplementValidationPipeline,
    private val deductionValidationPipeline: DeductionValidationPipeline,
    private val compensationBackFillValidationPipeline: CompensationBackFillValidationPipeline,
    private val compensationRevisionBackFillValidationPipeline: CompensationRevisionBackFillValidationPipeline,
    private val compensationExtrapolationManager: CompensationExtrapolationManager,
) {
    fun validate(
        request: CompensationBulkValidationRequest,
        context: CompensationValidatorContext = fetchValidatorContext(request),
    ): ValidationDataCollector<CompensationDraft> {
        logger.info {
            "Validating request for entity [${request.entityId}] for [${request.items.count()}] input items."
        }

        // Apply extrapolation and filtering for use cases that need derived items
        val itemsToValidate = compensationExtrapolationManager.extrapolateAndFilterDuplicateInputs(
            request.items,
            context,
            request.source,
        )

        val updatedRequest = request.copy(items = itemsToValidate)

        return executeValidation(
            input = updatedRequest,
            context = context,
        )
    }

    private fun executeValidation(
        input: CompensationBulkValidationRequest,
        context: CompensationValidatorContext,
    ): ValidationDataCollector<CompensationDraft> {
        val collector = when (input.source) {
            RequestType.COMPENSATION_SETUP -> compensationValidationPipeline.execute(context, input.items)
            RequestType.PAY_SUPPLEMENT -> paySupplementValidationPipeline.execute(context, input.items)
            RequestType.COMPENSATION_REVISION -> compensationRevisionValidationPipeline.execute(context, input.items)
            RequestType.DEDUCTION -> deductionValidationPipeline.execute(context, input.items)
            RequestType.COMPENSATION_BACKFILL -> compensationBackFillValidationPipeline.execute(context, input.items)
            RequestType.COMPENSATION_REVISION_BACKFILL ->
                compensationRevisionBackFillValidationPipeline.execute(context, input.items)
        }

        logger.info {
            "Validation collector has ${collector.rowValidationResult} validation results and " +
                "${collector.drafts} drafts"
        }

        return collector
    }

    private fun fetchValidatorContext(request: CompensationBulkValidationRequest): CompensationValidatorContext =
        contextProvider.initializeValidationContext(
            entityId = request.entityId,
            source = request.source,
            validationInputItems = request.items,
            contextUseCase = ContextUseCase.VALIDATE,
            customParams = request.customParams,
        )
}
