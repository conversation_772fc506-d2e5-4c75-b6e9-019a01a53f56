package com.multiplier.compensation.domain.compensationitem

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.util.UUID

class GenerateCompensationItemsForCompensationsRequestTest {
    @Test
    fun `should create valid GenerateCompensationItemsForCompensationsRequest with all fields set`() {
        // Given
        val compensationIds = listOf(UUID.randomUUID(), UUID.randomUUID())
        val shouldPersist = true

        // When
        val request = GenerateCompensationItemsForCompensationsRequest(
            compensationIds = compensationIds,
            shouldPersist = shouldPersist,
        )

        // Then
        assertEquals(compensationIds, request.compensationIds)
        assertEquals(shouldPersist, request.shouldPersist)
    }

    @Test
    fun `should use default value for shouldPersist when not provided`() {
        // Given
        val compensationIds = listOf(UUID.randomUUID(), UUID.randomUUID())

        // When
        val request = GenerateCompensationItemsForCompensationsRequest(compensationIds = compensationIds)

        // Then
        assertEquals(compensationIds, request.compensationIds)
        assertFalse(request.shouldPersist)
    }

    @Test
    fun `should allow empty compensationIds list`() {
        // Given
        val compensationIds = emptyList<UUID>()

        // When
        val request = GenerateCompensationItemsForCompensationsRequest(compensationIds = compensationIds)

        // Then
        assertTrue(request.compensationIds.isEmpty())
        assertFalse(request.shouldPersist)
    }

    @Test
    fun `should handle empty list and false shouldPersist as default`() {
        // When
        val request = GenerateCompensationItemsForCompensationsRequest(emptyList())

        // Then
        assertTrue(request.compensationIds.isEmpty())
        assertFalse(request.shouldPersist)
    }
}
