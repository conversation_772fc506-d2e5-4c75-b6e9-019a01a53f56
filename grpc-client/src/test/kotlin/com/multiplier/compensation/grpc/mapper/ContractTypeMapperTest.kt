package com.multiplier.compensation.grpc.mapper

import com.multiplier.compensation.grpc.client.mapper.toDomain
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import com.multiplier.compensation.domain.common.contract.ContractType as DomainContractType

class ContractTypeMapperTest {
    @Test
    fun `should map CONTRACTOR to domain CONTRACTOR`() {
        // Given
        val grpcContractType = ContractType.CONTRACTOR

        // When
        val result = grpcContractType.toDomain()

        // Then
        assertEquals(DomainContractType.CONTRACTOR, result)
    }

    @Test
    fun `should map FREELANCER to domain FREELANCER`() {
        // Given
        val grpcContractType = ContractType.FREELANCER

        // When
        val result = grpcContractType.toDomain()

        // Then
        assertEquals(DomainContractType.FREELANCER, result)
    }

    @Test
    fun `should map HR_MEMBER to domain HR_MEMBER`() {
        // Given
        val grpcContractType = ContractType.HR_MEMBER

        // When
        val result = grpcContractType.toDomain()

        // Then
        assertEquals(DomainContractType.HR_MEMBER, result)
    }

    @Test
    fun `should map EMPLOYEE to domain EMPLOYEE`() {
        // Given
        val grpcContractType = ContractType.EMPLOYEE

        // When
        val result = grpcContractType.toDomain()

        // Then
        assertEquals(DomainContractType.EMPLOYEE, result)
    }

    @Test
    fun `should throw IllegalArgumentException for UNRECOGNIZED`() {
        // Given
        val grpcContractType = ContractType.UNRECOGNIZED

        // When/Then
        val exception = assertThrows<IllegalArgumentException> {
            grpcContractType.toDomain()
        }

        // Then
        assertEquals("Unknown contract type: $grpcContractType", exception.message)
    }

    @Test
    fun `should throw IllegalArgumentException for NULL_CONTRACT_TYPE`() {
        // Given
        val grpcContractType = ContractType.NULL_CONTRACT_TYPE

        // When/Then
        val exception = assertThrows<IllegalArgumentException> {
            grpcContractType.toDomain()
        }

        // Then
        assertEquals("NULL_CONTRACT_TYPE is not supported", exception.message)
    }
}
