package com.multiplier.compensation.database.util

import com.multiplier.compensation.database.enums.CompensationStatus
import com.multiplier.compensation.database.tables.references.COMPENSATION_
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.CompensationFilter
import com.multiplier.compensation.domain.compensation.CompensationRecordsFilter
import com.multiplier.compensation.domain.compensation.CompensationRecordsGrpcFilter
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import org.jooq.Condition
import org.jooq.impl.DSL
import java.time.LocalDate
import java.util.UUID

/**
 * Builds conditions for filtering compensation records based on various criteria.
 */
object CompensationRecordConditionBuilder {
    /**
     * Builds filter conditions for CompensationRecordsFilter.
     *
     * Generated SQL:
     * ```sql
     * WHERE entity_id = :entityId
     * AND company_id = :companyId
     * AND contract_id IN (:contractIds)
     * AND category IN (:categories)
     * AND start_date >= :startDate
     * AND end_date <= :endDate
     * ```
     */
    fun buildCompensationRecordFilterCondition(filter: CompensationRecordsFilter): List<Condition> {
        val conditions = mutableListOf<Condition>()
        filter.entityId?.let {
            conditions.add(COMPENSATION_.ENTITY_ID.eq(it))
        }
        filter.companyId?.let {
            conditions.add(COMPENSATION_.COMPANY_ID.eq(it))
        }
        filter.contractIds?.let {
            if (it.isNotEmpty()) {
                conditions.add(COMPENSATION_.CONTRACT_ID.`in`(it))
            }
        }
        filter.categories?.let {
            if (it.isNotEmpty()) {
                conditions.add(COMPENSATION_.CATEGORY.`in`(it))
            }
        }
        filter.state?.let {
            applyCompensationStateFilter(conditions, filter)
        }
        filter.startDate?.let {
            conditions.add(COMPENSATION_.START_DATE.ge(it))
        }
        filter.endDate?.let {
            conditions.add(COMPENSATION_.END_DATE.le(it))
        }
        return conditions
    }

    /**
     * Builds filter conditions for CompensationRecordsGrpcFilter.
     *
     * Generated SQL:
     * ```sql
     * WHERE id IN (:ids)
     * AND entity_id = :entityId
     * AND contract_id IN (:contractIds)
     * AND category IN (:categories)
     * AND start_date <= :activeTo
     * AND (end_date IS NULL OR end_date >= :activeFrom)
     * ```
     */
    fun buildCompensationRecordFilterCondition(filter: CompensationRecordsGrpcFilter): List<Condition> {
        val conditions = mutableListOf<Condition>()
        filter.ids.let {
            if (it.isNotEmpty()) {
                conditions.add(COMPENSATION_.ID.`in`(it))
            }
        }
        filter.entityId?.takeIf { it != 0L }?.let {
            conditions.add(COMPENSATION_.ENTITY_ID.eq(it))
        }
        filter.contractIds.let {
            if (it.isNotEmpty()) {
                conditions.add(COMPENSATION_.CONTRACT_ID.`in`(it))
            }
        }
        filter.categories.let {
            if (it.isNotEmpty()) {
                conditions.add(COMPENSATION_.CATEGORY.`in`(it))
            }
        }
        /**
         * activeFrom and activeTo are the current date to fetch the compensations which are active as of now
         */
        filter.activeTo?.let {
            conditions.add(COMPENSATION_.START_DATE.lessOrEqual(it))
        }
        filter.activeFrom?.let {
            conditions.add((COMPENSATION_.END_DATE.isNull).or(COMPENSATION_.END_DATE.greaterOrEqual(it)))
        }
        filter.state?.let {
            applyCompensationStateFilter(
                conditions,
                filter,
            )
        }
        return conditions
    }

    /**
     * Applies state-specific filters to compensation records.
     * Handles COMPLETED, UPCOMING, PROCESSING and ACTIVE states.
     */
    private fun applyCompensationStateFilter(
        conditions: MutableList<Condition>,
        filter: CompensationFilter,
    ): List<Condition> = conditions.apply {
        when (filter.state) {
            CompensationState.COMPLETED -> {
                buildCompletedCompensationCondition()
            }

            CompensationState.UPCOMING -> {
                buildUpcomingCompensationCondition()
            }

            CompensationState.PROCESSING -> {
                buildProcessingCompensationCondition()
            }

            CompensationState.ACTIVE -> {
                buildActiveCompensationsCondition(filter)
            }

            else -> {
                throw InvalidArgumentException(
                    errorCode = ValidationErrorCode.InvalidCompensationState,
                    message = "Invalid CompensationState [${filter.state}]",
                    context = mapOf("state" to filter.state),
                )
            }
        }
    }

    /**
     * Builds condition for completed compensations.
     *
     * Generated SQL:
     * ```sql
     * WHERE status NOT IN ('DELETED', 'ABORTED')
     * AND processing_to IS NOT NULL
     * AND processing_to < CURRENT_TIME
     * ```
     */
    private fun MutableList<Condition>.buildCompletedCompensationCondition() {
        add(
            COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
                COMPENSATION_.PROCESSING_TO.isNotNull.and(
                    COMPENSATION_.PROCESSING_TO.lessThan(LocalDate.now().atStartOfDay()),
                ),
            ),
        )
    }

    /**
     * Builds condition for upcoming compensations.
     *
     * Generated SQL:
     * ```sql
     * WHERE status NOT IN ('DELETED', 'ABORTED')
     * AND (processing_from IS NULL OR processing_from >= CURRENT_TIME)
     * ```
     */
    private fun MutableList<Condition>.buildUpcomingCompensationCondition() {
        add(
            COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
                COMPENSATION_.PROCESSING_FROM.isNull.or(
                    COMPENSATION_.PROCESSING_FROM.greaterOrEqual(LocalDate.now().atStartOfDay()),
                ),
            ),
        )
    }

    /**
     * Builds condition for processing compensations.
     *
     * Generated SQL:
     * ```sql
     * WHERE status NOT IN ('DELETED', 'ABORTED')
     * AND processing_from IS NOT NULL
     * AND processing_from < CURRENT_TIME
     * AND (processing_to IS NULL OR processing_to >= CURRENT_TIME)
     * ```
     */
    private fun MutableList<Condition>.buildProcessingCompensationCondition() {
        add(
            COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED).and(
                COMPENSATION_.PROCESSING_FROM.isNotNull.and(
                    COMPENSATION_.PROCESSING_FROM.lessThan(LocalDate.now().atStartOfDay()),
                )
                    .and(
                        COMPENSATION_.PROCESSING_TO.isNull.or(
                            COMPENSATION_.PROCESSING_TO.greaterOrEqual(LocalDate.now().atStartOfDay()),
                        ),
                    ),
            ),
        )
    }

    /**
     * Builds conditions for active compensations.
     *
     * Generated SQL:
     * ```sql
     * WHERE status NOT IN ('DELETED', 'ABORTED')
     * AND (processing_to IS NULL OR processing_to >= :activeAsOn)
     * AND buildUniqueSchemaItemsCondition(filter)
     * ```
     */
    private fun MutableList<Condition>.buildActiveCompensationsCondition(filter: CompensationFilter) {
        val activeAsOn = filter.activeAsOn ?: LocalDate.now()
        add(
            COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED)
                .and(
                    COMPENSATION_.PROCESSING_TO.isNull.or(
                        COMPENSATION_.PROCESSING_TO.greaterOrEqual(activeAsOn.atStartOfDay()),
                    ),
                ),
        )
        add(buildUniqueSchemaItemsCondition(filter))
    }

    /**
     * Builds sub query to find unique schema items with the earliest start date
     * (Amongst all the compensations for the same schema item id which are either UPCOMING/PROCESSING).
     * To filter out duplicate compensations whose schema item id is already included in above conditions. Example:
     *      * For contract C, If we have Base Pay 1000.0 $ from 2024-11-01 to 2024-12-31
     *          and Base Pay 2000.0 $ from 2025-01-01 to 2025-03-31
     *      * And if the activeAsOn is 2024-10-01
     *      * Then we should only return Base Pay 1000.0 and exclude Base Pay 2000.0
     *
     * Generated SQL:
     * ```sql
     * id IN (
     *   SELECT non_duplicate_compensation_query.id
     *   FROM compensation non_duplicate_compensation_query
     *   JOIN (
     *     SELECT schema_item_id as schema_item,
     *            contract_id,
     *            MIN(start_date) as earliest_start_date
     *     FROM compensation
     *     WHERE status NOT IN ('DELETED', 'ABORTED')
     *     AND (PROCESSING_TO IS NULL OR PROCESSING_TO >= :activeAsOn)
     *     GROUP BY schema_item_id, contract_id
     *   ) earliest_compensation_query
     *   ON non_duplicate_compensation_query.schema_item_id = earliest_compensation_query.schema_item
     *   AND non_duplicate_compensation_query.start_date = earliest_compensation_query.earliest_start_date
     *   AND non_duplicate_compensation_query.contract_id = earliest_compensation_query.contract_id
     * )
     * ```
     */
    private fun buildUniqueSchemaItemsCondition(filter: CompensationFilter): Condition = COMPENSATION_.ID.`in`(
        DSL.select(DSL.field("non_duplicate_compensation_query.id", UUID::class.java))
            .from(COMPENSATION_.`as`("non_duplicate_compensation_query"))
            .join(
                buildEarliestCompensationsQuery(filter),
            )
            .on(
                buildEarliestCompensationsJoinConditions(),
            ),
    )

    private fun buildEarliestCompensationsQuery(filter: CompensationFilter) = DSL.select(
        COMPENSATION_.SCHEMA_ITEM_ID.`as`("schema_item"),
        COMPENSATION_.CONTRACT_ID.`as`("contract_id"),
        DSL.min(COMPENSATION_.START_DATE).`as`("earliest_start_date"),
    )
        .from(COMPENSATION_)
        .where(
            COMPENSATION_.STATUS.notIn(CompensationStatus.DELETED, CompensationStatus.ABORTED)
                .and(
                    COMPENSATION_.PROCESSING_TO.isNull.or(
                        COMPENSATION_.PROCESSING_TO.greaterOrEqual(
                            filter.activeAsOn?.atStartOfDay() ?: LocalDate.now().atStartOfDay(),
                        ),
                    ),
                ),
        )
        .groupBy(COMPENSATION_.SCHEMA_ITEM_ID, COMPENSATION_.CONTRACT_ID)
        .asTable("earliest_compensation_query")

    private fun buildEarliestCompensationsJoinConditions() = DSL.field(
        "non_duplicate_compensation_query.schema_item_id",
    ).eq(DSL.field("earliest_compensation_query.schema_item"))
        .and(
            DSL.field(
                "non_duplicate_compensation_query.start_date",
            ).eq(DSL.field("earliest_compensation_query.earliest_start_date")),
        )
        .and(
            DSL.field(
                "non_duplicate_compensation_query.contract_id",
            ).eq(DSL.field("earliest_compensation_query.contract_id")),
        )
}
