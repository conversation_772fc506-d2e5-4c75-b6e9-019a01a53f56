spring:
  datasource:
    url: jdbc:h2:mem:payroll_db
    username: sa
    password: password

  liquibase:
    changeLog: classpath:com/multiplier/compensation/liquibase/integration-test.xml
    liquibase-schema:

grpc:
  server:
    port: -1
    inProcessName: test
    security:
      certificate-chain: classpath:certificates/server.local.crt
      private-key: classpath:certificates/server.local.key

  client:
    GLOBAL:
      security:
        trust-cert-collection: classpath:certificates/server.local.crt
    company-service:
      address: dns:///localhost:9090
    test:
      address: in-process:test
    authority-service:
      address: in-process:test
    country-service:
      address: in-process:test
    payroll-schema-service:
      address: dns:///localhost:9090
    contract-service:
      address: dns:///localhost:9091
    contract-off-boarding-service:
      address: dns:///localhost:9093
    org-management-service:
      address: dns:///localhost:9092
    pigeon-service:
      address: dns:///localhost:9094

skeleton:
  structure-files-url: classpath*:skeleton_test/*.json

core:
  scheduler:
    default-lock-atleast-for: "PT2M"
    default-lock-atmost-for: "PT3M"

    compensation-item-generation:
      job-name: "CompensationItemGeneration"
      enabled: false
      lock-atleast-for: "PT2M"
      lock-atmost-for: "PT3M"
      frequency: "0 */5 * * * *"
      generation-period-in-days: 60

    variable-pay-notification:
      job-name: "VariablePayNotification"
      enabled: false
      lock-atleast-for: "PT2M"
      lock-atmost-for: "PT3M"
      frequency: "0 0 1 * * ?"

growthbook:
  base-url: https://api-gateway.api.acc.staging.usemultiplier.com
  refresh-frequency-ms: 15000
  env-key: sdk-baFSLlWpxEWQ6bSZ # Staging

file:
  paySchedulePath: classpath:bulk_upload_input_output/paySchedule/
  compensationSchemaPath: classpath:bulk_upload_input_output/compensationSchema/
  compensationPath: classpath:bulk_upload_input_output/compensation/
  compensationRevisionInputPath: classpath:bulk_upload_input_output/compensation_revision_input.csv
  compensationRevisionOutputPath: classpath:bulk_upload_input_output/compensation_revision_output.csv
  compensationPaySupplementInputPath: classpath:bulk_upload_input_output/compensation_paySupplement_input.csv
  compensationPaySupplementOutputPath: classpath:bulk_upload_input_output/compensation_paySupplement_output.csv
  compensationDeductionInputPath: classpath:bulk_upload_input_output/compensation_deduction_input.csv
  compensationDeductionOutputPath: classpath:bulk_upload_input_output/compensation_deduction_output.csv
  staticVariablePath: classpath:static_variables.csv

kafka:
  bootstrap-servers:
  consumer:
    auto-startup: false
    enabled: false

mpl:
  jobs:
    enabled: false
    scheduler:
      enabled: false

pigeon:
  client:
    kafka:
      bootstrap-servers:

compensation:
  notification:
    adminEmails: "<EMAIL>"
    fromEmail: "<EMAIL>"
    variablePaySubjectPrefix: "Variable Pay Override Notification"
