package com.multiplier.compensation.service.payschedule.strategy

import com.multiplier.compensation.database.repository.payschedule.PayScheduleRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.CategoryConstants.isPrimarySalaryOrDirectDependentComponent
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import org.springframework.stereotype.Component

/**
 * Strategy implementation for Global Payroll offering type.
 * Handles pay schedule and billing frequency logic specific to Global Payroll.
 */
@Component
class GlobalPayrollCompensationPayScheduleSelectionStrategy(
    private val payScheduleRepository: PayScheduleRepository,
) : CompensationPayScheduleSelectionStrategy {
    /**
     * Billing frequencies eligible for Global Payroll primary salary or direct dependent components.
     * Restricted to: MONTHLY, ANNUALLY, HOURLY, DAILY
     */
    private val globalPayrollPrimarySalaryEligibleFrequencies = listOf(
        BillingFrequency.MONTHLY,
        BillingFrequency.ANNUALLY,
        BillingFrequency.HOURLY,
        BillingFrequency.DAILY,
    )

    /**
     * Retrieves eligible pay schedules for Global Payroll based on the request parameters.
     */
    override fun getEligiblePaySchedules(request: GetEligiblePayScheduleRequest): List<PaySchedule> {
        validateRequest(request)

        val isPrimaryComponent = isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)

        val payScheduleFrequencies = if (isPrimaryComponent) {
            getPrimaryComponentPayScheduleFrequencies()
        } else {
            getAdditionalComponentPayScheduleFrequencies(request)
        }

        requireCondition(
            condition = payScheduleFrequencies.isNotEmpty(),
            errorCode = ValidationErrorCode.PayScheduleNotFound,
            message = "No eligible pay schedule frequencies found for the given criteria",
            context = mapOf(
                "billingFrequency" to request.billingFrequency,
                "offeringCode" to request.offeringCode,
                "countryCode" to request.countryCode,
                "compensationCategory" to request.compensationCategory,
                "grossSalaryPayScheduleFrequency" to request.grossSalaryPayScheduleFrequency,
                "isPrimaryComponent" to isPrimaryComponent,
            ),
        )

        val isInstallment: Boolean? = false.takeIf { isPrimaryComponent }

        return payScheduleRepository.findAllByEntityAndScopeAndFrequencies(
            entityId = request.entityId,
            scope = ConfigurationScope.COMPANY,
            frequencies = payScheduleFrequencies,
            isInstallment = isInstallment,
        ).toMutableList()
    }

    override fun getEligibleBillingFrequencies(request: GetEligibleBillingFrequenciesRequest): List<BillingFrequency> {
        validateBillingFrequenciesRequest(request)

        val isPrimaryComponent = isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)

        return if (isPrimaryComponent) {
            globalPayrollPrimarySalaryEligibleFrequencies
        } else {
            grossPayScheduleToAdditionalPaysFrequenciesForGP[request.grossSalaryPayScheduleFrequency]
                ?.keys?.toList().orEmpty()
        }
    }

    override fun validateRequest(request: GetEligiblePayScheduleRequest) {
        requireCondition(
            condition = request.billingFrequency != null,
            errorCode = ValidationErrorCode.NonNullableFieldViolation,
            message = "BillingFrequency is required to get the pay schedules for Global Payroll",
            context = mapOf(
                "billingFrequency" to request.billingFrequency,
                "offeringCode" to request.offeringCode,
                "entityId" to request.entityId,
                "compensationCategory" to request.compensationCategory,
            ),
        )
    }

    override fun validateBillingFrequenciesRequest(request: GetEligibleBillingFrequenciesRequest) {
        val isPrimaryComponent = isPrimarySalaryOrDirectDependentComponent(request.compensationCategory)

        if (!isPrimaryComponent) {
            requireCondition(
                condition = request.grossSalaryPayScheduleFrequency != null &&
                    request.grossSalaryPayScheduleFrequency in
                    grossPayScheduleToAdditionalPaysFrequenciesForGP.keys,
                errorCode = ValidationErrorCode.InvalidPayScheduleFrequency,
                message = "Pay schedule frequency [${request.grossSalaryPayScheduleFrequency}] is invalid for" +
                    " Global Payroll additional components. Valid frequencies: ${grossPayScheduleToAdditionalPaysFrequenciesForGP.keys}",
                context = mapOf(
                    "grossSalaryPayScheduleFrequency" to request.grossSalaryPayScheduleFrequency,
                    "validFrequencies" to grossPayScheduleToAdditionalPaysFrequenciesForGP.keys,
                    "offeringCode" to request.offeringCode,
                    "compensationCategory" to request.compensationCategory,
                    "entityId" to request.entityId,
                ),
            )
        }
    }

    private fun getPrimaryComponentPayScheduleFrequencies(): List<PayScheduleFrequency> =
        payScheduleFrequenciesForGlobalPayrollPrimarySalary

    private fun getAdditionalComponentPayScheduleFrequencies(request: GetEligiblePayScheduleRequest) =
        request.billingFrequency?.let { billingFreq ->
            grossPayScheduleToAdditionalPaysFrequenciesForGP[request.grossSalaryPayScheduleFrequency]?.get(
                billingFreq,
            )
        }?.toList().orEmpty()

    val payScheduleFrequenciesForGlobalPayrollPrimarySalary = listOf(
        PayScheduleFrequency.MONTHLY,
        PayScheduleFrequency.SEMI_MONTHLY,
        PayScheduleFrequency.BI_WEEKLY,
        PayScheduleFrequency.WEEKLY,
    )

    val grossPayScheduleToAdditionalPaysFrequenciesForGP:
        Map<PayScheduleFrequency, Map<BillingFrequency, List<PayScheduleFrequency>>> =
        mapOf(
            PayScheduleFrequency.MONTHLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
                BillingFrequency.DAILY to listOf(PayScheduleFrequency.MONTHLY),
            ),
            PayScheduleFrequency.SEMI_MONTHLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.SEMIMONTHLY to listOf(PayScheduleFrequency.SEMI_MONTHLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
                BillingFrequency.DAILY to listOf(PayScheduleFrequency.SEMI_MONTHLY),
            ),
            PayScheduleFrequency.BI_WEEKLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.BIWEEKLY to listOf(PayScheduleFrequency.BI_WEEKLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
                BillingFrequency.DAILY to listOf(PayScheduleFrequency.BI_WEEKLY),
            ),
            PayScheduleFrequency.WEEKLY to mapOf(
                BillingFrequency.ANNUALLY to listOf(PayScheduleFrequency.ANNUALLY),
                BillingFrequency.SEMIANNUALLY to listOf(PayScheduleFrequency.SEMI_ANNUALLY),
                BillingFrequency.QUARTERLY to listOf(PayScheduleFrequency.QUARTERLY),
                BillingFrequency.MONTHLY to listOf(PayScheduleFrequency.MONTHLY),
                BillingFrequency.WEEKLY to listOf(PayScheduleFrequency.WEEKLY),
                BillingFrequency.ONETIME to listOf(PayScheduleFrequency.ONE_TIME),
                BillingFrequency.DAILY to listOf(PayScheduleFrequency.WEEKLY),
            ),
        )
}
