package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.common.ValidatorUtil.getOriginalInputByKey
import com.multiplier.compensation.service.common.validationpipeline.common.ValidatorUtil.getSkeletonFieldForIdentification
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.dto.isBackFillContext
import com.multiplier.compensation.service.compensation.dto.paySchedulesById
import org.springframework.stereotype.Component

@Component
class DuplicateInputValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val id = draft.employeeId ?: draft.contractId ?: return false

        val duplicateInputIds = collector.drafts.filterValues { it == draft }.keys.toList()

        if (duplicateInputIds.size > 1) {
            addValidationResult(
                input = input,
                field = getSkeletonFieldForIdentification(context),
                resultType = ValidationResultType.ERROR,
                message = "Duplicate inputs found for employee $id",
                collector = collector,
            )
            return false
        }

        // Check for overlapping dates with other non-duplicate drafts
        val nonDuplicateDrafts = collector.drafts.filterValues { it != draft }
        val paySchedulesById = context.paySchedules.paySchedulesById()
        val overlappingInputIds = nonDuplicateDrafts.filterValues { otherDraft ->
            draft.companyId == otherDraft.companyId &&
                isSameEmployee(draft, otherDraft, context) &&
                draft.schemaItemId == otherDraft.schemaItemId &&
                isDateOverlap(draft, otherDraft, paySchedulesById)
        }.keys.toList()

        if (overlappingInputIds.isNotEmpty()) {
            val componentName = getOriginalInputByKey(CompensationSkeletonField.COMPONENT_NAME, input).value
            addValidationResult(
                input = input,
                field = CompensationSkeletonField.COMPONENT_NAME,
                resultType = ValidationResultType.ERROR,
                message = "Overlapping dates found for component $componentName in rows $overlappingInputIds",
                collector = collector,
            )
            return false
        }

        return true
    }

    private fun isDateOverlap(
        currentDraft: CompensationDraft,
        otherDraft: CompensationDraft,
        paySchedulesById: Map<java.util.UUID, PaySchedule>,
    ): Boolean {
        // Modify drafts to consider installment type's end date
        val modifiedCurrentDraft = modifyDraftEndDateIfInstallment(currentDraft, paySchedulesById)
        val modifiedOtherDraft = modifyDraftEndDateIfInstallment(otherDraft, paySchedulesById)

        val currentDraftStart = modifiedCurrentDraft.startDate
        val currentDraftEnd = modifiedCurrentDraft.endDate
        val otherDraftStart = modifiedOtherDraft.startDate
        val otherDraftEnd = modifiedOtherDraft.endDate

        // Validate that start is before end for both ranges where end is not null
        if ((currentDraftStart != null && currentDraftEnd != null && currentDraftStart.isAfter(currentDraftEnd)) ||
            (otherDraftStart != null && otherDraftEnd != null && otherDraftStart.isAfter(otherDraftEnd))
        ) {
            return false
        }

        // Handle cases where some end dates are null
        if (currentDraftStart == null || otherDraftStart == null) {
            return false
        }

        // Determine if the ranges overlap considering possible null end dates
        val endDateOverlaps = when {
            currentDraftEnd == null && otherDraftEnd == null -> true

            currentDraftEnd == null ->
                currentDraftStart.isBefore(otherDraftStart) || currentDraftStart.isEqual(otherDraftStart)

            otherDraftEnd == null ->
                otherDraftStart.isBefore(currentDraftEnd) || otherDraftStart.isEqual(currentDraftEnd)

            else ->
                (currentDraftStart.isBefore(otherDraftEnd) || currentDraftStart.isEqual(otherDraftEnd)) &&
                    (currentDraftEnd.isAfter(otherDraftStart) || currentDraftEnd.isEqual(otherDraftStart))
        }

        return endDateOverlaps
    }

    private fun modifyDraftEndDateIfInstallment(
        draft: CompensationDraft,
        paySchedulesById: Map<java.util.UUID, PaySchedule>,
    ): CompensationDraft {
        val payScheduleId = draft.payScheduleId ?: return draft
        val paySchedule = paySchedulesById[payScheduleId] ?: return draft

        return modifyEndDateIfInstalments(draft, paySchedule)
    }

    private fun isSameEmployee(
        draft: CompensationDraft,
        otherDraft: CompensationDraft,
        context: CompensationValidatorContext,
    ): Boolean = if (context.isBackFillContext()) {
        draft.contractId == otherDraft.contractId
    } else {
        draft.employeeId == otherDraft.employeeId
    }
}
