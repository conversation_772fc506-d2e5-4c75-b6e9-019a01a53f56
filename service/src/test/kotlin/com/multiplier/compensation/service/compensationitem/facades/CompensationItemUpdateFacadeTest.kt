package com.multiplier.compensation.service.compensationitem.facades

import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.service.compensationitem.dto.AdjustedItems
import com.multiplier.compensation.service.compensationitem.dto.BulkCompensationItemUpdateRequest
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUpdateRequest
import com.multiplier.compensation.service.compensationitem.dto.CompensationItemUpdateResponse
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationSchemaItemBuilder
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.handlers.UpdateHandler
import com.multiplier.compensation.service.compensationitem.update.handlers.UpdateHandlerFactory
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.UUID
import kotlin.test.assertFailsWith

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompensationItemUpdateFacadeTest {
    private lateinit var updateHandlerFactory: UpdateHandlerFactory
    private lateinit var compensationItemRepository: CompensationItemRepository
    private lateinit var transactional: Transactional
    private lateinit var facade: CompensationItemUpdateFacade

    @BeforeEach
    fun setUp() {
        updateHandlerFactory = mockk()
        compensationItemRepository = mockk()
        transactional = mockk()
        facade = CompensationItemUpdateFacade(
            updateHandlerFactory,
            compensationItemRepository,
            transactional,
        )
    }

    @ParameterizedTest(name = "{index} => {4}")
    @MethodSource("provideSingleCompensationUpdateTestCases")
    fun `test previewSingleCompensationItemUpdate`(
        oldImage: Compensation?,
        newImage: Compensation?,
        newRecord: Compensation?,
        schemaItem: CompensationSchemaItem?,
        shouldPersist: Boolean,
        description: String,
    ) {
        val adjustedItems = mockk<AdjustedItems> {
            every { oldImageItems } returns emptyList()
            every { newImageItems } returns emptyList()
            every { newRecordItems } returns emptyList()
        }
        val mockCompensationItems = listOf(mockk<CompensationItem>())
        val updateContext = mockk<CompensationUpdateContext> {
            every { <EMAIL> } returns oldImage
            every { <EMAIL> } returns newImage
            every { <EMAIL> } returns newRecord
            every { <EMAIL> } returns schemaItem
            every { <EMAIL> } returns adjustedItems
        }
        val handler = mockk<UpdateHandler> {
            every { handle(any()) } returns updateContext
        }

        val request = mockk<CompensationItemUpdateRequest> {
            every { <EMAIL> } returns oldImage
            every { <EMAIL> } returns newImage
            every { <EMAIL> } returns newRecord
            every { <EMAIL> } returns schemaItem
            every { <EMAIL> } returns shouldPersist
            every { <EMAIL> } returns null
        }

        every { transactional.invoke(any<TransactionContext.() -> List<CompensationItem>>()) } answers {
            firstArg<TransactionContext.() -> List<CompensationItem>>().invoke(mockk())
        }
        every { compensationItemRepository.getAllValidCompensationItems(any(), any()) } returns mockCompensationItems
        every { updateHandlerFactory.getUpdateHandler(any()) } returns handler

        // When
        val response = facade.previewSingleCompensationItemUpdate(request)

        // Then
        assertNotNull(response, "Response should not be null")
        assertEquals(adjustedItems, response.adjustedItems, description)
        if (shouldPersist) {
            assertNotNull(
                response.pendingTransaction,
                "Pending transaction should not be null when persistence is enabled",
            )
        } else {
            assertNull(response.pendingTransaction, "Pending transaction should be null when persistence is disabled")
        }
        verify { updateHandlerFactory.getUpdateHandler(any()) }
        verify { handler.handle(any()) }
    }

    @ParameterizedTest(name = "{index} => {5}")
    @MethodSource("provideParentUpdateContextTestCases")
    fun `test previewSingleCompensationItemUpdate with parent update context`(
        oldImage: Compensation?,
        newImage: Compensation?,
        newRecord: Compensation?,
        schemaItem: CompensationSchemaItem?,
        parentUpdateContext: CompensationUpdateContext?,
        shouldPersist: Boolean,
        description: String,
    ) {
        val adjustedItems = mockk<AdjustedItems> {
            every { oldImageItems } returns emptyList()
            every { newImageItems } returns emptyList()
            every { newRecordItems } returns emptyList()
        }
        val mockCompensationItems = listOf(mockk<CompensationItem>())
        val updateContext = mockk<CompensationUpdateContext> {
            every { <EMAIL> } returns oldImage
            every { <EMAIL> } returns newImage
            every { <EMAIL> } returns newRecord
            every { <EMAIL> } returns schemaItem
            every { <EMAIL> } returns adjustedItems
            every { <EMAIL> } returns parentUpdateContext
        }
        val handler = mockk<UpdateHandler> {
            every { handle(any()) } returns updateContext
        }

        val parentUpdateRequest = parentUpdateContext?.let {
            mockk<CompensationItemUpdateRequest> {
                every { <EMAIL> } returns it.oldImage
                every { <EMAIL> } returns it.newImage
                every { <EMAIL> } returns it.newRecord
                every { <EMAIL> } returns it.schemaItem
                every { <EMAIL> } returns null
            }
        }

        val request = mockk<CompensationItemUpdateRequest> {
            every { <EMAIL> } returns oldImage
            every { <EMAIL> } returns newImage
            every { <EMAIL> } returns newRecord
            every { <EMAIL> } returns schemaItem
            every { <EMAIL> } returns shouldPersist
            every { <EMAIL> } returns parentUpdateRequest
        }

        every { transactional.invoke(any<TransactionContext.() -> List<CompensationItem>>()) } answers {
            firstArg<TransactionContext.() -> List<CompensationItem>>().invoke(mockk())
        }
        every { compensationItemRepository.getAllValidCompensationItems(any(), any()) } returns mockCompensationItems
        every { updateHandlerFactory.getUpdateHandler(any()) } returns handler

        // When
        val response = facade.previewSingleCompensationItemUpdate(request)

        // Then
        assertNotNull(response, "Response should not be null")
        assertEquals(adjustedItems, response.adjustedItems, description)
        if (shouldPersist) {
            assertNotNull(
                response.pendingTransaction,
                "Pending transaction should not be null when persistence is enabled",
            )
        } else {
            assertNull(response.pendingTransaction, "Pending transaction should be null when persistence is disabled")
        }
        verify { updateHandlerFactory.getUpdateHandler(any()) }
        verify { handler.handle(any()) }
    }

    @ParameterizedTest(name = "{index} => {4}")
    @MethodSource("provideInvalidUpdateRequestTestCases")
    fun `test previewSingleCompensationItemUpdate with invalid combinations`(
        oldImage: Compensation?,
        newImage: Compensation?,
        newRecord: Compensation?,
        schemaItem: CompensationSchemaItem?,
        parentUpdateContext: CompensationUpdateContext?,
        expectedErrorMessage: String,
    ) {
        val mockCompensationItems = listOf(mockk<CompensationItem>())
        val mockedParentUpdateRequest = parentUpdateContext?.let {
            mockk<CompensationItemUpdateRequest> {
                every { <EMAIL> } returns it.oldImage
                every { <EMAIL> } returns it.newImage
                every { <EMAIL> } returns it.newRecord
                every { <EMAIL> } returns null
                every { <EMAIL> } returns it.schemaItem
            }
        }
        val request = mockk<CompensationItemUpdateRequest> {
            every { <EMAIL> } returns oldImage
            every { <EMAIL> } returns newImage
            every { <EMAIL> } returns newRecord
            every { <EMAIL> } returns schemaItem
            every { <EMAIL> } returns mockedParentUpdateRequest
        }

        every { transactional.invoke(any<TransactionContext.() -> List<CompensationItem>>()) } answers {
            firstArg<TransactionContext.() -> List<CompensationItem>>().invoke(mockk())
        }
        every { compensationItemRepository.getAllValidCompensationItems(any(), any()) } returns mockCompensationItems
        every { updateHandlerFactory.getUpdateHandler(any()) } throws InvalidArgumentException(
            errorCode = ValidationErrorCode.InvalidItemAdjustmentRequest,
            message = expectedErrorMessage,
        )

        // When & Then
        val exception = assertFailsWith<InvalidArgumentException> {
            facade.previewSingleCompensationItemUpdate(request)
        }

        assertEquals(expectedErrorMessage, exception.message, "Error message should match the expected one.")
    }

    @ParameterizedTest(name = "{index} => {4}")
    @MethodSource("provideTestCasesForPreviewBulkCompensationItemUpdate")
    fun `test previewBulkCompensationItemUpdate`(
        adjustedItems: AdjustedItems,
        shouldPersist: Boolean,
        expectedUpdatedItems: List<CompensationItem>,
        expectedNewItems: List<CompensationItem>,
        description: String,
    ) {
        val mockRepository = mockk<CompensationItemRepository> {
            every { getAllValidCompensationItems(any(), any()) } returns listOf(
                TestCompensationItemBuilder().build(),
                TestCompensationItemBuilder().build(),
            )
        }

        val (individualRequests, mockIndividualResponses) = createIndividualRequestsAndResponses(adjustedItems)

        val facade = spyk(
            CompensationItemUpdateFacade(
                updateHandlerFactory = mockk(),
                compensationItemRepository = mockRepository,
                transactional = mockk(),
            ),
        ) {
            individualRequests.zip(mockIndividualResponses).forEach { (request, response) ->
                every { previewSingleCompensationItemUpdate(request) } returns response
            }
        }

        val bulkRequest = BulkCompensationItemUpdateRequest(
            individualRequests = individualRequests,
            shouldPersist = shouldPersist,
        )

        // When
        val response = facade.previewBulkCompensationItemUpdate(bulkRequest)

        // Then
        assertNotNull(response, description)
        assertEquals(individualRequests.size, response.individualResponses.size)

        if (shouldPersist) {
            assertNotNull(response.pendingTransaction)
            assertEquals(expectedUpdatedItems, response.pendingTransaction!!.updatedCompensationItems)
            assertEquals(expectedNewItems, response.pendingTransaction!!.newCompensationItems)
        } else {
            assertNull(response.pendingTransaction)
        }
    }

    fun provideSingleCompensationUpdateTestCases(): List<Arguments> = listOf(
        Arguments.of(
            null,
            null,
            mockk<Compensation> { every { id } returns UUID.randomUUID() },
            mockk<CompensationSchemaItem>(),
            false,
            "Should trigger ComponentAdditionHandler with no persistence",
        ),
        Arguments.of(
            mockk<Compensation> { every { id } returns UUID.randomUUID() },
            null,
            null,
            mockk<CompensationSchemaItem>(),
            false,
            "Should trigger ImplicitUpdateHandler for valid parent context",
        ),
        Arguments.of(
            mockk<Compensation> { every { id } returns UUID.randomUUID() },
            mockk<Compensation> { every { id } returns UUID.randomUUID() },
            null,
            mockk<CompensationSchemaItem>(),
            false,
            "Should trigger TermUpdateHandler for oldImage and newImage without newRecord",
        ),
        Arguments.of(
            mockk<Compensation> { every { id } returns UUID.randomUUID() },
            mockk<Compensation> { every { id } returns UUID.randomUUID() },
            mockk<Compensation> { every { id } returns UUID.randomUUID() },
            mockk<CompensationSchemaItem>(),
            true,
            "Should trigger ValueUpdateHandler with persistence enabled",
        ),
    )

    companion object {
        @JvmStatic
        fun provideInvalidUpdateRequestTestCases(): List<Arguments> = listOf(
            Arguments.of(
                null,
                null,
                null,
                null,
                null,
                "No suitable update handler found for the update context.",
            ),
            Arguments.of(
                mockk<Compensation> { every { id } returns UUID.randomUUID() },
                null,
                null,
                mockk<CompensationSchemaItem>(),
                mockk<CompensationUpdateContext> {
                    every { oldImage } returns null
                    every { newImage } returns null
                    every { newRecord } returns null
                    every { schemaItem } returns null
                },
                "Incompatible update context for CompensationImplicitUpdateHandler. " +
                    "parentUpdateContext should be non-null and non-empty. " +
                    "newImage and newRecord should be null and oldImage should be non-null.",
            ),
            Arguments.of(
                null,
                mockk<Compensation> { every { id } returns UUID.randomUUID() },
                null,
                mockk<CompensationSchemaItem>(),
                null,
                "Incompatible update context for CompensationTermUpdateHandler." +
                    "newImage and oldImage should be non-null and newRecord should be null.",
            ),
            Arguments.of(
                mockk<Compensation> { every { id } returns UUID.randomUUID() },
                mockk<Compensation> { every { id } returns UUID.randomUUID() },
                null,
                mockk<CompensationSchemaItem>(),
                mockk<CompensationUpdateContext> {
                    every { oldImage } returns mockk()
                    every { newImage } returns null
                    every { newRecord } returns null
                    every { schemaItem } returns null
                },
                "Incompatible update context for CompensationValueUpdateHandler." +
                    "newImage, oldImage and newRecord should be non-null.",
            ),
        )
    }

    fun provideParentUpdateContextTestCases(): List<Arguments> = listOf(
        parentContextWithValidOldImage(),
        parentContextWithValidNewImage(),
        parentContextWithValidOldAndNewImage(),
        parentContextWithAllValidFields(),
        parentContextWithNullValues(),
    )

    private fun parentContextWithValidOldImage() = Arguments.of(
        mockk<Compensation> { every { id } returns UUID.randomUUID() },
        null,
        null,
        mockk<CompensationSchemaItem>(),
        mockk<CompensationUpdateContext> {
            every { oldImage } returns mockk()
            every { newImage } returns null
            every { newRecord } returns null
            every { schemaItem } returns mockk()
        },
        false,
        "Parent update context with valid oldImage",
    )

    private fun parentContextWithValidNewImage() = Arguments.of(
        null,
        mockk<Compensation> { every { id } returns UUID.randomUUID() },
        null,
        mockk<CompensationSchemaItem>(),
        mockk<CompensationUpdateContext> {
            every { oldImage } returns null
            every { newImage } returns mockk()
            every { newRecord } returns null
            every { schemaItem } returns mockk()
        },
        true,
        "Parent update context with valid newImage and persistence enabled",
    )

    private fun parentContextWithValidOldAndNewImage() = Arguments.of(
        mockk<Compensation> { every { id } returns UUID.randomUUID() },
        mockk<Compensation> { every { id } returns UUID.randomUUID() },
        null,
        mockk<CompensationSchemaItem>(),
        mockk<CompensationUpdateContext> {
            every { oldImage } returns mockk()
            every { newImage } returns mockk()
            every { newRecord } returns null
            every { schemaItem } returns mockk()
        },
        false,
        "Parent update context with valid oldImage and newImage",
    )

    private fun parentContextWithAllValidFields() = Arguments.of(
        mockk<Compensation> { every { id } returns UUID.randomUUID() },
        mockk<Compensation> { every { id } returns UUID.randomUUID() },
        mockk<Compensation> { every { id } returns UUID.randomUUID() },
        mockk<CompensationSchemaItem>(),
        mockk<CompensationUpdateContext> {
            every { oldImage } returns mockk()
            every { newImage } returns mockk()
            every { newRecord } returns mockk()
            every { schemaItem } returns mockk()
        },
        true,
        "Parent update context with valid oldImage, newImage, and newRecord",
    )

    private fun parentContextWithNullValues() = Arguments.of(
        null,
        null,
        null,
        mockk<CompensationSchemaItem>(),
        mockk<CompensationUpdateContext> {
            every { oldImage } returns null
            every { newImage } returns null
            every { newRecord } returns null
            every { schemaItem } returns null
        },
        false,
        "Parent update context with all null values",
    )

    fun provideTestCasesForPreviewBulkCompensationItemUpdate(): List<Arguments> = listOf(
        scenarioWithUpdatedAndNewItems(),
        scenarioWithNewRecordAndNoPersistence(),
        scenarioWithOnlyUpdatedOldImageItems(),
        scenarioWithMixedUpdatedAndNewItems(),
        scenarioWithNoAdjustedItemsAndPersistence(),
        scenarioWithNoAdjustedItemsAndNoPersistence(),
    )

    private fun createIndividualRequestsAndResponses(
        adjustedItems: AdjustedItems,
    ): Pair<List<CompensationItemUpdateRequest>, List<CompensationItemUpdateResponse>> {
        val scenarios = listOf(
            CompensationItemUpdateRequest(
                oldImage = TestCompensationBuilder().build(),
                newImage = null,
                newRecord = null,
                schemaItem = TestCompensationSchemaItemBuilder().build(),
            ),
            CompensationItemUpdateRequest(
                oldImage = null,
                newImage = TestCompensationBuilder().build(),
                newRecord = null,
                schemaItem = TestCompensationSchemaItemBuilder().build(),
            ),
            CompensationItemUpdateRequest(
                oldImage = TestCompensationBuilder().build(),
                newImage = TestCompensationBuilder().build(),
                newRecord = null,
                schemaItem = TestCompensationSchemaItemBuilder().build(),
            ),
            CompensationItemUpdateRequest(
                oldImage = null,
                newImage = null,
                newRecord = TestCompensationBuilder().build(),
                schemaItem = TestCompensationSchemaItemBuilder().build(),
            ),
        )

        val mockResponses = scenarios.map {
            CompensationItemUpdateResponse(
                oldImage = it.oldImage,
                updatedNewImage = it.newImage,
                updatedNewRecord = it.newRecord,
                adjustedItems = adjustedItems,
                pendingTransaction = null,
            )
        }

        return Pair(scenarios, mockResponses)
    }

    private fun scenarioWithUpdatedAndNewItems(): Arguments {
        val updatedOldImageItem = TestCompensationItemBuilder()
            .withUpdateTriggerReference("updatedOldTrigger")
            .build()
        val updatedNewImageItem = TestCompensationItemBuilder()
            .withUpdateTriggerReference("updatedNewTrigger")
            .build()
        val newRecordItem = TestCompensationItemBuilder().build()
        return Arguments.of(
            AdjustedItems(
                oldImageItems = listOf(updatedOldImageItem),
                newImageItems = listOf(updatedNewImageItem),
                newRecordItems = listOf(newRecordItem),
            ),
            true,
            listOf(updatedOldImageItem, updatedNewImageItem),
            listOf(newRecordItem),
            "Persistence enabled with updated and new items",
        )
    }

    private fun scenarioWithNewRecordAndNoPersistence(): Arguments {
        val newRecordItem = TestCompensationItemBuilder().build()
        return Arguments.of(
            AdjustedItems(
                oldImageItems = emptyList(),
                newImageItems = emptyList(),
                newRecordItems = listOf(newRecordItem),
            ),
            false,
            emptyList<CompensationItem>(),
            emptyList<CompensationItem>(),
            "Persistence disabled with new record item",
        )
    }

    private fun scenarioWithOnlyUpdatedOldImageItems(): Arguments {
        val updatedOldImageItem = TestCompensationItemBuilder()
            .withUpdateTriggerReference("updatedOldTrigger")
            .build()
        return Arguments.of(
            AdjustedItems(
                oldImageItems = listOf(updatedOldImageItem),
                newImageItems = emptyList(),
                newRecordItems = emptyList(),
            ),
            true,
            listOf(updatedOldImageItem),
            emptyList<CompensationItem>(),
            "Persistence enabled with only updated oldImage items",
        )
    }

    private fun scenarioWithMixedUpdatedAndNewItems(): Arguments {
        val updatedOldImageItem = TestCompensationItemBuilder()
            .withUpdateTriggerReference("updatedOldTrigger")
            .build()
        val updatedNewImageItem = TestCompensationItemBuilder()
            .withUpdateTriggerReference("updatedNewTrigger")
            .build()
        val newOldImageItem = TestCompensationItemBuilder().build()
        val newNewImageItem = TestCompensationItemBuilder().build()
        val newRecordItem = TestCompensationItemBuilder().build()
        return Arguments.of(
            AdjustedItems(
                oldImageItems = listOf(updatedOldImageItem, newOldImageItem),
                newImageItems = listOf(updatedNewImageItem, newNewImageItem),
                newRecordItems = listOf(newRecordItem),
            ),
            true,
            listOf(updatedOldImageItem, updatedNewImageItem),
            listOf(newOldImageItem, newNewImageItem, newRecordItem),
            "Persistence enabled with mixed updated and new items",
        )
    }

    private fun scenarioWithNoAdjustedItemsAndPersistence(): Arguments = Arguments.of(
        AdjustedItems(
            oldImageItems = emptyList(),
            newImageItems = emptyList(),
            newRecordItems = emptyList(),
        ),
        true,
        emptyList<CompensationItem>(),
        emptyList<CompensationItem>(),
        "Persistence enabled with no adjusted items",
    )

    private fun scenarioWithNoAdjustedItemsAndNoPersistence(): Arguments = Arguments.of(
        AdjustedItems(
            oldImageItems = emptyList(),
            newImageItems = emptyList(),
            newRecordItems = emptyList(),
        ),
        false,
        emptyList<CompensationItem>(),
        emptyList<CompensationItem>(),
        "Persistence disabled with no adjusted items",
    )
}
