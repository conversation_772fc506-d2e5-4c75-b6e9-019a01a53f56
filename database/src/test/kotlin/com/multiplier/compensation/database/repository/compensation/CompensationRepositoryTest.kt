package com.multiplier.compensation.database.repository.compensation

import com.multiplier.common.exception.MplSystemException
import com.multiplier.compensation.database.mapper.compensation.common.toDatabase
import com.multiplier.compensation.database.mapper.compensation.toDatabase
import com.multiplier.compensation.database.tables.records.CompensationRecord
import com.multiplier.compensation.database.tables.references.COMPENSATION_
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA_ITEM
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import com.multiplier.compensation.database.util.CompensationRecordConditionBuilder.buildCompensationRecordFilterCondition
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.CompensationEnriched
import com.multiplier.compensation.domain.compensation.CompensationRecordsFilter
import com.multiplier.compensation.domain.compensation.CompensationRecordsGrpcFilter
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.transaction.database.jooq.audit.Audit
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.Field
import org.jooq.Record1
import org.jooq.RecordMapper
import org.jooq.Result
import org.jooq.SelectConditionStep
import org.jooq.SelectLimitPercentStep
import org.jooq.SelectWhereStep
import org.jooq.Table
import org.jooq.impl.DSL
import org.jooq.impl.DSL.currentLocalDate
import org.jooq.impl.DSL.partitionBy
import org.jooq.impl.DSL.rank
import org.jooq.impl.SQLDataType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertNull
import com.multiplier.compensation.database.enums.CompensationStatus as DatabaseCompensationStatus

@ExtendWith(MockKExtension::class)
class CompensationRepositoryTest {
    private lateinit var audit: Audit
    private lateinit var dsl: DSLContext
    private lateinit var transactionContext: TransactionContext
    private lateinit var repository: CompensationRepository

    @BeforeEach
    fun setUp() {
        audit = mockk()
        dsl = mockk()
        transactionContext = mockk()
        repository = CompensationRepository(dsl)
        every { transactionContext.trx } returns dsl
        every { transactionContext.audit } returns audit
        every { audit.userId } returns -1
        every { audit.revision } returns UUID.randomUUID()
        every { audit.time } returns LocalDateTime.now()
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return compensation when findById is called`() {
        val id = UUID.randomUUID()

        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()
        val mappedCompensation = mockedCompensation() // The mapped compensation domain object
        val expectedCompensationRecord = mockedCompensationRecord(mappedCompensation)

        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(COMPENSATION_.ID.eq(id)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetchOne() } returns expectedCompensationRecord

        val result = repository.findById(id)

        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(COMPENSATION_.ID.eq(id))
            mockSelectConditionStep.fetchOne()
        }

        assertEquals(result, mappedCompensation)
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return null when no compensation is found`() {
        val id = UUID.randomUUID()

        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()

        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(COMPENSATION_.ID.eq(id)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetchOne() } returns null // No record found

        val result = repository.findById(id)

        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(COMPENSATION_.ID.eq(id))
            mockSelectConditionStep.fetchOne()
        }

        assertNull(result)
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should throw exception when query fails`() {
        val id = UUID.randomUUID()

        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()

        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(COMPENSATION_.ID.eq(id)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetchOne() } throws RuntimeException("Database error") // Simulate query failure

        val exception = assertThrows<RuntimeException> {
            repository.findById(id)
        }

        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(COMPENSATION_.ID.eq(id))
            mockSelectConditionStep.fetchOne()
        }

        assertEquals("Database error", exception.message)
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should use transaction context when provided`() {
        val id = UUID.randomUUID()
        val transactionContext = mockk<TransactionContext>()
        val mockDSLContext = mockk<DSLContext>()
        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()
        val mappedCompensation = mockedCompensation()
        val expectedCompensationRecord = mockedCompensationRecord(mappedCompensation)

        every { transactionContext.trx } returns mockDSLContext
        every { mockDSLContext.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(COMPENSATION_.ID.eq(id)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetchOne() } returns expectedCompensationRecord

        val result = repository.findById(id, transactionContext)

        verify {
            transactionContext.trx
            mockDSLContext.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(COMPENSATION_.ID.eq(id))
            mockSelectConditionStep.fetchOne()
        }

        assertEquals(result, mappedCompensation)
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return a compensation when findForIds is called`() {
        val id = UUID.randomUUID()
        val ids = listOf(id)
        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()
        val mappedCompensation = mockedCompensation()
        val expectedCompensations = mockk<Result<CompensationRecord>>()

        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(COMPENSATION_.ID.`in`(ids)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns expectedCompensations

        every { expectedCompensations.map(match<RecordMapper<CompensationRecord, Compensation>> { true }) } answers {
            listOf(mappedCompensation) // Return the mocked mapped result
        }

        val result = repository.findForIds(ids)

        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(COMPENSATION_.ID.`in`(ids))
            mockSelectConditionStep.fetch()
            expectedCompensations.map(any<RecordMapper<CompensationRecord, Compensation>>()) // Verify map is called
        }

        assertEquals(listOf(mappedCompensation), result)
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return an empty list when no compensation is found`() {
        val id = UUID.randomUUID()
        val ids = listOf(id)
        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()
        val emptyResult = mockk<Result<CompensationRecord>>() // Mock an empty Result

        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(COMPENSATION_.ID.`in`(ids)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns emptyResult
        every { emptyResult.map(any<RecordMapper<CompensationRecord, Compensation>>()) } returns emptyList()

        val result = repository.findForIds(ids)

        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(COMPENSATION_.ID.`in`(ids))
            mockSelectConditionStep.fetch()
            emptyResult.map(any<RecordMapper<CompensationRecord, Compensation>>())
        }

        assertTrue(result.isEmpty())
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return compensations when findAllByContractId is called`() {
        val contractId = 5000L
        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()
        val expectedCompensations = mockk<Result<CompensationRecord>>()
        val mappedCompensations = listOf(mockedCompensation())

        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(COMPENSATION_.CONTRACT_ID.eq(contractId)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns expectedCompensations

        every {
            expectedCompensations.map(any<RecordMapper<CompensationRecord, Compensation>>())
        } returns mappedCompensations

        val result = repository.findAllByContractId(contractId)

        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(COMPENSATION_.CONTRACT_ID.eq(contractId))
            mockSelectConditionStep.fetch()
        }

        assertEquals(result, mappedCompensations)
    }

    @Test
    fun `should save all entries`() {
        val entityId = 1L
        every {
            transactionContext.trx.batchInsert(any<List<CompensationRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        repository.saveAll(
            listOf(mockedCompensation()),
            transactionContext,
        )

        verify(exactly = 2) {
            transactionContext.trx.batchInsert(any<List<CompensationRecord>>()).execute()
        }
    }

    @Test
    fun `should return distinct contract ids`() {
        val entityId = 1L
        val contractIds = setOf(1L, 2L, 3L)

        val mockRecord1: Record1<Long?> = mockk {
            every { value1() } returns 1L
        }
        val mockRecord2: Record1<Long?> = mockk {
            every { value1() } returns 2L
        }

        val mockFetchResult: Result<Record1<Long?>> = mockk()

        every {
            dsl.selectDistinct(COMPENSATION_.CONTRACT_ID)
                .from(COMPENSATION_)
                .where(
                    COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED))
                        .and(COMPENSATION_.ENTITY_ID.eq(entityId))
                        .and(COMPENSATION_.CONTRACT_ID.`in`(contractIds)),
                )
                .fetch()
        } returns mockFetchResult

        every {
            mockFetchResult.map(any<RecordMapper<Record1<Long?>, Long?>>())
        } answers {
            listOf(mockRecord1, mockRecord2).map { record -> record.value1() }
        }

        val result = repository.fetchValidContractIdsForEntity(entityId, contractIds)

        val expectedContractIds = listOf(1L, 2L)
        assertEquals(expectedContractIds, result)

        verify(exactly = 1) {
            dsl.selectDistinct(COMPENSATION_.CONTRACT_ID)
                .from(COMPENSATION_)
                .where(
                    COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED))
                        .and(COMPENSATION_.ENTITY_ID.eq(entityId))
                        .and(COMPENSATION_.CONTRACT_ID.`in`(contractIds)),
                )
                .fetch()
        }
    }

    @Test
    fun `should return compensations by contract ids`() {
        val entityId = 1L
        val contractIds = setOf(1L, 2L, 3L)

        val mockFetchResult: Result<CompensationRecord> = mockk()
        val expectedCompensations = listOf(mockedCompensation())

        every {
            dsl.selectFrom(COMPENSATION_)
                .where(
                    COMPENSATION_.ENTITY_ID.eq(entityId).and(
                        COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED)),
                    ).and(
                        COMPENSATION_.CONTRACT_ID.`in`(contractIds),
                    ),
                ).fetch()
        } returns mockFetchResult

        every {
            mockFetchResult.map(any<RecordMapper<CompensationRecord, Compensation>>())
        } answers {
            expectedCompensations
        }

        val result = repository.fetchAllValidCompensationsForContractIds(entityId, contractIds)

        assertEquals(expectedCompensations, result)

        verify(exactly = 1) {
            dsl.selectFrom(COMPENSATION_)
                .where(
                    COMPENSATION_.ENTITY_ID.eq(entityId).and(
                        COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED)),
                    ).and(
                        COMPENSATION_.CONTRACT_ID.`in`(contractIds),
                    ),
                ).fetch()
        }
    }

    @Test
    fun `should return active compensations for ended contract`() {
        val contractIds = setOf(1L, 2L, 3L)
        val earliestLastWorkingDayOfAnyContract = LocalDate.of(2025, 10, 1)

        val mockFetchResult: Result<CompensationRecord> = mockk()
        val expectedCompensations = listOf(mockedCompensation())

        every {
            dsl.selectFrom(COMPENSATION_)
                .where(
                    (
                        COMPENSATION_.CONTRACT_ID.`in`(contractIds)
                    ).and(
                        COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED)),
                    ).and(
                        COMPENSATION_.END_DATE.isNull.or(
                            COMPENSATION_.END_DATE.greaterThan(earliestLastWorkingDayOfAnyContract),
                        ),
                    ),
                ).fetch()
        } returns mockFetchResult

        every {
            mockFetchResult.map(any<RecordMapper<CompensationRecord, Compensation>>())
        } answers {
            expectedCompensations
        }

        val result = repository.fetchAllActiveCompensationsForOffBoardedContract(
            contractIds,
            earliestLastWorkingDayOfAnyContract,
        )

        assertEquals(expectedCompensations, result)

        verify(exactly = 1) {
            dsl.selectFrom(COMPENSATION_)
                .where(
                    (
                        COMPENSATION_.CONTRACT_ID.`in`(contractIds)
                    ).and(
                        COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED)),
                    ).and(
                        COMPENSATION_.END_DATE.isNull.or(
                            COMPENSATION_.END_DATE.greaterThan(earliestLastWorkingDayOfAnyContract),
                        ),
                    ),
                ).fetch()
        }
    }

    @Test
    fun `buildCompensationRecordFilterCondition should return correct conditions`() {
        val filter = CompensationRecordsFilter(
            entityId = 1L,
            companyId = 1000L,
            contractIds = listOf(123L, 456L),
            categories = listOf("category1", "category2"),
            startDate = LocalDate.of(2023, 1, 1),
            endDate = LocalDate.of(2023, 12, 31),
        )
        val expectedConditions = listOf<Condition>(
            COMPENSATION_.ENTITY_ID.eq(filter.entityId),
            COMPENSATION_.COMPANY_ID.eq(filter.companyId),
            COMPENSATION_.CONTRACT_ID.`in`(filter.contractIds),
            COMPENSATION_.CATEGORY.`in`(filter.categories),
            COMPENSATION_.START_DATE.ge(filter.startDate),
            COMPENSATION_.END_DATE.le(filter.endDate),
        )

        val actualConditions = buildCompensationRecordFilterCondition(filter)
        assertEquals(expectedConditions, actualConditions)
    }

    @Test
    fun `should build conditions for COMPLETED state`() {
        val filter = CompensationRecordsFilter(
            state = CompensationState.COMPLETED,
            entityId = 1L,
            companyId = 1000L,
            contractIds = listOf(123L, 456L),
            categories = listOf("category1", "category2"),
        )
        val expectedConditions = listOf<Condition>(
            COMPENSATION_.ENTITY_ID.eq(filter.entityId),
            COMPENSATION_.COMPANY_ID.eq(filter.companyId),
            COMPENSATION_.CONTRACT_ID.`in`(filter.contractIds),
            COMPENSATION_.CATEGORY.`in`(filter.categories),
            COMPENSATION_.STATUS.notIn(DatabaseCompensationStatus.DELETED, DatabaseCompensationStatus.ABORTED).and(
                COMPENSATION_.PROCESSING_TO.isNotNull.and(
                    COMPENSATION_.PROCESSING_TO.lessThan(LocalDate.now().atStartOfDay()),
                ),
            ),
        )

        val actualConditions = buildCompensationRecordFilterCondition(filter)
        assertEquals(expectedConditions, actualConditions)
    }

    @Test
    fun `should build conditions for UPCOMING state`() {
        val filter = CompensationRecordsFilter(
            state = CompensationState.UPCOMING,
            entityId = 1L,
            companyId = 1000L,
            contractIds = listOf(123L, 456L),
            categories = listOf("category1", "category2"),
        )
        val expectedConditions = listOf<Condition>(
            COMPENSATION_.ENTITY_ID.eq(filter.entityId),
            COMPENSATION_.COMPANY_ID.eq(filter.companyId),
            COMPENSATION_.CONTRACT_ID.`in`(filter.contractIds),
            COMPENSATION_.CATEGORY.`in`(filter.categories),
            COMPENSATION_.STATUS.notIn(DatabaseCompensationStatus.DELETED, DatabaseCompensationStatus.ABORTED).and(
                COMPENSATION_.PROCESSING_FROM.isNull.or(
                    COMPENSATION_.PROCESSING_FROM.greaterOrEqual(LocalDate.now().atStartOfDay()),
                ),
            ),
        )

        val actualConditions = buildCompensationRecordFilterCondition(filter)
        assertEquals(expectedConditions, actualConditions)
    }

    @Test
    fun `should build conditions for PROCESSING state`() {
        val filter = CompensationRecordsFilter(
            state = CompensationState.PROCESSING,
            entityId = 1L,
            companyId = 1000L,
            contractIds = listOf(123L, 456L),
            categories = listOf("category1", "category2"),
        )
        val expectedConditions = listOf<Condition>(
            COMPENSATION_.ENTITY_ID.eq(filter.entityId),
            COMPENSATION_.COMPANY_ID.eq(filter.companyId),
            COMPENSATION_.CONTRACT_ID.`in`(filter.contractIds),
            COMPENSATION_.CATEGORY.`in`(filter.categories),
            COMPENSATION_.STATUS.notIn(DatabaseCompensationStatus.DELETED, DatabaseCompensationStatus.ABORTED).and(
                COMPENSATION_.PROCESSING_FROM.isNotNull.and(
                    COMPENSATION_.PROCESSING_FROM.lessThan(LocalDate.now().atStartOfDay()),
                )
                    .and(
                        COMPENSATION_.PROCESSING_TO.isNull.or(
                            COMPENSATION_.PROCESSING_TO.greaterOrEqual(LocalDate.now().atStartOfDay()),
                        ),
                    ),
            ),
        )

        val actualConditions = buildCompensationRecordFilterCondition(filter)
        assertEquals(expectedConditions, actualConditions)
    }

    @Test
    fun `should build conditions for ACTIVE state`() {
        val filter = CompensationRecordsFilter(
            state = CompensationState.ACTIVE,
            entityId = 1L,
            companyId = 1000L,
            contractIds = listOf(123L, 456L),
            categories = listOf("category1", "category2"),
        )
        val expectedConditions = listOf<Condition>(
            COMPENSATION_.ENTITY_ID.eq(filter.entityId),
            COMPENSATION_.COMPANY_ID.eq(filter.companyId),
            COMPENSATION_.CONTRACT_ID.`in`(filter.contractIds),
            COMPENSATION_.CATEGORY.`in`(filter.categories),
            COMPENSATION_.STATUS.notIn(
                com.multiplier.compensation.database.enums.CompensationStatus.DELETED,
                com.multiplier.compensation.database.enums.CompensationStatus.ABORTED,
            )
                .and(
                    COMPENSATION_.PROCESSING_TO.isNull.or(
                        COMPENSATION_.PROCESSING_TO.greaterOrEqual(LocalDate.now().atStartOfDay()),
                    ),
                ),
            COMPENSATION_.ID.`in`(
                DSL.select(DSL.field("non_duplicate_compensation_query.id", UUID::class.java))
                    .from(COMPENSATION_.`as`("non_duplicate_compensation_query"))
                    .join(
                        DSL.select(
                            COMPENSATION_.SCHEMA_ITEM_ID.`as`("schema_item"),
                            COMPENSATION_.CONTRACT_ID.`as`("contract_id"),
                            DSL.min(COMPENSATION_.START_DATE).`as`("earliest_start_date"),
                        )
                            .from(COMPENSATION_)
                            .where(
                                COMPENSATION_.STATUS.notIn(
                                    DatabaseCompensationStatus.DELETED,
                                    DatabaseCompensationStatus.ABORTED,
                                )
                                    .and(
                                        COMPENSATION_.PROCESSING_TO.isNull.or(
                                            COMPENSATION_.PROCESSING_TO.greaterOrEqual(LocalDate.now().atStartOfDay()),
                                        ),
                                    ),
                            )
                            .groupBy(COMPENSATION_.SCHEMA_ITEM_ID, COMPENSATION_.CONTRACT_ID)
                            .asTable("earliest_compensation_query"),
                    )
                    .on(
                        DSL.field(
                            "non_duplicate_compensation_query.schema_item_id",
                        ).eq(DSL.field("earliest_compensation_query.schema_item"))
                            .and(
                                DSL.field(
                                    "non_duplicate_compensation_query.start_date",
                                ).eq(DSL.field("earliest_compensation_query.earliest_start_date")),
                            )
                            .and(
                                DSL.field(
                                    "non_duplicate_compensation_query.contract_id",
                                ).eq(DSL.field("earliest_compensation_query.contract_id")),
                            ),
                    ),
            ),
        )

        val actualConditions = buildCompensationRecordFilterCondition(filter)
        assertEquals(expectedConditions, actualConditions)
    }

    @Test
    fun `should return enriched compensation records with filters `() {
        val filter = CompensationRecordsGrpcFilter(
            entityId = 1L,
            contractIds = listOf(123L, 456L),
            categories = listOf("category1", "category2"),
        )
        val conditions = buildCompensationRecordFilterCondition(filter)
        val mockFetchResult: Result<org.jooq.Record> = mockk()
        val expectedRecords = listOf(mockk<CompensationEnriched>(), mockk<CompensationEnriched>())
        every {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(conditions)
                .fetch()
        } returns mockFetchResult

        every {
            mockFetchResult.map(any<RecordMapper<org.jooq.Record, CompensationEnriched>>())
        } returns expectedRecords

        val result = repository.getEnrichedCompensationRecordsWithFilters(filter)

        assertEquals(expectedRecords, result)
        verify {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(conditions)
                .fetch()
        }
    }

    @Test
    fun `should fetch base pay components for contract`() {
        val contractId = 1L
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 12, 31)
        val expectedCompensations = listOf(mockk<Compensation>())
        val mockResult: Result<CompensationRecord> = mockk()

        every {
            dsl.selectFrom(COMPENSATION_)
                .where(
                    COMPENSATION_.CONTRACT_ID.eq(contractId)
                        .and(COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED)))
                        .and(COMPENSATION_.CATEGORY.eq(CategoryConstants.CATEGORY_CONTRACT_BASE_PAY))
                        .and(COMPENSATION_.START_DATE.lessOrEqual(endDate))
                        .and(COMPENSATION_.END_DATE.isNull.or(COMPENSATION_.END_DATE.greaterOrEqual(startDate))),
                ).fetch()
        } returns mockResult

        every {
            mockResult.map(any<RecordMapper<CompensationRecord, Compensation>>())
        } answers {
            expectedCompensations
        }

        val result = repository.fetchBasePayComponentsForContract(contractId, startDate, endDate)

        assertEquals(expectedCompensations, result)

        verify {
            dsl.selectFrom(COMPENSATION_)
                .where(
                    COMPENSATION_.CONTRACT_ID.eq(contractId)
                        .and(COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED)))
                        .and(COMPENSATION_.CATEGORY.eq(CategoryConstants.CATEGORY_CONTRACT_BASE_PAY))
                        .and(COMPENSATION_.START_DATE.lessOrEqual(endDate))
                        .and(COMPENSATION_.END_DATE.isNull.or(COMPENSATION_.END_DATE.greaterOrEqual(startDate))),
                ).fetch()
        }
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return compensations eligible for item generation`() {
        // Arrange
        val limit = 10
        val itemGenerationWindowEndDate = LocalDate.of(2025, 1, 31)

        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()
        val mockSelectLimitStep = mockk<SelectLimitPercentStep<CompensationRecord>>()
        val expectedCompensations = mockk<Result<CompensationRecord>>()
        val mappedCompensations = listOf(mockedCompensation(), mockedCompensation())

        // Mock DSL behaviors
        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every {
            mockSelectWhereStep.where(
                (
                    COMPENSATION_.END_DATE.isNull
                        .or(COMPENSATION_.PROCESSED_UNTIL_DATE.isNull)
                )
                    .or(COMPENSATION_.END_DATE.gt(COMPENSATION_.PROCESSED_UNTIL_DATE))
                    .and(
                        COMPENSATION_.PROCESSED_UNTIL_DATE.isNull
                            .or(COMPENSATION_.PROCESSED_UNTIL_DATE.lt(itemGenerationWindowEndDate)),
                    )
                    .and(COMPENSATION_.START_DATE.lt(itemGenerationWindowEndDate))
                    .and(COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED))),
            )
        } returns mockSelectConditionStep

        every { mockSelectConditionStep.limit(limit) } returns mockSelectLimitStep
        every { mockSelectLimitStep.fetch() } returns expectedCompensations
        every { expectedCompensations.map(any<RecordMapper<CompensationRecord, Compensation>>()) } returns
            mappedCompensations

        // Act
        val result = repository.fetchAllEligibleForItemGeneration(limit, itemGenerationWindowEndDate)

        // Verify interactions
        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(
                (
                    COMPENSATION_.END_DATE.isNull
                        .or(COMPENSATION_.PROCESSED_UNTIL_DATE.isNull)
                )
                    .or(COMPENSATION_.END_DATE.gt(COMPENSATION_.PROCESSED_UNTIL_DATE))
                    .and(
                        COMPENSATION_.PROCESSED_UNTIL_DATE.isNull
                            .or(COMPENSATION_.PROCESSED_UNTIL_DATE.lt(itemGenerationWindowEndDate)),
                    )
                    .and(COMPENSATION_.START_DATE.lt(itemGenerationWindowEndDate))
                    .and(COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED))),
            )
            mockSelectConditionStep.limit(limit)
            mockSelectLimitStep.fetch()
            expectedCompensations.map(any<RecordMapper<CompensationRecord, Compensation>>())
        }

        // Assert the expected result
        assertEquals(mappedCompensations, result)
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return empty list when no eligible compensations are found`() {
        // Arrange
        val limit = 10
        val itemGenerationWindowEndDate = LocalDate.of(2025, 1, 31)

        val mockSelectWhereStep = mockk<SelectWhereStep<CompensationRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationRecord>>()
        val mockSelectLimitStep = mockk<SelectLimitPercentStep<CompensationRecord>>()
        val emptyResult = mockk<Result<CompensationRecord>>()

        // Mock DSL behaviors
        every { dsl.selectFrom(COMPENSATION_) } returns mockSelectWhereStep
        every {
            mockSelectWhereStep.where(
                (
                    COMPENSATION_.END_DATE.isNull
                        .or(COMPENSATION_.PROCESSED_UNTIL_DATE.isNull)
                )
                    .or(COMPENSATION_.END_DATE.gt(COMPENSATION_.PROCESSED_UNTIL_DATE))
                    .and(
                        COMPENSATION_.PROCESSED_UNTIL_DATE.isNull
                            .or(COMPENSATION_.PROCESSED_UNTIL_DATE.lt(itemGenerationWindowEndDate)),
                    )
                    .and(COMPENSATION_.START_DATE.lt(itemGenerationWindowEndDate))
                    .and(COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED))),
            )
        } returns mockSelectConditionStep

        every { mockSelectConditionStep.limit(limit) } returns mockSelectLimitStep
        every { mockSelectLimitStep.fetch() } returns emptyResult
        every { emptyResult.map(any<RecordMapper<CompensationRecord, Compensation>>()) } returns emptyList()

        // Act
        val result = repository.fetchAllEligibleForItemGeneration(limit, itemGenerationWindowEndDate)

        // Verify interactions
        verify {
            dsl.selectFrom(COMPENSATION_)
            mockSelectWhereStep.where(
                (
                    COMPENSATION_.END_DATE.isNull
                        .or(COMPENSATION_.PROCESSED_UNTIL_DATE.isNull)
                )
                    .or(COMPENSATION_.END_DATE.gt(COMPENSATION_.PROCESSED_UNTIL_DATE))
                    .and(
                        COMPENSATION_.PROCESSED_UNTIL_DATE.isNull
                            .or(COMPENSATION_.PROCESSED_UNTIL_DATE.lt(itemGenerationWindowEndDate)),
                    )
                    .and(COMPENSATION_.START_DATE.lt(itemGenerationWindowEndDate))
                    .and(COMPENSATION_.STATUS.notIn(listOf(CompensationStatus.ABORTED, CompensationStatus.DELETED))),
            )
            mockSelectConditionStep.limit(limit)
            mockSelectLimitStep.fetch()
            emptyResult.map(any<RecordMapper<CompensationRecord, Compensation>>())
        }

        // Assert the expected result
        assertTrue(result.isEmpty())
    }

    @Test
    fun `should update compensation record successfully`() {
        // Arrange: Prepare mock objects and behavior
        val compensation = mockedCompensation()

        every { transactionContext.trx.executeUpdate(any(), any()) } returns 1 // Simulate successful update
        every { transactionContext.trx.executeInsert(any()) } returns 1 // Simulate successful insert

        // Act: Call the method under test
        repository.update(compensation, transactionContext)

        // Assert: Verify interactions
        verify {
            transactionContext.trx.executeUpdate(any(), any())
        }
    }

    @Test
    fun `should throw exception when no record is updated`() {
        // Arrange: Prepare mock objects and behavior
        val compensation = mockedCompensation()
        // val updatedRecord = mockedCompensationRecord(compensation)

        every { transactionContext.trx.executeUpdate(any(), any()) } returns 0 // Simulate no record updated
        every { transactionContext.trx.executeInsert(any()) } returns 1 // Simulate successful insert

        // Act & Assert: Verify exception is thrown
        val exception = assertThrows<MplSystemException> {
            repository.update(compensation, transactionContext)
        }

        assertEquals(
            "Compensation record with id [${compensation.id}] and updatedOn [${compensation.updatedOn}] either does not exist or has been modified by another transaction.",
            exception.message,
        )
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return latest ended compensation records`() {
        val filter = CompensationRecordsFilter(
            contractIds = listOf(123L, 456L),
            categories = listOf("category1", "category2"),
        )

        val expectedRecords = listOf(
            mockk<CompensationEnriched>(),
            mockk<CompensationEnriched>(),
        )

        val mockedEnrichedRecords: Result<org.jooq.Record> = mockk()
        val rankedCompensations: Table<org.jooq.Record> = mockk()

        val rnkField: Field<Int> = DSL.field("rnk", SQLDataType.INTEGER)

        every { rankedCompensations.field("rnk", Int::class.java) } returns rnkField
        coreCompensationFields.forEach { field ->
            every { rankedCompensations.field(field) } returns mockk()
        }

        every { rankedCompensations.field(COMPENSATION_.PAY_SCHEDULE_ID)!!.eq(PAY_SCHEDULE.ID) } returns mockk()
        every { rankedCompensations.field(COMPENSATION_.SCHEMA_ITEM_ID)!!.eq(COMPENSATION_SCHEMA_ITEM.ID) } returns
            mockk()

        every {
            dsl.select(
                *coreCompensationFields,
                rank().over(
                    partitionBy(COMPENSATION_.CONTRACT_ID, COMPENSATION_.SCHEMA_ITEM_ID)
                        .orderBy(COMPENSATION_.END_DATE.desc()),
                ).`as`("rnk"),
            )
                .from(COMPENSATION_)
                .where(COMPENSATION_.CONTRACT_ID.`in`(filter.contractIds))
                .and(COMPENSATION_.CATEGORY.`in`(filter.categories))
                .and(COMPENSATION_.END_DATE.isNotNull)
                .and(COMPENSATION_.END_DATE.le(currentLocalDate()))
                .asTable("RankedCompensations")
        } returns rankedCompensations

        val whereCaptor = slot<Condition>()
        val rankedCompensationsFields = coreCompensationFields
            .mapNotNull { rankedCompensations.field(it) }
            .toTypedArray()
        every {
            dsl.select(
                *rankedCompensationsFields,
                rnkField,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(rankedCompensations)
                .leftJoin(PAY_SCHEDULE).on(
                    rankedCompensations.field(COMPENSATION_.PAY_SCHEDULE_ID)!!.eq(PAY_SCHEDULE.ID),
                )
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(
                    rankedCompensations.field(COMPENSATION_.SCHEMA_ITEM_ID)!!.eq(COMPENSATION_SCHEMA_ITEM.ID),
                )
                .where(capture(whereCaptor))
                .fetch()
        } returns mockedEnrichedRecords

        val recordMapperSlot = slot<RecordMapper<org.jooq.Record, CompensationEnriched>>()
        every { mockedEnrichedRecords.map(capture(recordMapperSlot)) } returns expectedRecords

        val result = repository.getLatestEndedCompensationRecords(filter)

        assertEquals(expectedRecords, result)
        assertTrue(whereCaptor.captured.toString().contains("rnk"), "WHERE clause should filter rnk = 1")

        verify(exactly = 1) {
            dsl.select(
                *coreCompensationFields,
                rank().over(
                    partitionBy(COMPENSATION_.CONTRACT_ID, COMPENSATION_.SCHEMA_ITEM_ID)
                        .orderBy(COMPENSATION_.END_DATE.desc()),
                ).`as`("rnk"),
            )
                .from(COMPENSATION_)
                .where(COMPENSATION_.CONTRACT_ID.`in`(filter.contractIds))
                .and(COMPENSATION_.CATEGORY.`in`(filter.categories))
                .and(COMPENSATION_.END_DATE.isNotNull)
                .and(COMPENSATION_.END_DATE.le(currentLocalDate()))
                .asTable("RankedCompensations")
        }

        verify(exactly = 1) {
            dsl.select(
                *rankedCompensationsFields,
                rnkField,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
        }
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return enriched compensation record when getEnrichedCompensationRecordById is called`() {
        val compensationId = UUID.randomUUID()
        val expectedEnrichedCompensation = mockk<CompensationEnriched>()
        val mockSelectConditionStep = mockk<SelectConditionStep<org.jooq.Record>>()
        val mockRecord = mockk<org.jooq.Record>()

        every {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(listOf(COMPENSATION_.ID.eq(compensationId)))
        } returns mockSelectConditionStep

        every { mockSelectConditionStep.fetchOne() } returns mockRecord
        every { mockRecord.map<CompensationEnriched>(any()) } returns expectedEnrichedCompensation

        val result = repository.getEnrichedCompensationRecordById(compensationId, transactionContext)

        assertEquals(expectedEnrichedCompensation, result)

        verify(exactly = 1) {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(listOf(COMPENSATION_.ID.eq(compensationId)))
        }
        verify(exactly = 1) { mockSelectConditionStep.fetchOne() }
        verify(exactly = 1) { mockRecord.map<CompensationEnriched>(any()) }
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return null when no enriched compensation record is found by id`() {
        val compensationId = UUID.randomUUID()
        val mockSelectConditionStep = mockk<SelectConditionStep<org.jooq.Record>>()

        every {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(listOf(COMPENSATION_.ID.eq(compensationId)))
        } returns mockSelectConditionStep

        every { mockSelectConditionStep.fetchOne() } returns null

        val result = repository.getEnrichedCompensationRecordById(compensationId, transactionContext)

        assertNull(result)

        verify(exactly = 1) {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(listOf(COMPENSATION_.ID.eq(compensationId)))
        }
        verify(exactly = 1) { mockSelectConditionStep.fetchOne() }
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should propagate exception when query fails in getEnrichedCompensationRecordById`() {
        val compensationId = UUID.randomUUID()
        val mockSelectConditionStep = mockk<SelectConditionStep<org.jooq.Record>>()
        val errorMessage = "Database error during enriched compensation record retrieval"

        every {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(listOf(COMPENSATION_.ID.eq(compensationId)))
        } returns mockSelectConditionStep

        every { mockSelectConditionStep.fetchOne() } throws RuntimeException(errorMessage)

        val exception = assertThrows<RuntimeException> {
            repository.getEnrichedCompensationRecordById(compensationId, transactionContext)
        }

        assertEquals(errorMessage, exception.message)

        verify(exactly = 1) {
            dsl.select(
                *coreCompensationFields,
                PAY_SCHEDULE.PAY_SCHEDULE_NAME,
                PAY_SCHEDULE.FREQUENCY,
                COMPENSATION_SCHEMA_ITEM.COMPONENT_NAME,
                COMPENSATION_SCHEMA_ITEM.IS_TAXABLE,
                COMPENSATION_SCHEMA_ITEM.IS_FIXED,
                COMPENSATION_SCHEMA_ITEM.IS_PRORATED,
                COMPENSATION_SCHEMA_ITEM.IS_MANDATORY,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_BASE_PAY,
                COMPENSATION_SCHEMA_ITEM.CATEGORY,
                COMPENSATION_SCHEMA_ITEM.LABEL,
                COMPENSATION_SCHEMA_ITEM.IS_OVERTIME_ELIGIBLE,
                COMPENSATION_SCHEMA_ITEM.IS_PART_OF_CTC,
            )
                .from(COMPENSATION_)
                .leftJoin(PAY_SCHEDULE).on(COMPENSATION_.PAY_SCHEDULE_ID.eq(PAY_SCHEDULE.ID))
                .leftJoin(COMPENSATION_SCHEMA_ITEM).on(COMPENSATION_.SCHEMA_ITEM_ID.eq(COMPENSATION_SCHEMA_ITEM.ID))
                .where(listOf(COMPENSATION_.ID.eq(compensationId)))
        }
        verify(exactly = 1) { mockSelectConditionStep.fetchOne() }
    }

    private fun mockedCompensation() = Compensation(
        id = UUID.randomUUID(),
        companyId = 1000L,
        entityId = 1L,
        contractId = 1L,
        schemaItemId = UUID.randomUUID(),
        category = "category",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 100.00,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = UUID.randomUUID(),
        startDate = LocalDate.now(),
        endDate = LocalDate.now(),
        isInstallment = false,
        noOfInstallments = null,
        status = CompensationStatus.NEW,
        generatedInstallments = null,
        processedUntilDate = LocalDate.now(),
        createdOn = LocalDateTime.now(),
        createdBy = -1,
        updatedOn = LocalDateTime.now(),
        updatedBy = -1,
    )

    private fun mockedCompensationRecord(compensation: Compensation) = CompensationRecord(
        id = compensation.id,
        companyId = compensation.companyId,
        entityId = compensation.entityId,
        contractId = compensation.contractId,
        schemaItemId = compensation.schemaItemId,
        category = compensation.category,
        currency = compensation.currency,
        billingRateType = compensation.billingRateType.toDatabase(),
        billingRate = compensation.billingRate,
        billingFrequency = compensation.billingFrequency.toDatabase(),
        payScheduleId = compensation.payScheduleId,
        startDate = compensation.startDate,
        endDate = compensation.endDate,
        isInstallment = compensation.isInstallment,
        noOfInstallments = compensation.noOfInstallments,
        previousId = compensation.previousId,
        status = compensation.status.toDatabase(),
        generatedInstallments = compensation.generatedInstallments,
        processedUntilDate = compensation.processedUntilDate,
        createdOn = compensation.createdOn,
        createdBy = compensation.createdBy,
        updatedOn = compensation.updatedOn,
        updatedBy = compensation.updatedBy,
        processingFrom = null,
        processingTo = null,
    )

    private val coreCompensationFields = arrayOf(
        COMPENSATION_.ID,
        COMPENSATION_.COMPANY_ID,
        COMPENSATION_.ENTITY_ID,
        COMPENSATION_.CONTRACT_ID,
        COMPENSATION_.SCHEMA_ITEM_ID,
        COMPENSATION_.CATEGORY,
        COMPENSATION_.CURRENCY,
        COMPENSATION_.BILLING_RATE_TYPE,
        COMPENSATION_.BILLING_RATE,
        COMPENSATION_.BILLING_FREQUENCY,
        COMPENSATION_.PAY_SCHEDULE_ID,
        COMPENSATION_.START_DATE,
        COMPENSATION_.END_DATE,
        COMPENSATION_.PROCESSING_FROM,
        COMPENSATION_.PROCESSING_TO,
        COMPENSATION_.IS_INSTALLMENT,
        COMPENSATION_.NO_OF_INSTALLMENTS,
        COMPENSATION_.PREVIOUS_ID,
        COMPENSATION_.STATUS,
        COMPENSATION_.GENERATED_INSTALLMENTS,
        COMPENSATION_.PROCESSED_UNTIL_DATE,
        COMPENSATION_.NOTES,
        COMPENSATION_.CREATED_ON,
        COMPENSATION_.CREATED_BY,
        COMPENSATION_.UPDATED_ON,
        COMPENSATION_.UPDATED_BY,
    )
}
