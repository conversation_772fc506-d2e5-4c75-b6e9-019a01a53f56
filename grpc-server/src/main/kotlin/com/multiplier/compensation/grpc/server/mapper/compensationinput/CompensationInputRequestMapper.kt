package com.multiplier.compensation.grpc.server.mapper.compensationinput

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.grpc.schema.CompensationInputStatus
import com.multiplier.compensation.grpc.schema.CompensationInputsFilter
import com.multiplier.compensation.grpc.schema.CompensationWatermarkingInputsFilter
import com.multiplier.compensation.grpc.server.utils.toLocalDateTimeOrNullIfDefault
import com.multiplier.grpc.common.v1.toDomain
import com.multiplier.compensation.domain.compensationinput.CompensationInputFilter as DomainCompensationInputFilter
import com.multiplier.compensation.domain.compensationinput.CompensationWatermarkingInputFilter as DomainCompensationWatermarkingInputFilter
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus as domainItemStatus
import com.multiplier.compensation.grpc.schema.CompensationInputStatus as gRPCInputStatus

fun CompensationInputsFilter.toDto() = DomainCompensationInputFilter(
    ids = this.idsList.map { it.toDomain() }.toSet(),
    includedCategories = this.includedCategoriesList.toSet(),
    excludedCategories = this.excludedCategoriesList.toSet(),
    updatedFromExclusive = this.updatedFromExclusive.toLocalDateTimeOrNullIfDefault(),
    updatedToInclusive = this.updatedToInclusive.toLocalDateTimeOrNullIfDefault(),
    contractIds = this.contractIdsList.toSet(),
    requestId = this.requestId,
    statuses = this.statusesList
        .map { it.toDomain() }
        .toSet(),
)

fun CompensationWatermarkingInputsFilter.toDto() = DomainCompensationWatermarkingInputFilter(
    ids = this.idsList.map { it.toDomain() }.toSet(),
    includedCategories = this.includedCategoriesList.toSet(),
    excludedCategories = this.excludedCategoriesList.toSet(),
    updatedFromExclusive = this.updatedFromExclusive.toLocalDateTimeOrNullIfDefault(),
    updatedToInclusive = this.updatedToInclusive.toLocalDateTimeOrNullIfDefault(),
)

fun CompensationInputStatus.toDomain() = when (this) {
    gRPCInputStatus.COMPENSATION_INPUT_STATUS_ONBOARDING_DRAFT ->
        domainItemStatus.ONBOARDING_DRAFT
    gRPCInputStatus.COMPENSATION_INPUT_STATUS_ACTIVATED ->
        domainItemStatus.ACTIVATED
    gRPCInputStatus.COMPENSATION_INPUT_STATUS_ABORTED ->
        domainItemStatus.ABORTED
    gRPCInputStatus.COMPENSATION_INPUT_STATUS_TERMINATED ->
        domainItemStatus.TERMINATED
    gRPCInputStatus.COMPENSATION_INPUT_STATUS_DELETED ->
        domainItemStatus.DELETED
    else -> throw InvalidArgumentException(
        errorCode = ValidationErrorCode.InvalidCompensationInputStatus,
        message = "Invalid CompensationInputStatus [$this]",
        context = mapOf("status" to this),
    )
}
