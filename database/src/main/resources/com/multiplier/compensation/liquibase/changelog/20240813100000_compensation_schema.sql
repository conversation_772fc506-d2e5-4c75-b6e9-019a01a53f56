-- liquibase formatted sql

-- changeset Jayendran S:20240803100000_compensation_schema

CREATE TABLE compensation.compensation_schema (
    id                                  UUID                                PRIMARY KEY NOT NULL,
    entity_id                           BIGINT                              NOT NULL,
    country                             compensation.country_code           NOT NULL,
    company_id                          BIGINT                              NOT NULL,
    is_default                          BOOLEAN                             NOT NULL,
    name                                VARCHAR(255)                        NOT NULL,
    is_active                           BOOLEAN                             NOT NULL,
    created_on                          TIMESTAMP                           NOT NULL,
    created_by                          BIGINT                              NOT NULL,
    updated_on                          TIMESTAMP                           NOT NULL,
    updated_by                          BIGINT                              NOT NULL
);

CREATE INDEX compensation_schema_entity_id_idx ON compensation.compensation_schema (entity_id);
