package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.extrapolation.CompensationFrequencyConverter
import com.multiplier.compensation.service.compensation.validation.validators.BillingRateInputValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationSchemaItemFixture
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class BillingRateInputValidatorTest {
    private lateinit var validator: BillingRateInputValidator
    private lateinit var context: CompensationValidatorContext
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private lateinit var input: ValidationInputItem
    private lateinit var draft: CompensationDraft
    private lateinit var compensationFrequencyConverter: CompensationFrequencyConverter
    private val schemaItemId = UUID.randomUUID()
    private val componentName = "Base_Salary"
    private val validationFormula = "$componentName > 1000"

    @BeforeEach
    fun setUp() {
        compensationFrequencyConverter = CompensationFrequencyConverter()
        validator = BillingRateInputValidator(compensationFrequencyConverter)
        context = mockk()
        collector = ValidationDataCollector(
            rowValidationResult = mutableMapOf(),
            drafts = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair("COMPONENT_NAME", componentName),
            ),
        )

        draft = CompensationDraft(
            companyId = 1L,
            employeeId = "EMP1",
            contractId = 50000L,
            schemaItemId = schemaItemId,
            schemaCategory = "SALARY",
            currency = "USD",
            billingRateType = null,
            billingRate = 2000.0,
            billingFrequency = BillingFrequency.MONTHLY,
            payScheduleId = null,
            startDate = null,
            endDate = null,
            isInstallment = false,
            noOfInstallments = null,
            reasonCode = null,
        )

        collector.drafts[input.id] = draft
    }

    @Test
    fun `should return false when draft is not found in collector`() {
        collector.drafts.clear()

        val result = validator.validate(input, context, collector)

        assertFalse(result)
    }

    @Test
    fun `should return false when draft schemaItemId is null`() {
        val draftWithNullSchemaItemId = draft.copy(schemaItemId = null)
        collector.drafts[input.id] = draftWithNullSchemaItemId

        every { context.getCompensationSchemaItem(schemaItemId) } returns null
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertFalse(result)
    }

    @Test
    fun `should return false when schema item is not found`() {
        every { context.getCompensationSchemaItem(schemaItemId) } returns null
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertFalse(result)
    }

    @Test
    fun `should return true when schema item is not INPUT type`() {
        val schemaItem = createMockSchemaItem(itemType = ItemType.CALCULATED)
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should return true when schema item has no validation formula`() {
        val schemaItem = createMockSchemaItem(validation = null)
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should return true when validation formula evaluates to non-zero value`() {
        val schemaItem = createMockSchemaItem()
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertTrue(result)
        assertTrue(collector.rowValidationResult.isEmpty())
    }

    @Test
    fun `should return false and add error when validation formula evaluates to zero`() {
        val schemaItem = createMockSchemaItem()
        val draftWithFailedValidation = draft.copy(billingRate = 50.0)
        collector.drafts[input.id] = draftWithFailedValidation

        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )

        val result = validator.validate(input, context, collector)

        assertFalse(result)

        val validationResults = collector.rowValidationResult[input.id]
        assertEquals(1, validationResults?.size)

        val validationResult = validationResults?.first()
        assertEquals(CompensationSkeletonField.COMPONENT_NAME.id, validationResult?.field?.key)
        assertEquals(ValidationResultType.ERROR, validationResult?.type)
        assertEquals(
            "The billing rate value for $componentName does not meet the required criteria.",
            validationResult?.message,
        )
    }

    @Test
    fun `should return false and add error when validation formula throws exception`() {
        val schemaItem = createMockSchemaItem(componentName = "Base Salary")
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertFalse(result)

        val validationResults = collector.rowValidationResult[input.id]
        assertEquals(1, validationResults?.size)

        val validationResult = validationResults?.first()
        assertEquals(CompensationSkeletonField.COMPONENT_NAME.id, validationResult?.field?.key)
        assertEquals(ValidationResultType.ERROR, validationResult?.type)
        assertEquals(
            "Invalid validation expression for Base Salary: Failed to evaluate formula [Base_Salary > 1000]",
            validationResult?.message,
        )
    }

    @Test
    fun `should use component name from schema item in validation formula`() {
        val customComponentName = "Custom_Component"
        val customValidationFormula = "$customComponentName >= 500"

        val schemaItem = createMockSchemaItem(
            componentName = customComponentName,
            validation = customValidationFormula,
        )
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should handle complex validation formula`() {
        val complexFormula = "($componentName > 1000) && ($componentName < 100000)"
        val schemaItem = createMockSchemaItem(validation = complexFormula)
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should use converted draft for validation`() {
        val originalDraft = draft.copy(billingRate = 10000.0)
        collector.drafts[input.id] = originalDraft

        val schemaItem = createMockSchemaItem()
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )

        val result = validator.validate(input, context, collector)

        assertTrue(result)
    }

    @Test
    fun `should validate using converted billing rate in formula`() {
        val originalDraft = draft.copy(billingRate = 10000.0)
        collector.drafts[input.id] = originalDraft

        val schemaItem = createMockSchemaItem()
        every { context.getCompensationSchemaItem(schemaItemId) } returns schemaItem
        every { context.getCompensationSchemaItems() } returns mapOf(
            componentName to compensationSchemaItemFixture(),
        )
        val result = validator.validate(input, context, collector)

        assertTrue(result)
    }

    private fun createMockSchemaItem(
        componentName: String = this.componentName,
        itemType: ItemType = ItemType.INPUT,
        validation: String? = validationFormula,
    ): CompensationSchemaItem = mockk {
        every { <EMAIL> } returns componentName
        every { <EMAIL> } returns itemType
        every { <EMAIL> } returns validation
    }
}
