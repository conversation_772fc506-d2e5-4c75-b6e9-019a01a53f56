import requests
import json
import os
import time
import base64
import pandas as pd
import sys
from openpyxl import load_workbook
from typing import Optional
import shutil

# URLs - Change according to the env
AUTH_URL = "https://api.usemultiplier.com/authenticate"
GRAPHQL_URL = "https://api.usemultiplier.com/graphql"

# Credentials
USERNAME = ""
USER_ID = ""
PASSWORD = ""

# Log colors
CEND = '\033[0m'
CSELECTED = '\33[7m'
CURL = '\33[4m'
CRED2 = '\33[91m'
CGREEN2 = '\33[92m'
CYELLOW2 = '\33[93m'

REQUIRED_FILES = ["COMPENSATION_SCHEMA", "PAY_SCHEDULE"]
UPLOAD_ORDER = ["COMPENSATION_SCHEMA", "PAY_SCHEDULE", "COMPENSATION_BACKFILL", "COMPENSATION_REVISION_BACKFILL", "SALARY_REVISION"]

ID_TOKEN=""

configData = {}

# Status tracking variables
status_tracker = {}
STATUS_FILE = "upload_status.xlsx"

def initialize_status_tracker():
    """Initialize or load existing status tracker Excel file"""
    global status_tracker

    if os.path.exists(STATUS_FILE):
        try:
            df = pd.read_excel(STATUS_FILE)
            # Convert existing data to dictionary for easy manipulation
            for _, row in df.iterrows():
                folder_name = row['FOLDER']
                status_tracker[folder_name] = {
                    'COUNTRY': row.get('COUNTRY', ''),
                    'COMPENSATION_BACKFILL': row.get('COMPENSATION_BACKFILL', 'NO'),
                    'COMPENSATION_REVISION_BACKFILL': row.get('COMPENSATION_REVISION_BACKFILL', 'NO'),
                    'FINAL_STATUS': row.get('FINAL_STATUS', 'FAILURE')
                }
            print(f"Loaded existing status tracker with {len(status_tracker)} entries")
        except Exception as e:
            print(f"Error loading existing status file: {e}")
            status_tracker = {}
    else:
        status_tracker = {}
        print("Created new status tracker")

def update_status_tracker(folder_name, use_case, success, country=''):
    """Update the status tracker for a specific folder and use case"""
    if folder_name not in status_tracker:
        status_tracker[folder_name] = {
            'COUNTRY': country,
            'COMPENSATION_BACKFILL': 'NO',
            'COMPENSATION_REVISION_BACKFILL': 'NO',
            'FINAL_STATUS': 'FAILURE'
        }
    elif country and not status_tracker[folder_name]['COUNTRY']:
        # Update country if it wasn't set before
        status_tracker[folder_name]['COUNTRY'] = country

    if use_case in ['COMPENSATION_BACKFILL', 'COMPENSATION_REVISION_BACKFILL']:
        status_tracker[folder_name][use_case] = 'YES' if success else 'NO'

    # Update final status based on both backfill statuses
    comp_backfill = status_tracker[folder_name]['COMPENSATION_BACKFILL']
    comp_revision_backfill = status_tracker[folder_name]['COMPENSATION_REVISION_BACKFILL']

    # Final status is SUCCESS only if both backfills are YES
    if comp_backfill == 'YES' and comp_revision_backfill == 'YES':
        status_tracker[folder_name]['FINAL_STATUS'] = 'SUCCESS'
    else:
        status_tracker[folder_name]['FINAL_STATUS'] = 'FAILURE'

def save_status_tracker():
    """Save the status tracker to Excel file"""
    try:
        data = []
        for folder_name, status in status_tracker.items():
            data.append({
                'FOLDER': folder_name,
                'COUNTRY': status['COUNTRY'],
                'COMPENSATION_BACKFILL': status['COMPENSATION_BACKFILL'],
                'COMPENSATION_REVISION_BACKFILL': status['COMPENSATION_REVISION_BACKFILL'],
                'FINAL_STATUS': status['FINAL_STATUS']
            })

        df = pd.DataFrame(data)
        df.to_excel(STATUS_FILE, index=False)
        print(f"Status tracker saved to {STATUS_FILE}")
    except Exception as e:
        print(f"Error saving status tracker: {e}")

def move_folder_based_on_status(folder_path, folder_name):
    """Move folder based on its status - success or partial success, maintaining country structure"""
    try:
        if folder_name not in status_tracker:
            return False

        # Get the path to the base directory (parent of sheets folder)
        sheets_dir = os.path.dirname(os.path.dirname(folder_path))
        base_dir = os.path.dirname(sheets_dir)

        # Get country folder name from the path
        country_folder = os.path.basename(os.path.dirname(folder_path))

        # Check if it's a complete success
        if status_tracker[folder_name]['FINAL_STATUS'] == 'SUCCESS':
            # Create migrated_entities/COUNTRY directory
            migrated_dir = os.path.join(base_dir, "migrated_entities", country_folder)
            os.makedirs(migrated_dir, exist_ok=True)

            # Move the folder
            destination = os.path.join(migrated_dir, folder_name)
            shutil.move(folder_path, destination)
            print(CGREEN2 + f"Successfully moved folder to migrated_entities/{country_folder}: {folder_name}" + CEND)

        else:
            # Create failed_entities_to_be_fixed/COUNTRY directory
            failed_dir = os.path.join(base_dir, "failed_entities_to_be_fixed", country_folder)
            os.makedirs(failed_dir, exist_ok=True)

            # Move the folder
            destination = os.path.join(failed_dir, folder_name)
            shutil.move(folder_path, destination)
            print(CYELLOW2 + f"Moved unsuccessful folder to failed_entities_to_be_fixed: {folder_name}" + CEND)

    except Exception as e:
        print(CRED2 + f"Error moving folder {folder_name}: {e}" + CEND)

def parse_text_file_to_json(file_path):
    data = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if "=" in line:  # Assuming key-value pairs are separated by '='
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip()
                    # Convert comma-separated values into lists
                    if ',' in value:
                        value = [v.strip() for v in value.split(',')]

                    data[key] = value
    except FileNotFoundError as e:
        print(f"Error: The file {file_path} was not found {e}")
        return {}
    except Exception as e:
        print("An error occured : {e}")
        return {}
    return data

def get_id_token(username: str, password: str) -> str:
    global ID_TOKEN
    headers = {"Content-Type": "application/json"}
    payload = {"username": username, "password": password}

    try:
        print("\nAuthenticating...")
        response = requests.post(AUTH_URL, json=payload, headers=headers)
        response.raise_for_status()
        ID_TOKEN = response.json().get("id_token")
        if ID_TOKEN:
            print("Authentication successful.")
            return ID_TOKEN
        else:
            print("id_token not found in response.")
            return "id_token not found in response"
    except requests.exceptions.RequestException as e:
        print(f"Authentication failed: {e}")
        return f"Request failed: {e}"

def call_graphql(query: str, variables: dict) -> dict:
    if not ID_TOKEN:
        raise ValueError("ID_TOKEN is not set. Authenticate first.")

    headers = {
        "Authorization": f"Bearer {ID_TOKEN}",
        "Content-Type": "application/json",
        "user-id": USER_ID,
        "experience": "Operations"
    }
    # print(f"Calling GraphQL API with query: {query[:50]}...")
    response = requests.post(GRAPHQL_URL, headers=headers, json={"query": query, "variables": variables})
    response.raise_for_status()
    print("GraphQL API call successful.")
    return response.json()

def format_excel_columns(file_path: str):
    wb = load_workbook(file_path)
    for sheet in wb.sheetnames:
        ws = wb[sheet]
        for col in ws.iter_cols():
            column_header = col[0].value
            if column_header and str(column_header).lower() in ["employeeid", "contract_id"]:
                print(CYELLOW2 + f"{column_header} header found. Formating data types for the values..." + CEND)
                for cell in col[3:]:  # Skip first three rows as they are headers
                    # print(f"cell - {cell.value}")
                    if cell.value is None or cell.value == "None" or cell.value == "":
                        print(f"cell {cell} - {cell.value}")
                        break
                    # cell.value = str(int(cell.value))  # Remove decimal places
                    cell.value = str(cell.value)

    wb.save(file_path)

def excel_to_json(file_path: str, entity_id: str, company_id: str, use_case: str):
    # Read the Excel file
    df = pd.read_excel(file_path,header=None)

    # Replace NaN values with empty string
    df = df.fillna('')

    # Extract headers from the first row (excluding the first column)
    headers = df.iloc[0, 1:].tolist()

    # Extract data starting from the fourth row (excluding the first column)
    data = df.iloc[3:, 1:].values.tolist()

    # Convert each row into an array of key-value pairs
    json_data = []
    for row in data:
        # Only include key-value pairs where value is not empty
        row_data = [{"key": key, "value": value} for key, value in zip(headers, row) if value]
        if use_case == 'COMPENSATION_SCHEMA' or use_case == 'PAY_SCHEDULE':
            row_data.append({"key": "ENTITY_ID", "value": "-1"})
            row_data.append({"key": "COMPANY_ID", "value" : "-1"})
        else:
            row_data.append({"key": "ENTITY_ID", "value": entity_id})
            row_data.append({"key": "COMPANY_ID", "value" : company_id})
        json_data.append(row_data)

    KeyValuePairs = json_data
    bulkUpsertInput = []

    for idx in range(len(KeyValuePairs)):
        entry = {
            "id": str(idx+1),
            "keyValueInputs": KeyValuePairs[idx],
        }
        bulkUpsertInput.append(entry)
    return bulkUpsertInput

def read_and_upload_file(file_path: str, company_id: str, entity_id: str, use_case: str, folder_name: str = None, country_code: str = '') -> bool:
    print("\n" + CURL + f"Use case: {use_case}" + CEND)
    format_excel_columns(file_path)
    bulkUpsertInput = excel_to_json(file_path, entity_id, company_id, use_case)
    # print(bulkUpsertInput)
    commit_partially = False

    if use_case == "COMPENSATION_SCHEMA":
        # Implement batching - process 50 records at a time
        batch_size = 50
        total_batches = (len(bulkUpsertInput) + batch_size - 1) // batch_size  # Ceiling division
        overall_success = True

        print(f"Processing {len(bulkUpsertInput)} records in {total_batches} batches of {batch_size}")

        for i in range(0, len(bulkUpsertInput), batch_size):
            batch = bulkUpsertInput[i:i+batch_size]
            print(f"Processing batch {(i//batch_size)+1}/{total_batches} with {len(batch)} records")

            variables = {
                "bulkUpsertRequest": {
                    "entityId": "-1",
                    "useCase": use_case,
                    "commitPartially": commit_partially,
                    "bulkUpsertInput": batch,
                    "customParams": []
                }
            }

            upsert_query = f"""
                mutation upsertCompensationSchema($bulkUpsertRequest: CompensationDomainBulkUpsertRequest!) {{ upsertCompensationSchema(bulkUpsertRequest: $bulkUpsertRequest) {{ status recordValidationResults {{ id componentValidationResults {{ type message componentDetails {{ key type value }}}}}} }}}}
                """
            response = call_graphql(upsert_query, variables)
            print(f"response : {response}")
            print(f"Batch {(i//batch_size)+1} response status: {response['data']['upsertCompensationSchema']['status']}")

            status = response["data"]["upsertCompensationSchema"]["status"]
            if status != "SUCCESS":
                overall_success = False
                # Continue processing other batches even if one fails

        if overall_success:
            print(CGREEN2 + "All batches processed successfully" + CEND + f" Company: {company_id}, Entity: {entity_id}, Use Case: {use_case}")
            return True
        else:
            print(CRED2 + "Some batches failed" + CEND + f" Company: {company_id}, Entity: {entity_id}, Use Case: {use_case}")
            return False
    elif use_case == "PAY_SCHEDULE":
        upsert_query = f"""
            mutation upsertPaySchedule($bulkUpsertRequest: CompensationDomainBulkUpsertRequest!) {{ upsertPaySchedule(bulkUpsertRequest: $bulkUpsertRequest) {{ status recordValidationResults {{ id componentValidationResults {{ type message componentDetails {{ key type value }}}}}} }} }}
            """

        variables = {
            "bulkUpsertRequest": {
                "entityId": "-1",
                "useCase": use_case,
                "commitPartially": commit_partially,
                "bulkUpsertInput": bulkUpsertInput,
                "customParams": []
            }
        }

        response = call_graphql(upsert_query, variables)
        print(f"response : {response}")
        status = response["data"]["upsertPaySchedule"]["status"]
        print(f"Status: {status}")

        if status == "SUCCESS":
            print(CGREEN2 + status + CEND + f" Company: {company_id}, Entity: {entity_id}, Use Case: {use_case}")
            return True
        elif status == "PARTIAL_SUCCESS":
            print(CRED2 + status + CEND + f" Company: {company_id}, Entity: {entity_id}, Use Case: {use_case}")
            return False
        else:
            print(CRED2 + status + CEND + f" Company: {company_id}, Entity: {entity_id}, Use Case: {use_case}")
            return False
    else:
        group = "SALARY_REVISION" if use_case == "SALARY_REVISION" else ("BACKFILL" if "BACKFILL" in use_case else "PAYROLL")
        data = {
            "OFFERING_TYPE": configData["OFFERING_TYPE"],
            "SCHEMA_NAME": configData["SCHEMA_NAME"],
            "COUNTRY_CODE": configData["COUNTRY_CODE"],
        }
        json_data = json.dumps(data)
        try:
            with open(file_path, "rb") as file:
                files = {"1": (os.path.basename(file_path), file, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
                payload = {
                    "operations": json.dumps({
                        "operationName": "CreateBulkUploadJob",
                        "query": """
                        mutation CreateBulkUploadJob($options: BulkUploadOptions!, $file: Upload!) {
                            bulkUploadJobCreate(options: $options, file: $file) {
                                id groupName moduleNames companyId status
                            }
                        }""",
                        "variables": {
                            "options": {
                                "groupName": group,
                                "moduleNames": [use_case],
                                "companyId": company_id,
                                "entityId": entity_id,
                                "moduleCustomParams": [
                                    {
                                        "moduleName": use_case,
                                        "jsonCustomParams": json_data,
                                    }
                                ],
                            }
                        }
                    }),
                    "map": json.dumps({"1": ["variables.file"]})
                }
                headers = {
                    "Authorization": f"Bearer {ID_TOKEN}",
                    "user-id": USER_ID
                }
                print(f"Uploading file: {file_path}")
                response = requests.post(GRAPHQL_URL, headers=headers, files=files, data=payload)
                response.raise_for_status()
                response_data = response.json()
                print(f"Response: {response_data}")

                # Check for empty file error
                if "errors" in response_data:
                    for error in response_data.get("errors", []):
                        error_message = error.get("message", "")
                        error_code = error.get("extensions", {}).get("errorCode", "")

                        if "Input file is empty" in error_message or error_code == "EMPTY_JOB_DATA":
                            print(CYELLOW2 + f"[WARNING]: Empty file detected for {use_case}. Skipping this file and continuing." + CEND)
                            # Update status tracker for empty files as successful
                            if folder_name and use_case in ['COMPENSATION_BACKFILL', 'COMPENSATION_REVISION_BACKFILL']:
                                update_status_tracker(folder_name, use_case, True)
                            return True  # Return True to continue with other files

                    # If we get here, there were errors but not the empty file error
                    print(CRED2 + "File upload failed with errors:" + CEND)
                    print(response_data.get("errors"))
                    # Update status tracker for failed uploads
                    if folder_name and use_case in ['COMPENSATION_BACKFILL', 'COMPENSATION_REVISION_BACKFILL']:
                        update_status_tracker(folder_name, use_case, False)
                    return False

                job_id = response_data["data"]["bulkUploadJobCreate"]["id"]
                print(CGREEN2 + f"File uploaded. Job created: {job_id}" + CEND)
        except Exception as e:
            print(CRED2 + "File upload failed" + CEND + f"\n Error: {str(e)}")
            # Update status tracker for failed uploads
            if folder_name and use_case in ['COMPENSATION_BACKFILL', 'COMPENSATION_REVISION_BACKFILL']:
                update_status_tracker(folder_name, use_case, False, country_code)
                save_status_tracker()
                # Get the folder path from the file path
                folder_dir = os.path.dirname(file_path)
                move_folder_based_on_status(folder_dir, folder_name)
                sys.exit(1)  # Exit with error code
            return False

        # Validate and commit the job, then update status tracker
        success = validate_and_commit_job(job_id, company_id, entity_id, use_case)
        if folder_name and use_case in ['COMPENSATION_BACKFILL', 'COMPENSATION_REVISION_BACKFILL']:
            update_status_tracker(folder_name, use_case, success, country_code)
        return success

def validate_and_commit_job(job_id: str, company_id: str, entity_id: str, use_case: str) -> bool:
    print(f"Validating job {job_id}...")
    validation_query = f"""
    mutation ValidateJob {{ bulkUploadJobValidate(jobId: "{job_id}") {{ id status }} }}
    """
    validation_response = call_graphql(validation_query, {})
    status = validation_response["data"]["bulkUploadJobValidate"]["status"]
    print(f"Validation status: {status}")

    if status == "VALIDATION_IN_PROGRESS":
        job_status = status
        retries = 1
        while (job_status == status) & (retries <= 3):
            print(CGREEN2 + status + CEND +f" Waiting for validation to complete ({retries})...")
            time.sleep(5)
            get_job_query = f"""
            query GetJob {{
                company(id: "{company_id}") {{
                    bulkUploadJob(jobId: "{job_id}") {{
                        id status groupName moduleNames validationSummary {{
                            validationSummaryChunks {{ name identifiedRowCount failedRowCount }}
                        }}
                    }}
                }}
            }}"""
            job_response = call_graphql(get_job_query, {})
            job_status = job_response["data"]["company"]["bulkUploadJob"]["status"]
            retries += 1
        print(f"Job [{job_id}] status after validation: {job_status}")

        if job_status == "VALIDATION_SUCCESS":
            commit_job(job_id, company_id, entity_id, use_case, get_job_query)
            return True
        else:
            download_error_report(job_id, company_id, entity_id, job_status)
            return False
    else:
        print(CRED2 + "Validation could not start" + status + CEND)
        return False

def download_error_report(job_id: str, company_id: str, entity_id: str, job_status: str):
    print(CRED2 + job_status + CEND + " Downloading error report...")
    download_query = f"""
            query DownloadReport {{ bulkUploadValidationReport(jobId: "{job_id}", reportType: FULL_VALIDATION_REPORT) {{ blob }} }}
            """
    download_response = call_graphql(download_query, {})
    blob_content = download_response["data"]["bulkUploadValidationReport"]["blob"]
    error_dir = os.path.join(os.getcwd(), "errors")
    os.makedirs(error_dir, exist_ok=True)
    file_name = os.path.join(error_dir, f"errors-{job_id}-{company_id}-{entity_id}.xlsx")
    with open(file_name, "wb") as file:
        file.write(base64.b64decode(blob_content))
    print(CYELLOW2 + f"Error file saved: {file_name}" + CEND)

def commit_job(job_id: str, company_id: str, entity_id: str, use_case: str, get_job_query: str):
    print(f"Committing job [{job_id}]...")
    commit_query = f"""
            mutation CommitJob {{ bulkUploadJobCommit(jobId: "{job_id}") {{ id status }} }}
            """
    commit_response = call_graphql(commit_query, {})
    commit_status = commit_response["data"]["bulkUploadJobCommit"]["status"]
    print(CGREEN2 + commit_status + CEND + f" Job ID: {job_id} - Company: {company_id}, Entity: {entity_id}, Use Case: {use_case}")

    final_status = commit_status
    retries = 1
    while (final_status != "DATA_CREATION_SUCCESS") & (retries <= 3):
        print(f"Waiting for job [{job_id}] to complete the commit ({retries})...")
        time.sleep(5)
        final_response = call_graphql(get_job_query, {})
        final_status = final_response["data"]["company"]["bulkUploadJob"]["status"]
        retries += 1
    commit_color = CGREEN2 if final_status == "DATA_CREATION_SUCCESS" else CRED2
    print(commit_color + final_status + CEND + f" Job ID: {job_id} - Company: {company_id}, Entity: {entity_id}, Use Case: {use_case}")

files={}
def process_files_in_folder(folder_path: str, company_id: str, entity_id: str, country_code: str, folder_name: str) -> bool:
    for f in os.listdir(folder_path):
        if f.endswith(".xlsx"):
            use_case_with_country, entity_id_ext = os.path.splitext(f)[0].rsplit("_", 1)
            use_case = use_case_with_country.replace(f"_{country_code}", "")
            if entity_id_ext == entity_id:
                files[use_case] = os.path.join(folder_path, f)

    if not all(req in files for req in REQUIRED_FILES):
        print(CYELLOW2 + f"Skipping {folder_path} due to missing required files." + CEND)
        return False

    # Initialize folder in status tracker if not exists
    if folder_name not in status_tracker:
        update_status_tracker(folder_name, 'COMPENSATION_BACKFILL', False, country_code)

    folder_success = True
    for use_case in UPLOAD_ORDER:
        if use_case in files and use_case not in ["COMPENSATION_SCHEMA", "PAY_SCHEDULE"]:
            success = read_and_upload_file(files[use_case], company_id, entity_id, use_case, folder_name, country_code)
            if not success:
                print(CYELLOW2 + f"Skipping remaining files due to validation failure. Company ID: {company_id} Entity Id: {entity_id}" + CEND)
                folder_success = False
                break

    # Save status after processing this folder
    save_status_tracker()

    move_folder_based_on_status(folder_path, folder_name)

    return folder_success

def process_all_files(country_path: str) -> bool:
    for file in os.listdir(country_path):
        file_path = os.path.join(country_path, file)
        if os.path.isfile(file_path):
            if file.endswith(".txt") and file.startswith("CUSTOM_PARAMS_"):
                output = parse_text_file_to_json(file_path)
                if output:
                    configData.update(output)
            if file.endswith(".xlsx") and (file.startswith("COMPENSATION_SCHEMA_") or file.startswith("PAY_SCHEDULE_")):
                use_case, country_code = os.path.splitext(file)[0].rsplit("_", 1)
                files[use_case] = file_path

    if not all(req in files for req in REQUIRED_FILES):
        print(CYELLOW2 + f"Skipping {country_path} due to missing required files." + CEND)
        return False

    for use_case in UPLOAD_ORDER:
        if use_case in files:
            success = read_and_upload_file(files[use_case], "-1", "-1", use_case)
            if not success:
                print(CYELLOW2 + f"Skipping remaining files due to validation failure. Company ID: -1 Entity Id: -1" + CEND)
                return False

    for folder in os.listdir(country_path):
        folder_path = os.path.join(country_path, folder)
        if os.path.isdir(folder_path):
            parts = folder.split("_")
            entity_id = parts[-5]
            company_id = parts[-3]
            country_code = parts[-1]
            print("\n\n" + CSELECTED + f"CompanyId: {company_id} EntityId: {entity_id} CountryCode: {country_code}" + CEND)
            folder_success = process_files_in_folder(folder_path, company_id, entity_id, country_code, folder)

            if not folder_success:
                print(CRED2 + f"Failed to process folder: {folder}" + CEND)
                # Continue processing other folders instead of returning False

            # Clean up files map to keep only COMPENSATION_SCHEMA and PAY_SCHEDULE
            keys_to_remove = [key for key in files.keys() if key not in ["COMPENSATION_SCHEMA", "PAY_SCHEDULE"]]
            for key in keys_to_remove:
                files.pop(key, None)

    files.clear()
    return True  # Return True to continue processing other countries

def process_country_folder():
    base_path = os.path.join(os.getcwd(), "sheets")
    print("\nProcessing all country folders...")

    for countryFolder in os.listdir(base_path):
        country_path = os.path.join(base_path, countryFolder)
        if os.path.isdir(country_path):
            print("\n\n" + CSELECTED + f"Country: {countryFolder}" + CEND)
            success = process_all_files(country_path)
            if not success:
                print(CRED2 + f"Stopping further processing due to failure in {countryFolder}" + CEND)
                return
            print(CGREEN2 + f"Processing of {countryFolder} completed successfully" + CEND)

    # Final save of status tracker
    save_status_tracker()
    print(f"\nFinal status report saved to {STATUS_FILE}")

if __name__ == "__main__":
    # Initialize status tracking
    initialize_status_tracker()

    username = USERNAME #input("Enter username: ")
    password = PASSWORD #input("Enter password: ")
    id_token = get_id_token(username, password)
    # Uncomment the below line to manually pass the token, in case authentication API does not work or returns incorrect token.
    # id_token = ID_TOKEN
    # print(f"id_token: {id_token}")
    process_country_folder()
