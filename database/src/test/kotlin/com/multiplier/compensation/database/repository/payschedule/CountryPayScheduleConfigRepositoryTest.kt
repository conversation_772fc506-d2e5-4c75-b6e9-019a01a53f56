package com.multiplier.compensation.database.repository.payschedule

import com.multiplier.compensation.database.mapper.common.toDatabase
import com.multiplier.compensation.database.tables.references.COUNTRY_PAY_SCHEDULE_CONFIG
import com.multiplier.compensation.domain.common.CountryCode
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.jooq.DSLContext
import org.jooq.Record1
import org.jooq.Result
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertTrue

@ExtendWith(MockKExtension::class)
class CountryPayScheduleConfigRepositoryTest {
    private lateinit var dsl: DSLContext
    private lateinit var repository: CountryPayScheduleConfigRepository

    @BeforeEach
    fun setUp() {
        dsl = mockk()
        repository = CountryPayScheduleConfigRepository(dsl)
    }

    @Test
    fun `should return all state codes for a given country`() {
        // Given
        val country = CountryCode.USA
        val expectedStateCodes = listOf("CA", "NY", "TX")

        val mockResult = mockk<Result<Record1<String?>>>()
        val mockIterator = mockk<MutableIterator<Record1<String?>>>()
        val mockRecords = expectedStateCodes.map { stateCode ->
            mockk<Record1<String?>> {
                every { get(COUNTRY_PAY_SCHEDULE_CONFIG.STATE_CODE) } returns stateCode
            }
        }

        every { mockIterator.hasNext() } returnsMany List(mockRecords.size) { true } andThen false
        every { mockIterator.next() } returnsMany mockRecords
        every { mockResult.iterator() } returns mockIterator

        every {
            dsl.selectDistinct(COUNTRY_PAY_SCHEDULE_CONFIG.STATE_CODE)
                .from(COUNTRY_PAY_SCHEDULE_CONFIG)
                .where(COUNTRY_PAY_SCHEDULE_CONFIG.COUNTRY_CODE.eq(country.toDatabase()))
                .fetch()
        } returns mockResult

        // When
        val result = repository.getStateCodesForCountry(country)

        // Then
        assertTrue { result.size == 3 }
        assertTrue { result.containsAll(expectedStateCodes) }
        verify {
            dsl.selectDistinct(COUNTRY_PAY_SCHEDULE_CONFIG.STATE_CODE)
                .from(COUNTRY_PAY_SCHEDULE_CONFIG)
                .where(COUNTRY_PAY_SCHEDULE_CONFIG.COUNTRY_CODE.eq(country.toDatabase()))
                .fetch()
        }
    }
}
