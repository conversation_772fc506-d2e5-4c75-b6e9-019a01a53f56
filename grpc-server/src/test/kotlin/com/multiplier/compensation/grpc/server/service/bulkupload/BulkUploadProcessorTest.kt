package com.multiplier.compensation.grpc.server.service.bulkupload

import assertk.all
import assertk.assertThat
import assertk.assertions.containsExactly
import assertk.assertions.containsExactlyInAnyOrder
import assertk.assertions.isEmpty
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import com.fasterxml.uuid.Generators
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensationinput.CompensationInputFilter
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.SkeletonData
import com.multiplier.compensation.domain.skeleton.enums.SkeletonKey
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.grpc.server.service.common.compensationInputEnrichedFixture
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.utils.toYesNo
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.RowValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.BulkCompensationBackFillService
import com.multiplier.compensation.service.compensation.BulkCompensationCreationService
import com.multiplier.compensation.service.compensation.BulkCompensationRevisionBackFillService
import com.multiplier.compensation.service.compensation.BulkCompensationRevisionService
import com.multiplier.compensation.service.compensation.CompensationValidationService
import com.multiplier.compensation.service.compensation.dto.CompensationBulkCreateResponse
import com.multiplier.compensation.service.compensation.dto.CompensationRecordField
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensationinput.CompensationInputService
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.compensationschema.CompensationSchemaValidationService
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaBulkUpsertResponse
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import com.multiplier.compensation.service.payschedule.BulkPayScheduleCreationService
import com.multiplier.compensation.service.payschedule.dto.PayScheduleBulkCreationOutput
import com.multiplier.compensation.service.payschedule.dto.PayScheduleDraft
import com.multiplier.compensation.service.payschedule.dto.PayScheduleSkeletonField
import com.multiplier.compensation.service.payschedule.validation.PayScheduleValidationService
import com.multiplier.compensation.service.skeleton.SkeletonService
import com.multiplier.grpc.common.bulkupload.v1.FieldType
import com.multiplier.grpc.common.bulkupload.v1.bulkDataRequest
import com.multiplier.grpc.common.bulkupload.v1.fieldRequirement
import com.multiplier.grpc.common.bulkupload.v1.fieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.platformKeys
import com.multiplier.grpc.common.bulkupload.v1.upsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.upsertRequest
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputRequest
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.util.UUID
import kotlin.test.assertFailsWith

@ExtendWith(MockKExtension::class)
class BulkUploadProcessorTest {
    private lateinit var compensationInputService: CompensationInputService
    private lateinit var bulkCompensationCreationService: BulkCompensationCreationService
    private lateinit var bulkCompensationRevisionService: BulkCompensationRevisionService
    private lateinit var bulkCompensationRevisionBackFillService: BulkCompensationRevisionBackFillService
    private lateinit var bulkUploadProcessor: BulkUploadProcessor
    private lateinit var compensationSchemaService: CompensationSchemaService
    private lateinit var compensationSchemaValidationService: CompensationSchemaValidationService
    private lateinit var compensationValidationService: CompensationValidationService
    private lateinit var payScheduleCreationService: BulkPayScheduleCreationService
    private lateinit var payScheduleValidationService: PayScheduleValidationService
    private lateinit var compensationBackFillService: BulkCompensationBackFillService
    private lateinit var skeletonService: SkeletonService
    private val timeBasedEpochGenerator = Generators.timeBasedEpochGenerator()!!

    @BeforeEach
    fun setUp() {
        bulkCompensationCreationService = mockk()
        bulkCompensationRevisionService = mockk()
        bulkCompensationRevisionBackFillService = mockk()
        compensationInputService = mockk()
        compensationSchemaService = mockk()
        compensationSchemaValidationService = mockk()
        compensationValidationService = mockk()
        payScheduleCreationService = mockk()
        payScheduleValidationService = mockk()
        skeletonService = mockk()
        compensationBackFillService = mockk()

        bulkUploadProcessor = BulkUploadProcessor(
            bulkCompensationCreationService,
            bulkCompensationRevisionService,
            bulkCompensationRevisionBackFillService,
            compensationInputService,
            compensationSchemaService,
            compensationSchemaValidationService,
            compensationValidationService,
            payScheduleCreationService,
            payScheduleValidationService,
            skeletonService,
            timeBasedEpochGenerator,
            compensationBackFillService,
        )

        every { skeletonService.getSkeleton(any(), SkeletonType.PAY_SCHEDULE, emptyMap()) } returns Skeleton(
            keys = SkeletonKey.entries,
            data = listOf(
                SkeletonData(
                    fieldName = "Pay schedule name",
                    fieldId = PayScheduleSkeletonField.PAY_SCHEDULE_NAME.id,
                    description = "Pay schedule name to uniquely identify it in the system",
                    valueType = ValueType.STRING,
                    mandatory = true,
                    possibleValues = null,
                    defaultValue = null,
                    validationRegex = null,
                ),
                SkeletonData(
                    fieldName = "Pay Schedule Frequency",
                    fieldId = PayScheduleSkeletonField.PAY_SCHEDULE_FREQUENCY.id,
                    description = "Frequency of the pay schedule.",
                    valueType = ValueType.STRING,
                    mandatory = true,
                    possibleValues = null,
                    defaultValue = null,
                    validationRegex = null,
                ),
            ),
        )
    }

    @Test
    fun `test get requirements when use case is correctly passed`() {
        val entityId = 1L
        val useCase = "PAY_SCHEDULE"

        val request = fieldRequirementsRequest {
            this.entityId = entityId
            this.useCase = useCase
        }
        val response = bulkUploadProcessor.getRequirements(request)

        assertThat(response).isNotNull()

        assertThat(response.fieldRequirementsList).containsExactly(
            fieldRequirement {
                this.key = "PAY_SCHEDULE_NAME"
                this.label = "Pay schedule name"
                this.type = FieldType.FIELD_TYPE_TEXT
                this.description = "Pay schedule name to uniquely identify it in the system"
                this.mandatory = true
            },
            fieldRequirement {
                this.key = "PAY_SCHEDULE_FREQUENCY"
                this.label = "Pay Schedule Frequency"
                this.type = FieldType.FIELD_TYPE_TEXT
                this.description = "Frequency of the pay schedule."
                this.mandatory = true
            },
        )
    }

    @Test
    fun `test get requirements when use case is incorrectly passed`() {
        val entityId = 1L
        val useCase = "INVALID_PAY_SCHEDULE"

        val request = fieldRequirementsRequest {
            this.entityId = entityId
            this.useCase = useCase
        }

        every { skeletonService.getSkeleton(any(), any(), any()) } throws IllegalArgumentException(
            "No enum constant com.multiplier.compensation.domain.skeleton.enums.SkeletonType.INVALID_PAY_SCHEDULE",
        )

        val exception = assertFailsWith<InvalidArgumentException> {
            bulkUploadProcessor.getRequirements(request)
        }

        assertThat(exception.message).isEqualTo(
            "Invalid use case [INVALID_PAY_SCHEDULE]",
        )
    }

    @Test
    fun `validate returns success for PAY_SCHEDULE use case`() {
        val request = mockedCorrectPayScheduleValidateBulkInput()

        val expectedValidationResponse = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf(
                "1" to mutableListOf(),
                "2" to mutableListOf(),
            ),
            inputPlusDerivedRows = emptyMap(),
        )

        every { skeletonService.getSkeletonFields(any()) } returns setOf(
            "PAY_SCHEDULE_NAME",
            "PAY_SCHEDULE_FREQUENCY",
            "PAY_DATE_REFERENCE_TYPE",
            "IS_INSTALLMENT",
        )
        every { payScheduleValidationService.validate(any()) } returns expectedValidationResponse

        val result = bulkUploadProcessor.validate(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(true)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[0].messagesList).isEmpty()
        assertThat(result.resultsList[0].validatedInputDataMap.keys).containsExactlyInAnyOrder(
            "ENTITY_ID",
            "COMPANY_ID",
            "COUNTRY_CODE",
            "PAY_SCHEDULE_NAME",
            "PAY_SCHEDULE_FREQUENCY",
            "PAY_DATE_REFERENCE_TYPE",
            "IS_INSTALLMENT",
        )
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(true)
        assertThat(result.resultsList[0].messagesList).isEmpty()
        assertThat(result.resultsList[1].validatedInputDataMap.keys).containsExactlyInAnyOrder(
            "ENTITY_ID",
            "COMPANY_ID",
            "COUNTRY_CODE",
            "PAY_SCHEDULE_NAME",
            "PAY_SCHEDULE_FREQUENCY",
            "PAY_DATE_REFERENCE_TYPE",
            "IS_INSTALLMENT",
        )
    }

    @Test
    fun `validate returns failure for PAY_SCHEDULE use case`() {
        val request = mockedIncorrectPayScheduleValidateBulkInput()

        val expectedValidationResponse = ValidationDataCollector<PayScheduleDraft>(
            rowValidationResult = mutableMapOf(
                "1" to mutableListOf(),
                "2" to mutableListOf(
                    CellValidationResult(
                        field = KeyValuePair(
                            key = "PAY_DATE_REFERENCE_TYPE",
                            value = PayDateReference.PAY_SCHEDULE_START_DATE.name,
                        ),
                        type = ValidationResultType.ERROR,
                        message = "Pay schedule should have pay date reference type as COMPENSATION_START_DATE " +
                            "if isInstallment is true",
                    ),
                ),
            ),
            inputPlusDerivedRows = emptyMap(),
        )

        every { skeletonService.getSkeletonFields(any()) } returns setOf(
            "PAY_SCHEDULE_NAME",
            "PAY_SCHEDULE_FREQUENCY",
            "PAY_DATE_REFERENCE_TYPE",
            "IS_INSTALLMENT",
        )
        every { payScheduleValidationService.validate(any()) } returns expectedValidationResponse

        val result = bulkUploadProcessor.validate(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[0].messagesList).isEmpty()
        assertThat(result.resultsList[0].validatedInputDataMap.keys).containsExactlyInAnyOrder(
            "ENTITY_ID",
            "COMPANY_ID",
            "COUNTRY_CODE",
            "PAY_SCHEDULE_NAME",
            "PAY_SCHEDULE_FREQUENCY",
            "PAY_DATE_REFERENCE_TYPE",
            "IS_INSTALLMENT",
        )
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(false)
        assertThat(result.resultsList[1].messagesList.first().key).isEqualTo(
            "PAY_DATE_REFERENCE_TYPE",
        )
        assertThat(result.resultsList[1].messagesList.first().errorsList).containsExactly(
            "Pay schedule should have pay date reference type as COMPENSATION_START_DATE if isInstallment is true",
        )
        assertThat(result.resultsList[1].validatedInputDataMap.keys).containsExactlyInAnyOrder(
            "ENTITY_ID",
            "COMPANY_ID",
            "COUNTRY_CODE",
            "PAY_SCHEDULE_NAME",
            "PAY_SCHEDULE_FREQUENCY",
            "PAY_DATE_REFERENCE_TYPE",
            "IS_INSTALLMENT",
        )
    }

    @Test
    fun `upsert returns success as true for PAY_SCHEDULE use case`() {
        val request = mockedCorrectPayScheduleUpsertBulkInput()

        val expectedCreationResponse = PayScheduleBulkCreationOutput(
            entityId = 1L,
            status = OperationStatus.SUCCESS,
            rowValidationResults = emptyList(),
        )

        every { payScheduleCreationService.bulkCreatePaySchedules(any()) } returns expectedCreationResponse

        val result = bulkUploadProcessor.upsert(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(true)
    }

    @Test
    fun `upsert returns success as false for one row for PAY_SCHEDULE use case`() {
        val request = mockedIncorrectPayScheduleUpsertBulkInput()

        val expectedCreationResponse = PayScheduleBulkCreationOutput(
            entityId = 1L,
            status = OperationStatus.PARTIAL_SUCCESS,
            rowValidationResults = mutableListOf(
                RowValidationResult(
                    id = "2",
                    cellValidationResults = listOf(
                        CellValidationResult(
                            field = KeyValuePair(
                                key = "PAY_DATE_REFERENCE_TYPE",
                                value = PayDateReference.PAY_SCHEDULE_START_DATE.name,
                            ),
                            type = ValidationResultType.ERROR,
                            message = "Pay schedule should have pay date reference type as COMPENSATION_START_DATE " +
                                "if isInstallment is true",
                        ),
                    ),
                ),
            ),
        )

        every { payScheduleCreationService.bulkCreatePaySchedules(any()) } returns expectedCreationResponse

        val result = bulkUploadProcessor.upsert(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(false)
    }

    @Test
    fun `upsert returns success as true for COMPENSATION_SCHEMA use case`() {
        val request = mockedCorrectCompensationSchemaUpsertBulkInput()

        val expectedCreationResponse = CompensationSchemaBulkUpsertResponse(
            entityId = 1L,
            status = OperationStatus.SUCCESS,
            rowValidationResults = emptyList(),
        )

        every { compensationSchemaService.upsertCompensationSchema(any()) } returns expectedCreationResponse

        val result = bulkUploadProcessor.upsert(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(true)
    }

    @Test
    fun `upsert returns success as false for one row for COMPENSATION_SCHEMA use case`() {
        val request = mockedInCorrectCompensationSchemaUpsertBulkInput()

        val expectedCreationResponse = CompensationSchemaBulkUpsertResponse(
            entityId = 1L,
            status = OperationStatus.PARTIAL_SUCCESS,
            rowValidationResults = mutableListOf(
                RowValidationResult(
                    id = "2",
                    cellValidationResults = listOf(
                        CellValidationResult(
                            field = KeyValuePair(
                                key = "SCHEMA_NAME",
                                value = "default-schema-USA",
                            ),
                            type = ValidationResultType.ERROR,
                            message = "Schema name is duplicate.",
                        ),
                    ),
                ),
            ),
        )

        every { compensationSchemaService.upsertCompensationSchema(any()) } returns expectedCreationResponse

        val result = bulkUploadProcessor.upsert(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(false)
    }

    @Test
    fun `upsert returns success as true for COMPENSATION_REVISION use case`() {
        val request = mockedCorrectCompensationRevisionUpsertBulkInput()

        val expectedCreationResponse = CompensationBulkCreateResponse(
            entityId = 1L,
            status = OperationStatus.SUCCESS,
            requestId = UUID.randomUUID().toString(),
            rowValidationResults = emptyList(),
        )

        every { bulkCompensationRevisionService.execute(any()) } returns expectedCreationResponse

        val result = bulkUploadProcessor.upsert(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(true)
    }

    @Test
    fun `upsert returns success as false for one row for COMPENSATION_REVISION use case`() {
        val request = mockedInCorrectCompensationRevisionUpsertBulkInput()

        val expectedCreationResponse = CompensationBulkCreateResponse(
            entityId = 1L,
            status = OperationStatus.PARTIAL_SUCCESS,
            requestId = UUID.randomUUID().toString(),
            rowValidationResults = mutableListOf(
                RowValidationResult(
                    id = "2",
                    cellValidationResults = listOf(
                        CellValidationResult(
                            field = KeyValuePair(
                                key = "COMPONENT_NAME",
                                value = "Base Pay",
                            ),
                            type = ValidationResultType.ERROR,
                            message = "Component is duplicate.",
                        ),
                    ),
                ),
            ),
        )

        every { bulkCompensationRevisionService.execute(any()) } returns expectedCreationResponse

        val result = bulkUploadProcessor.upsert(request)

        assertThat(result.resultsList.size).isEqualTo(2)
        assertThat(result.resultsList[0].inputId).isEqualTo("1")
        assertThat(result.resultsList[0].success).isEqualTo(true)
        assertThat(result.resultsList[1].inputId).isEqualTo("2")
        assertThat(result.resultsList[1].success).isEqualTo(false)
    }

    @Test
    fun `getFieldData should return bulk data response for compensation setup use case`() {
        val request = bulkDataRequest {
            this.useCase = "COMPENSATION_SETUP"
            this.entityIds.addAll(listOf(1L))
        }
        val compensationInputFilter = CompensationInputFilter(
            contractIds = emptySet(),
            entityId = 1L,
            statuses = setOf(CompensationInputStatus.ONBOARDING_DRAFT),
            excludeExtrapolatedInputs = true,
        )
        val enrichedData = compensationInputEnrichedFixture(
            CompensationInputStatus.ONBOARDING_DRAFT,
        )

        every { compensationInputService.getCompensationInputEnrichedData(compensationInputFilter) } returns
            enrichedData

        val result = bulkUploadProcessor.getFieldData(request)

        assertThat(result).isNotNull().all {
            transform { it.rowsList.size }.isEqualTo(1)
            transform {
                it.rowsList[0].dataMap[CompensationSchemaSkeletonField.COMPONENT_NAME.id]
            }.isEqualTo(enrichedData[0].schemaComponentName)
            transform { it.rowsList[0].dataMap[CommonSkeletonField.CURRENCY.id] }.isEqualTo(enrichedData[0].currency)
            transform {
                it.rowsList[0].dataMap[CompensationRecordField.BILLING_RATE_TYPE.id]
            }.isEqualTo(enrichedData[0].billingRateType.description)
            transform {
                it.rowsList[0].dataMap[CompensationRecordField.BILLING_RATE.id]
            }.isEqualTo(enrichedData[0].billingRate?.toString().orEmpty())
            transform {
                it.rowsList[0].dataMap[CompensationSkeletonField.BILLING_FREQUENCY.id]
            }.isEqualTo(enrichedData[0].billingFrequency.toString())
            transform {
                it.rowsList[0].dataMap[PayScheduleSkeletonField.PAY_SCHEDULE_NAME.id]
            }.isEqualTo(enrichedData[0].payScheduleName)
            transform {
                it.rowsList[0].dataMap[PayScheduleSkeletonField.IS_INSTALLMENT.id]
            }.isEqualTo(enrichedData[0].isInstallment.toYesNo())
            transform {
                it.rowsList[0].dataMap[CompensationSkeletonField.START_DATE.id]
            }.isEqualTo(enrichedData[0].startDate.toString())
            transform {
                it.rowsList[0].dataMap[CompensationSkeletonField.END_DATE.id]
            }.isEqualTo(enrichedData[0].endDate?.toString().orEmpty())
            transform {
                it.rowsList[0].dataMap[CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id]
            }.isEqualTo(enrichedData[0].noOfInstallments?.toString().orEmpty())
        }
        verify { compensationInputService.getCompensationInputEnrichedData(compensationInputFilter) }
    }

    @Test
    fun `getFieldData should throw InvalidArgumentException for non compensation setup use case`() {
        val request = bulkDataRequest {
            this.useCase = "COMPENSATION_REVISION"
            this.entityIds.addAll(listOf(1L))
        }

        assertThrows<InvalidArgumentException> {
            bulkUploadProcessor.getFieldData(request)
        }.also { exception ->
            assertThat(exception).all {
                transform { it.errorCode }.isEqualTo(ValidationErrorCode.InvalidCompensationUseCase)
                transform { it.message }.isEqualTo("Use case [COMPENSATION_REVISION] not allowed for get field data")
                transform { it.context }.isEqualTo(mapOf("useCase" to "COMPENSATION_REVISION"))
            }
        }
    }

    private fun mockedCorrectPayScheduleValidateBulkInput() = validateUpsertInputBulkRequest {
        this.useCase = "PAY_SCHEDULE"
        this.inputs.addAll(
            listOf(
                validateUpsertInputRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-01",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "false",
                        ),
                    )
                    this.keys = platformKeys {
                        this.entityId = 1
                    }
                },
                validateUpsertInputRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-02",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "false",
                        ),
                    )
                    this.keys = platformKeys {
                        this.entityId = 1
                    }
                },
            ),
        )
    }

    private fun mockedCorrectPayScheduleUpsertBulkInput() = upsertBulkRequest {
        this.useCase = "PAY_SCHEDULE"
        this.inputs.addAll(
            listOf(
                upsertRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-01",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "false",
                        ),
                    )
                },
                upsertRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-02",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "false",
                        ),
                    )
                },
            ),
        )
    }

    private fun mockedIncorrectPayScheduleValidateBulkInput() = validateUpsertInputBulkRequest {
        this.useCase = "PAY_SCHEDULE"
        this.inputs.addAll(
            listOf(
                validateUpsertInputRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-01",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "false",
                        ),
                    )
                    this.keys = platformKeys {
                        this.entityId = 1
                    }
                },
                validateUpsertInputRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-02",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "true",
                        ),
                    )
                    this.keys = platformKeys {
                        this.entityId = 1
                    }
                },
            ),
        )
    }

    private fun mockedIncorrectPayScheduleUpsertBulkInput() = upsertBulkRequest {
        this.useCase = "PAY_SCHEDULE"
        this.inputs.addAll(
            listOf(
                upsertRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-01",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "false",
                        ),
                    )
                },
                upsertRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "PAY_SCHEDULE_NAME" to "SemiMonthly-02",
                            "PAY_SCHEDULE_FREQUENCY" to "SEMI_MONTHLY",
                            "PAY_DATE_REFERENCE_TYPE" to "PAY_SCHEDULE_START_DATE",
                            "IS_INSTALLMENT" to "true",
                        ),
                    )
                },
            ),
        )
    }

    private fun mockedCorrectCompensationSchemaUpsertBulkInput() = upsertBulkRequest {
        this.useCase = "COMPENSATION_SCHEMA"
        this.inputs.addAll(
            listOf(
                upsertRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "SCHEMA_NAME" to "default-USA",
                        ),
                    )
                },
                upsertRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "SCHEMA_NAME" to "default-USA",
                        ),
                    )
                },
            ),
        )
    }

    private fun mockedInCorrectCompensationSchemaUpsertBulkInput() = upsertBulkRequest {
        this.useCase = "COMPENSATION_SCHEMA"
        this.inputs.addAll(
            listOf(
                upsertRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                        ),
                    )
                },
                upsertRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "SCHEMA_NAME" to "default-USA",
                        ),
                    )
                },
            ),
        )
    }

    private fun mockedCorrectCompensationRevisionUpsertBulkInput() = upsertBulkRequest {
        this.useCase = "COMPENSATION_REVISION"
        this.inputs.addAll(
            listOf(
                upsertRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "COMPONENT_NAME" to "Base Pay",
                        ),
                    )
                },
                upsertRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "COMPONENT_NAME" to "Allowance",
                        ),
                    )
                },
            ),
        )
    }

    private fun mockedInCorrectCompensationRevisionUpsertBulkInput() = upsertBulkRequest {
        this.useCase = "COMPENSATION_REVISION"
        this.inputs.addAll(
            listOf(
                upsertRequest {
                    this.inputId = "1"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "COMPONENT_NAME" to "Base Pay",
                        ),
                    )
                },
                upsertRequest {
                    this.inputId = "2"
                    this.data.putAll(
                        mapOf(
                            "ENTITY_ID" to "1",
                            "COMPONENT_NAME" to "Base Pay",
                        ),
                    )
                },
            ),
        )
    }
}
