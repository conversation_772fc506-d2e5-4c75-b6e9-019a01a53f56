package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SchemaResolutionGroupValidatorTest {
    private lateinit var validator: SchemaResolutionGroupValidator
    private lateinit var collector: ValidationDataCollector<CompensationDraft>

    @BeforeEach
    fun setup() {
        validator = SchemaResolutionGroupValidator()
        collector = mockk(relaxed = true)
    }

    @Test
    fun `should return true when schema resolution is successful`() {
        val context = mockk<CompensationValidatorContext> {
            every { getSchemaErrorMessage() } returns null
        }

        val result = validator.validate(emptyList(), context, collector)

        assertTrue(result)
    }

    @Test
    fun `should return false and collect errors when schema resolution fails`() {
        val context = mockk<CompensationValidatorContext> {
            every { getSchemaErrorMessage() } returns "No schema found for EOR in given country"
        }

        val result = validator.validate(emptyList(), context, collector)

        assertFalse(result)
    }

    @Test
    fun `should return true when error message is empty`() {
        val context = mockk<CompensationValidatorContext> {
            every { getSchemaErrorMessage() } returns ""
        }

        val result = validator.validate(emptyList(), context, collector)

        assertTrue(result)
    }

    @Test
    fun `should return false and collect schema resolution error`() {
        val inputId = "draft-1"
        val schemaErrorMessage = "No schema found for EOR in given country"
        val input = ValidationInputItem(id = inputId, fieldKeyValuePairs = emptyList())
        val collector = ValidationDataCollector<CompensationDraft>(inputPlusDerivedRows = emptyMap())
        val context = mockk<CompensationValidatorContext> {
            every { getSchemaErrorMessage() } returns schemaErrorMessage
        }

        val validator = SchemaResolutionGroupValidator()

        val result = validator.validate(listOf(input), context, collector)

        assertFalse(result)

        val results = collector.rowValidationResult[inputId]
        assertNotNull(results)
        assertEquals(1, results!!.size)

        val validationResult = results.first()
        assertEquals(ValidationResultType.ERROR, validationResult.type)
        assertEquals(schemaErrorMessage, validationResult.message)
        assertEquals(CompensationSchemaSkeletonField.SCHEMA_NAME.toString(), validationResult.field.key)
    }
}
