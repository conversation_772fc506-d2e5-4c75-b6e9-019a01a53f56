package com.multiplier.compensation.service.compensation

import com.fasterxml.uuid.impl.TimeBasedEpochGenerator
import com.multiplier.compensation.domain.common.CategoryConstants.CATEGORY_CONTRACT_BASE_PAY
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.domain.compensationinput.CompensationInput
import com.multiplier.compensation.domain.compensationinput.CompensationInputFilter
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.OperationStatus
import com.multiplier.compensation.service.common.dto.RowItem
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.schemaTagToOfferingType
import com.multiplier.compensation.service.common.utils.toYesNo
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationBulkInput
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.UpsertCompensationByMemberRequest
import com.multiplier.compensation.service.compensation.dto.UpsertCompensationByMemberResponse
import com.multiplier.compensation.service.compensation.validation.validators.UpsertCompensationByMemberValidator
import com.multiplier.compensation.service.compensationinput.CompensationInputService
import com.multiplier.compensation.service.compensationschema.CompensationSchemaService
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import com.multiplier.compensation.service.payschedule.PayScheduleService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class UpsertCompensationByMemberService(
    private val bulkCompensationCreationService: BulkCompensationCreationService,
    private val compensationInputService: CompensationInputService,
    private val timeBasedEpochGenerator: TimeBasedEpochGenerator,
    private val compensationSchemaService: CompensationSchemaService,
    private val payScheduleService: PayScheduleService,
    private val upsertCompensationByMemberValidator: UpsertCompensationByMemberValidator,
) {
    fun upsertCompensationByMember(request: UpsertCompensationByMemberRequest): UpsertCompensationByMemberResponse {
        log.info { "Processing upsertCompensationByMember for contractId [${request.contractId}]" }

        // Step 1: Minimal validation - only category and country
        val validationResults = upsertCompensationByMemberValidator.validateMemberInput(request)
        if (validationResults.any { it.type == ValidationResultType.ERROR }) {
            return UpsertCompensationByMemberResponse(
                contractId = request.contractId,
                status = OperationStatus.FAILURE,
                componentValidationResults = validationResults,
            )
        }

        // Step 2: Fetch all onboarding draft components for this contract ONCE
        val allOnboardingComponents = getAllOnboardingDraftComponents(request.contractId)

        // Step 3: Fetch parent CONTRACT_BASE_PAY component to get required data
        val basePayComponent = allOnboardingComponents.firstOrNull { it.category == CATEGORY_CONTRACT_BASE_PAY }
        if (basePayComponent == null) {
            return UpsertCompensationByMemberResponse(
                contractId = request.contractId,
                status = OperationStatus.FAILURE,
                componentValidationResults = listOf(
                    CellValidationResult(
                        field = KeyValuePair(CommonSkeletonField.CONTRACT_ID.name, request.contractId.toString()),
                        type = ValidationResultType.ERROR,
                        message = "No [${CATEGORY_CONTRACT_BASE_PAY}] component found for this contract",
                    ),
                ),
            )
        }

        // Step 4: Build CompensationBulkInput using parent component data + user input
        val (bulkInputParam, buildValidationErrors) = buildCompensationBulkInput(
            request,
            basePayComponent,
            allOnboardingComponents,
        )

        // If there are validation errors from building the bulk input, return them
        if (buildValidationErrors.isNotEmpty()) {
            return UpsertCompensationByMemberResponse(
                contractId = request.contractId,
                status = OperationStatus.FAILURE,
                componentValidationResults = buildValidationErrors,
            )
        }

        val bulkInput = bulkInputParam ?: return UpsertCompensationByMemberResponse(
            contractId = request.contractId,
            status = OperationStatus.FAILURE,
            componentValidationResults = listOf(
                CellValidationResult(
                    field = KeyValuePair(CommonSkeletonField.CONTRACT_ID.name, request.contractId.toString()),
                    type = ValidationResultType.ERROR,
                    message = "Failed to build CompensationBulkInput",
                ),
            ),
        )

        // Step 5: Use BulkCompensationCreationService to handle the compensation setup
        val bulkResponse = bulkCompensationCreationService.execute(bulkInput)

        // Step 6: Map response back to UpsertCompensationByMemberResponse
        return mapBulkResponseToMemberResponse(request.contractId, bulkResponse)
    }

    private fun getAllOnboardingDraftComponents(contractId: Long): List<CompensationInput> {
        val filter = CompensationInputFilter(
            contractIds = setOf(contractId),
            statuses = setOf(CompensationInputStatus.ONBOARDING_DRAFT),
        )

        return compensationInputService.getCompensationInputData(filter)
    }

    private fun buildCompensationBulkInput(
        request: UpsertCompensationByMemberRequest,
        basePayComponent: CompensationInput,
        allOnboardingComponents: List<CompensationInput>,
    ): Pair<CompensationBulkInput?, List<CellValidationResult>> {
        val basePayInput = basePayComponent

        // Initialize employeeId (if needed) - for now, we assume it's not provided in the request
        val employeeId = "E1"

        // Extract user input fields and validate dependencies
        val userInputFields = extractUserInputFields(request)
        val dependencyData = fetchAndValidateDependencies(basePayInput, userInputFields)
        if (dependencyData.validationErrors.isNotEmpty()) {
            return Pair(null, dependencyData.validationErrors)
        }

        // Convert existing components to RowItems, excluding EMPLOYEE_CONTRIBUTION
        val existingRowItems =
            buildExistingComponentRowItems(
                allOnboardingComponents,
                employeeId,
                userInputFields,
                dependencyData,
            )

        // Create new EMPLOYEE_CONTRIBUTION RowItem from user input
        val newEmployeeContributionRowItem = buildNewEmployeeContributionRowItem(
            userInputFields,
            basePayInput,
            employeeId,
            request.contractId,
            dependencyData,
        )

        // Combine all RowItems (existing + new EMPLOYEE_CONTRIBUTION)
        val allRowItems = existingRowItems + newEmployeeContributionRowItem

        // Build custom parameters and assemble bulk input
        val customParams = buildCustomParameters(dependencyData.schema, dependencyData.offeringType)
        val bulkInput = assembleBulkInput(basePayInput, customParams, allRowItems)

        return Pair(bulkInput, emptyList())
    }

    // Helper data class to hold user input fields
    private data class UserInputFields(
        val componentName: String?,
        val billingRateType: String?,
        val billingRate: String?,
    )

    // Helper data class to hold dependency data
    private data class DependencyData(
        val inputComponentPaySchedule: PaySchedule?,
        val schemaItem: CompensationSchemaItem?,
        val schema: CompensationSchema?,
        val offeringType: OfferingType?,
        val inputCategory: String?,
        val validationErrors: List<CellValidationResult>,
        val paySchedules: Collection<PaySchedule>,
    )

    private fun extractUserInputFields(request: UpsertCompensationByMemberRequest): UserInputFields {
        // Extract user input fields (these should already be validated in validateMinimalRequirements)
        val componentName = request.keyValueInputs.find {
            it.key == CompensationSkeletonField.COMPONENT_NAME.name
        }?.value
        val billingRateType = request.keyValueInputs.find {
            it.key == CompensationSkeletonField.BILLING_RATE_TYPE.name
        }?.value
        val billingRate = request.keyValueInputs.find { it.key == CompensationSkeletonField.BILLING_RATE.name }?.value

        return UserInputFields(componentName, billingRateType, billingRate)
    }

    private fun fetchAndValidateDependencies(
        basePayInput: CompensationInput,
        userInputFields: UserInputFields,
    ): DependencyData {
        val paySchedule = payScheduleService.getPaySchedulesByIds(setOf(basePayInput.payScheduleId)).firstOrNull()

        val schemaItem = compensationSchemaService.getSchemaItemById(basePayInput.schemaItemId)
        val schema = if (schemaItem != null) {
            compensationSchemaService.getSchemaById(schemaItem.schemaId)
        } else {
            null
        }
        val category = schema?.schemaItems?.find { it.componentName == userInputFields.componentName }?.category

        val paySchedules = payScheduleService.getPaySchedules(
            entityId = basePayInput.entityId,
            offeringType = schema?.tags?.firstOrNull()?.schemaTagToOfferingType() ?: OfferingType.EOR,
            countryCode = schema?.country,
            excludeInactiveSchedules = true,
        )
        // Validate dependencies
        val dependencyValidationErrors =
            upsertCompensationByMemberValidator.validateDependencies(
                basePayInput,
                paySchedule,
                schema,
                schemaItem,
                category,
            )

        // Determine offering type based on schema configuration scope
        val offeringType = schema?.tags?.firstOrNull()?.schemaTagToOfferingType()

        return DependencyData(
            paySchedule,
            schemaItem,
            schema,
            offeringType,
            category,
            dependencyValidationErrors,
            paySchedules,
        )
    }

    private fun buildExistingComponentRowItems(
        allOnboardingComponents: List<CompensationInput>,
        employeeId: String?,
        userInputFields: UserInputFields,
        dependencyData: DependencyData,
    ): List<RowItem> {
        val inputComponentSchemaItemId = dependencyData.schema?.schemaItems?.find {
            it.componentName == userInputFields.componentName
        }?.id
        // Step 3: Convert existing components to RowItems, excluding EMPLOYEE_CONTRIBUTION
        return allOnboardingComponents
            .filter { inputComponentSchemaItemId != it.schemaItemId }
            .map { component ->
                convertCompensationInputToRowItem(component, employeeId, dependencyData)
            }
    }

    private fun buildNewEmployeeContributionRowItem(
        userInputFields: UserInputFields,
        basePayInput: CompensationInput,
        employeeId: String?,
        contractId: Long,
        dependencyData: DependencyData,
    ): RowItem {
        // Step 4: Create new EMPLOYEE_CONTRIBUTION RowItem from user input
        val keyValuePairs = mutableListOf(
            KeyValuePair(CompensationSkeletonField.COMPONENT_NAME.id, userInputFields.componentName),
            KeyValuePair(CompensationSkeletonField.BILLING_RATE_TYPE.id, userInputFields.billingRateType),
            KeyValuePair(CommonSkeletonField.CONTRACT_ID.id, contractId.toString()),
            KeyValuePair(CompensationSchemaSkeletonField.CATEGORY.id, dependencyData.inputCategory),
            KeyValuePair(CompensationSkeletonField.BILLING_FREQUENCY.id, basePayInput.billingFrequency.name),
            KeyValuePair(
                CompensationSkeletonField.PAY_SCHEDULE_NAME.id,
                dependencyData.inputComponentPaySchedule?.name,
            ),
            KeyValuePair(CompensationSkeletonField.IS_INSTALLMENT.id, basePayInput.isInstallment.toYesNo()),
            KeyValuePair(
                CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                basePayInput.noOfInstallments?.toString(),
            ),
            KeyValuePair(CompensationSkeletonField.START_DATE.id, basePayInput.startDate.toString()),
            KeyValuePair(CommonSkeletonField.ENTITY_ID.id, basePayInput.entityId.toString()),
            KeyValuePair(CommonSkeletonField.COMPANY_ID.id, basePayInput.companyId.toString()),
            KeyValuePair(CommonSkeletonField.CURRENCY.id, basePayInput.currency),
            KeyValuePair(CommonSkeletonField.EMPLOYEE_ID.id, employeeId),
        )
        basePayInput.endDate?.takeIf { !basePayInput.isInstallment }?.let {
            keyValuePairs.add(KeyValuePair(CompensationSkeletonField.END_DATE.id, it.toString()))
        }
        userInputFields.billingRate?.takeIf { it.isNotEmpty() }?.let {
            keyValuePairs.add(KeyValuePair(CompensationSkeletonField.BILLING_RATE.id, it))
        }
        return RowItem(
            id = timeBasedEpochGenerator.generate().toString(),
            keyValuePairs = keyValuePairs,
        )
    }

    private fun buildCustomParameters(
        schema: CompensationSchema?,
        offeringType: OfferingType?,
    ): Map<String, String> {
        // Build custom parameters
        val customParams = mutableMapOf<String, String>()
        customParams[BulkJsonCustomParamsUtil.BulkCustomKeys.SCHEMA_NAME] = schema?.name.orEmpty()
        customParams[BulkJsonCustomParamsUtil.BulkCustomKeys.COUNTRY_CODE] = schema?.country?.name.orEmpty()
        customParams[BulkJsonCustomParamsUtil.BulkCustomKeys.OFFERING_TYPE] = offeringType?.name.orEmpty()
        return customParams
    }

    private fun assembleBulkInput(
        basePayInput: CompensationInput,
        customParams: Map<String, String>,
        allRowItems: List<RowItem>,
    ): CompensationBulkInput = CompensationBulkInput(
        entityId = basePayInput.entityId,
        commitPartially = false,
        requestType = RequestType.COMPENSATION_SETUP,
        requestId = timeBasedEpochGenerator.generate().toString(),
        customParams = customParams,
        rowItems = allRowItems,
    )

    private fun convertCompensationInputToRowItem(
        compensationInput: CompensationInput,
        employeeId: String?,
        dependencyData: DependencyData,
    ): RowItem {
        // Get pay schedule name for this component
        val schemaItem = dependencyData.schema?.schemaItems?.find { it.id == compensationInput.schemaItemId }
        val componentName = schemaItem?.componentName
        val paySchedule = dependencyData.paySchedules.find { it.id == compensationInput.payScheduleId }
        val keyValuePairs = mutableListOf(
            KeyValuePair(CompensationSkeletonField.COMPONENT_NAME.name, componentName),
            KeyValuePair(
                CompensationSkeletonField.BILLING_RATE_TYPE.name,
                compensationInput.billingRateType.description,
            ),
            KeyValuePair(CommonSkeletonField.CONTRACT_ID.id, compensationInput.contractId.toString()),
            KeyValuePair(CompensationSchemaSkeletonField.CATEGORY.id, compensationInput.category),
            KeyValuePair(CompensationSkeletonField.BILLING_FREQUENCY.id, compensationInput.billingFrequency.name),
            KeyValuePair(CompensationSkeletonField.PAY_SCHEDULE_NAME.id, paySchedule?.name),
            KeyValuePair(CompensationSkeletonField.IS_INSTALLMENT.id, compensationInput.isInstallment.toYesNo()),
            KeyValuePair(
                CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                compensationInput.noOfInstallments?.toString(),
            ),
            KeyValuePair(CompensationSkeletonField.START_DATE.id, compensationInput.startDate.toString()),
            KeyValuePair(CommonSkeletonField.ENTITY_ID.id, compensationInput.entityId.toString()),
            KeyValuePair(CommonSkeletonField.COMPANY_ID.id, compensationInput.companyId.toString()),
            KeyValuePair(CommonSkeletonField.CURRENCY.id, compensationInput.currency),
            KeyValuePair(CommonSkeletonField.EMPLOYEE_ID.id, employeeId),
            KeyValuePair(CompensationSkeletonField.REASON_CODE.id, compensationInput.reasonCode?.name),
        )
        compensationInput.notes?.takeIf { it.isNotEmpty() }?.let {
            keyValuePairs.add(KeyValuePair(CompensationSkeletonField.NOTES.id, it))
        }
        if (!compensationInput.isInstallment) {
            keyValuePairs.add(
                KeyValuePair(CompensationSkeletonField.END_DATE.id, compensationInput.endDate?.toString()),
            )
        }
        compensationInput.billingRate?.toString()?.takeIf { it.isNotEmpty() }?.let {
            keyValuePairs.add(KeyValuePair(CompensationSkeletonField.BILLING_RATE.id, it))
        }
        val rowItem = RowItem(
            id = timeBasedEpochGenerator.generate().toString(),
            keyValuePairs = keyValuePairs,
        )

        return rowItem
    }

    private fun mapBulkResponseToMemberResponse(
        contractId: Long,
        bulkResponse: com.multiplier.compensation.service.compensation.dto.CompensationBulkCreateResponse,
    ): UpsertCompensationByMemberResponse {
        // Map validation results from bulk response
        val componentValidationResults = bulkResponse.rowValidationResults.flatMap { rowResult ->
            rowResult.cellValidationResults
        }

        return UpsertCompensationByMemberResponse(
            contractId = contractId,
            status = bulkResponse.status,
            componentValidationResults = componentValidationResults,
        )
    }
}
