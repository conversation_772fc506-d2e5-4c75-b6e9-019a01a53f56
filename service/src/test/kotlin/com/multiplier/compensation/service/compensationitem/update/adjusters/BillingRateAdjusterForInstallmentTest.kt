package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class BillingRateAdjusterForInstallmentTest {
    private companion object TestData {
        val oldImage = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withBillingRate(100.00)
            .withIsInstallment(true)
            .withNoOfInstallments(4)
            .build()

        fun preUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(oldImage)
    }

    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager
    private val generationPeriodInDays = 60L

    private lateinit var billingRateAdjuster: BillingRateUpdateAdjuster

    @BeforeEach
    fun setup() {
        itemGenerationManager = mockk()
        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns emptyList()

        itemUpdateManager = CompensationItemUpdateManager(
            compensationItemRepository = mockk(),
            transactional = mockk(),
        )
        cancellationArrearManager = CancellationArrearManager()
        spillOverItemManager = SpillOverItemManager()
        consolidationManager = CompensationItemConsolidationManager()

        billingRateAdjuster = BillingRateUpdateAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
            generationPeriodInDays,
        )
    }

    @Test
    fun `should adjust when new record dates do not align with the existing compensation dates`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .withCurrentInstallment(1)
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .withCurrentInstallment(2)
            .build()
        val item3 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCutOffDate(LocalDate.of(2024, 3, 15))
            .withCurrentInstallment(3)
            .build()
        val existingItems = listOf(item1, item2, item3)
        val newImage = oldImage.copy(
            endDate = LocalDate.of(2024, 2, 14),
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 2, 15))
            .withBillingRate(200.00)
            .withIsInstallment(true)
            .withNoOfInstallments(4)
            .build()

        fun postUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(newRecord)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 15))
                .withEndDate(LocalDate.of(2024, 3, 14))
                .withCurrentInstallment(1)
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 15))
                .withEndDate(LocalDate.of(2024, 4, 14))
                .withCurrentInstallment(2)
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 4, 15))
                .withEndDate(LocalDate.of(2024, 5, 14))
                .withCurrentInstallment(3)
                .build(),
        )

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }

        assertEquals(3, updatedContext.adjustedItems.newRecordItems.count())
        val adjustmentArrears = updatedContext.adjustedItems.newRecordItems.filter { it.isArrear }
        assertEquals(2, adjustmentArrears.count())
        val adjustmentArrear1 = adjustmentArrears.firstOrNull { it.startDate == LocalDate.of(2024, 2, 15) }
        val adjustmentArrear2 = adjustmentArrears.firstOrNull { it.startDate == LocalDate.of(2024, 3, 15) }
        assertNotNull(adjustmentArrear1)
        assertNotNull(adjustmentArrear2)
        assertEquals(item2.id, adjustmentArrear1.arrearOf)
        assertEquals(item3.id, adjustmentArrear2.arrearOf)
    }

    @Test
    fun `should adjust when new record dates align with the existing compensation dates`() {
        val item1 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .withCurrentInstallment(1)
            .build()
        val item2 = preUpdateItemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCutOffDate(LocalDate.of(2024, 2, 15))
            .withCurrentInstallment(2)
            .build()
        val existingItems = listOf(item1, item2)
        val newImage = oldImage.copy(
            endDate = LocalDate.of(2024, 1, 31),
        )
        val newRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withBillingRate(200.00)
            .withIsInstallment(true)
            .withNoOfInstallments(4)
            .build()

        fun postUpdateItemBuilder() = TestCompensationItemBuilder()
            .withCompensation(newRecord)
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(oldImage)
            .withNewImage(newImage)
            .withNewRecord(newRecord)
            .withExistingItems(existingItems)
            .build()

        every {
            itemGenerationManager.generateItems(newRecord, any<LocalDate>())
        } returns listOf(
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 2, 1))
                .withEndDate(LocalDate.of(2024, 2, 29))
                .withCurrentInstallment(1)
                .build(),
            postUpdateItemBuilder()
                .withStartDate(LocalDate.of(2024, 3, 1))
                .withEndDate(LocalDate.of(2024, 3, 30))
                .withCurrentInstallment(2)
                .build(),
        )

        val updatedContext = billingRateAdjuster.adjust(initialContext)

        assertTrue { initialContext != updatedContext }
        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isNotEmpty() }

        assertEquals(2, updatedContext.adjustedItems.newRecordItems.count())
        val adjustmentArrears = updatedContext.adjustedItems.newRecordItems.filter { it.isArrear }
        assertEquals(1, adjustmentArrears.count())
        assertEquals(item2.id, adjustmentArrears[0].arrearOf)
    }
}
