package com.multiplier.compensation.service.payschedule.mapper

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PayScheduleSkeletonFieldTest {
    @Test
    fun `test PayScheduleSkeletonField values`() {
        assertEquals("ID", PayScheduleSkeletonField.ID.id)
        assertEquals("COMPANY_ID", PayScheduleSkeletonField.COMPANY_ID.id)
        assertEquals("ENTITY_ID", PayScheduleSkeletonField.ENTITY_ID.id)
        assertEquals("PAY_SCHEDULE_NAME", PayScheduleSkeletonField.PAY_SCHEDULE_NAME.id)
        assertEquals("FREQUENCY", PayScheduleSkeletonField.FREQUENCY.id)
        assertEquals("START_DATE_REFERENCE", PayScheduleSkeletonField.START_DATE_REFERENCE.id)
        assertEquals("END_DATE_REFERENCE", PayScheduleSkeletonField.END_DATE_REFERENCE.id)
        assertEquals("RELATIVE_PAY_DAYS", PayScheduleSkeletonField.RELATIVE_PAY_DAYS.id)
        assertEquals("PAY_DATE_REFERENCE_TYPE", PayScheduleSkeletonField.PAY_DATE_REFERENCE_TYPE.id)
        assertEquals("IS_INSTALLMENT", PayScheduleSkeletonField.IS_INSTALLMENT.id)
        assertEquals("IS_ACTIVE", PayScheduleSkeletonField.IS_ACTIVE.id)
    }
}
