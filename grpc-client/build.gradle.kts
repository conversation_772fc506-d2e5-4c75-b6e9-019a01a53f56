plugins {
    alias(kt.plugins.jvm)
    alias(kt.plugins.spring)
}

dependencies {
    // Project
    implementation(projects.domain)

    // Multiplier
    implementation(multiplier.logging.keys)

    implementation(multiplier.grpc.common)
    implementation(multiplier.platform.spring.starter)
    implementation(multiplier.company.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.core.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.country.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.contract.offboarding.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.contract.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.payroll.schema.service.grpc.schema) { excludeGrpcDependencies() }
    implementation(multiplier.org.management.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.pigeon.service.client) { excludeGrpcDependencies() }
    implementation(multiplier.pigeon.service.schema) { excludeGrpcDependencies() }

    // Kotlin
    implementation(kt.coroutines.core)

    // Spring
    implementation(spring.boot.starter)

    // GRPC
    implementation(libs.grpc.spring.boot.starter) {
        exclude(spring.boot.starter.asProvider().get().group)
    }
    implementation(libs.grpc.kotlin.stub)
    implementation(libs.protobuf.kotlin)

    // Logging
    implementation(kt.logging)
    testRuntimeOnly(libs.logback.classic)

    // Commons
    implementation(libs.commons.lang3)

    // Test
    testImplementation(kt.test.junit5)
    testImplementation(test.assertk)
    testImplementation(test.mockk)
}

fun ExternalModuleDependency.excludeGrpcDependencies() {
    exclude(libs.grpc.spring.boot.starter.get().group)
    exclude(libs.grpc.stub.get().group)
    exclude(libs.protobuf.kotlin.get().group)
}
