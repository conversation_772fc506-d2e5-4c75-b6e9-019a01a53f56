package com.multiplier.compensation.graph.common.mapper

import com.multiplier.compensation.domain.common.BillingFrequency
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test
import com.multiplier.graph.types.BillingFrequency as GraphBillingFrequency

class BillingFrequencyMapperTest {
    @Test
    fun `test toGraph mapping`() {
        assertEquals(GraphBillingFrequency.ANNUALLY, BillingFrequency.ANNUALLY.toGraph())
        assertEquals(GraphBillingFrequency.ONETIME, BillingFrequency.ONETIME.toGraph())
        assertEquals(GraphBillingFrequency.SEMIANNUALLY, BillingFrequency.SEMIANNUALLY.toGraph())
        assertEquals(GraphBillingFrequency.QUARTERLY, BillingFrequency.QUARTERLY.toGraph())
        assertEquals(GraphBillingFrequency.MONTHLY, BillingFrequency.MONTHLY.toGraph())
        assertEquals(GraphBillingFrequency.SEMIMONTHLY, BillingFrequency.SEMIMONTHLY.toGraph())
        assertEquals(GraphBillingFrequency.BIWEEKLY, BillingFrequency.BIWEEKLY.toGraph())
        assertEquals(GraphBillingFrequency.WEEKLY, BillingFrequency.WEEKLY.toGraph())
        assertEquals(GraphBillingFrequency.DAILY, BillingFrequency.DAILY.toGraph())
        assertEquals(GraphBillingFrequency.HOURLY, BillingFrequency.HOURLY.toGraph())
    }

    @Test
    fun `test toDomain mapping`() {
        assertEquals(BillingFrequency.ANNUALLY, GraphBillingFrequency.ANNUALLY.toDomain())
        assertEquals(BillingFrequency.ONETIME, GraphBillingFrequency.ONETIME.toDomain())
        assertEquals(BillingFrequency.SEMIANNUALLY, GraphBillingFrequency.SEMIANNUALLY.toDomain())
        assertEquals(BillingFrequency.QUARTERLY, GraphBillingFrequency.QUARTERLY.toDomain())
        assertEquals(BillingFrequency.MONTHLY, GraphBillingFrequency.MONTHLY.toDomain())
        assertEquals(BillingFrequency.SEMIMONTHLY, GraphBillingFrequency.SEMIMONTHLY.toDomain())
        assertEquals(BillingFrequency.BIWEEKLY, GraphBillingFrequency.BIWEEKLY.toDomain())
        assertEquals(BillingFrequency.WEEKLY, GraphBillingFrequency.WEEKLY.toDomain())
        assertEquals(BillingFrequency.DAILY, GraphBillingFrequency.DAILY.toDomain())
        assertEquals(BillingFrequency.HOURLY, GraphBillingFrequency.HOURLY.toDomain())
    }

    @Test
    fun `test all domain BillingFrequency values are mapped to GraphBillingFrequency`() {
        val domainValues = BillingFrequency.entries

        domainValues.forEach { domainValue ->
            try {
                domainValue.toGraph() // This should succeed if mapping exists
            } catch (e: Exception) {
                fail("Missing mapping for domain BillingFrequency value: $domainValue. Exception: ${e.message}", e)
            }
        }
    }

    @Test
    fun `test all GraphBillingFrequency values are mapped to domain BillingFrequency`() {
        val graphValues = GraphBillingFrequency.entries.toTypedArray()

        graphValues.forEach { graphValue ->
            try {
                graphValue.toDomain() // This should succeed if mapping exists
            } catch (e: Exception) {
                fail("Missing mapping for GraphBillingFrequency value: $graphValue. Exception: ${e.message}", e)
            }
        }
    }

    @Test
    fun `test domain and GraphBillingFrequency enums have the same number of values`() {
        val domainSize = BillingFrequency.entries.size
        val graphSize = GraphBillingFrequency.entries.size

        assertEquals(
            domainSize,
            graphSize,
            "Mismatch in number of BillingFrequency values between domain and GraphBillingFrequency enums",
        )
    }
}
