package com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.impl.biweekly

import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.service.payschedule.schedulegeneration.dto.PayScheduleInterval
import com.multiplier.compensation.service.payschedule.schedulegeneration.strategy.PayScheduleStrategy
import java.time.LocalDate

class BiWeeklyPayScheduleStrategy : PayScheduleStrategy {
    private val biweeklyIntervalLength = 14L

    override fun generate(
        paySchedule: PaySchedule,
        from: LocalDate,
        to: LocalDate,
    ): Collection<PayScheduleInterval> {
        val payScheduleIntervals = mutableListOf<PayScheduleInterval>()
        var startDate = from
        var endDate = startDate.plusDays(biweeklyIntervalLength - 1)
        while (startDate <= to) {
            payScheduleIntervals.add(
                PayScheduleInterval(
                    startDate = startDate,
                    endDate = endDate,
                    payDate = estimatePayDate(
                        startDate = startDate,
                        endDate = endDate,
                        payDateReference = paySchedule.payDateReferenceType,
                        relativePayDays = paySchedule.relativePayDays,
                    ),
                ),
            )
            startDate = endDate.plusDays(1)
            endDate = startDate.plusDays(biweeklyIntervalLength - 1)
        }
        return payScheduleIntervals
    }

    override fun estimateEndDate(
        startDate: LocalDate,
        installments: Int,
    ): LocalDate = startDate.plusDays(installments * biweeklyIntervalLength).minusDays(1)
}
