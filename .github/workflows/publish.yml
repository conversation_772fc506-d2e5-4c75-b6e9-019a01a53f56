name: Publish to CodeArtifact

on:
  release:
    types: [ published ]

run-name: Publish [${{ github.ref_name }}] to CodeArtifact

jobs:
  aws:
    uses: ./.github/workflows/aws.yml
    secrets: inherit

  publish:
    if: startsWith(github.ref, 'refs/tags/')
    name: Publish
    runs-on: ubuntu-latest
    needs: [ aws ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Publish
        run: ./gradlew build publish -x check --info
