package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.contract.ContractOffBoardingStatus
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDate

private val log = KotlinLogging.logger {}

@Component
class OffBoardingCompensationValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val draft = collector.drafts[input.id] ?: return false
        val contractId = draft.contractId ?: return false
        val contractOffBoardingContext = context.offBoardingContracts[contractId] ?: return false

        val contractLastWorkingDay = contractOffBoardingContext.lastWorkingDay
        val contractOffBoardingStatus = contractOffBoardingContext.offBoardingStatus

        if (isOffBoardingAlreadyCompleted(contractOffBoardingStatus, contractId, input, collector)) {
            return false
        }

        if (isLastWorkingDayInvalid(contractLastWorkingDay, contractId, input, collector)) {
            return false
        }

        return when {
            draft.isInstallment == true -> validateInstallmentCompensation(
                contractId,
                contractLastWorkingDay,
                draft,
                input,
                collector,
            )
            else -> validateNonInstallmentCompensation(contractId, contractLastWorkingDay, draft, input, collector)
        }
    }

    private fun isLastWorkingDayInvalid(
        contractLastWorkingDay: LocalDate?,
        contractId: Long,
        input: ValidationInputItem,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        if (contractLastWorkingDay == null) {
            log.warn {
                "Contract last working day is not found for contract [$contractId]"
            }
            addValidationResult(
                input = input,
                field = CommonSkeletonField.CONTRACT_ID,
                resultType = ValidationResultType.ERROR,
                message = "Contract last working day could not be found",
                collector = collector,
            )
            return true
        }
        return false
    }

    private fun isOffBoardingAlreadyCompleted(
        contractOffBoardingStatus: ContractOffBoardingStatus,
        contractId: Long,
        input: ValidationInputItem,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        if (contractOffBoardingStatus == ContractOffBoardingStatus.COMPLETED) {
            log.warn {
                "Contract off boarding already completed for contract [$contractId]"
            }
            addValidationResult(
                input = input,
                field = CommonSkeletonField.CONTRACT_ID,
                resultType = ValidationResultType.ERROR,
                message = "Contract off boarding is already completed",
                collector = collector,
            )
            return true
        }
        return false
    }

    private fun validateInstallmentCompensation(
        contractId: Long,
        contractLastWorkingDay: LocalDate?,
        draft: CompensationDraft,
        input: ValidationInputItem,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean = if (draft.noOfInstallments == null || draft.noOfInstallments > 1) {
        log.warn {
            "Number of installments is greater than 1 for offboarding contract [$contractId]"
        }
        addValidationResult(
            input = input,
            field = CompensationSkeletonField.NUMBER_OF_INSTALLMENTS,
            resultType = ValidationResultType.ERROR,
            message = "Number of installments should be 1",
            collector = collector,
        )
        false
    } else if (
        contractLastWorkingDay == null ||
        draft.startDate == null ||
        draft.startDate.isAfter(contractLastWorkingDay)
    ) {
        log.warn {
            "Start date is after last working day for contract [$contractId]"
        }
        addValidationResult(
            input = input,
            field = CompensationSkeletonField.START_DATE,
            resultType = ValidationResultType.ERROR,
            message = "Start date should be before last working day",
            collector = collector,
        )
        false
    } else {
        true
    }

    private fun validateNonInstallmentCompensation(
        contractId: Long,
        contractLastWorkingDay: LocalDate?,
        draft: CompensationDraft,
        input: ValidationInputItem,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean = if (
        contractLastWorkingDay == null ||
        draft.endDate == null ||
        draft.endDate.isAfter(contractLastWorkingDay)
    ) {
        log.warn {
            "End date is after last working day for contract [$contractId]"
        }
        addValidationResult(
            input = input,
            field = CompensationSkeletonField.END_DATE,
            resultType = ValidationResultType.ERROR,
            message = "End date should be before last working day",
            collector = collector,
        )
        false
    } else {
        true
    }
}
