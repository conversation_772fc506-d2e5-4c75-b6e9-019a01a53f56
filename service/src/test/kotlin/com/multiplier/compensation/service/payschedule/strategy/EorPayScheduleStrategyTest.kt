package com.multiplier.compensation.service.payschedule.strategy

import com.multiplier.compensation.database.repository.payschedule.CountryPayScheduleConfigRepository
import com.multiplier.compensation.database.repository.payschedule.PayScheduleRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.payschedule.GetEligibleBillingFrequenciesRequest
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.UUID

@DisplayName("EorPayScheduleStrategy Tests")
class EorPayScheduleStrategyTest {
    private lateinit var payScheduleRepository: PayScheduleRepository
    private lateinit var countryPayScheduleConfigRepository: CountryPayScheduleConfigRepository
    private lateinit var strategy: EorCompensationPayScheduleSelectionStrategy

    @BeforeEach
    fun setUp() {
        payScheduleRepository = mockk()
        countryPayScheduleConfigRepository = mockk()
        strategy =
            EorCompensationPayScheduleSelectionStrategy(payScheduleRepository, countryPayScheduleConfigRepository)
    }

    @Nested
    @DisplayName("Primary Salary Component Tests")
    inner class PrimarySalaryComponentTests {
        @Test
        @DisplayName("Should return eligible pay schedules for primary salary components")
        fun shouldReturnEligiblePaySchedulesForPrimarySalary() {
            // Given
            val request = createPayScheduleRequest(
                billingFrequency = BillingFrequency.MONTHLY,
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val mockPaySchedules = listOf(createMockPaySchedule())

            every {
                countryPayScheduleConfigRepository.getAllPayScheduleFrequenciesByCountryStateAndBillingFrequency(
                    country = CountryCode.USA,
                    state = null,
                    billingFrequency = BillingFrequency.MONTHLY,
                )
            } returns listOf(PayScheduleFrequency.MONTHLY)

            every {
                countryPayScheduleConfigRepository.getStateCodesForCountry(CountryCode.USA)
            } returns emptyList()

            every {
                payScheduleRepository.findAllEligiblePaySchedules(
                    request = request,
                    scope = ConfigurationScope.COUNTRY,
                    frequencies = listOf(PayScheduleFrequency.MONTHLY),
                    isInstallment = false,
                    excludeInactiveSchedules = true,
                )
            } returns mockPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(mockPaySchedules, result)
            verify {
                payScheduleRepository.findAllEligiblePaySchedules(
                    request = request,
                    scope = ConfigurationScope.COUNTRY,
                    frequencies = listOf(PayScheduleFrequency.MONTHLY),
                    isInstallment = false,
                    excludeInactiveSchedules = true,
                )
            }
        }

        @Test
        @DisplayName("Should return billing frequencies for primary salary from country config")
        fun shouldReturnBillingFrequenciesForPrimarySalary() {
            // Given
            val request = createBillingFrequenciesRequest(
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val expectedBillingFrequencies = listOf(BillingFrequency.MONTHLY, BillingFrequency.BIWEEKLY)

            every {
                countryPayScheduleConfigRepository.getAllBillingFrequenciesByCountryAndState(
                    country = CountryCode.USA,
                    state = null,
                )
            } returns expectedBillingFrequencies

            every {
                countryPayScheduleConfigRepository.getStateCodesForCountry(CountryCode.USA)
            } returns emptyList()

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertEquals(expectedBillingFrequencies, result)
        }

        @Test
        @DisplayName("Should handle state codes correctly for primary salary")
        fun shouldHandleStateCodesCorrectlyForPrimarySalary() {
            // Given
            val request = createBillingFrequenciesRequest(
                state = "CA",
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val expectedBillingFrequencies = listOf(BillingFrequency.MONTHLY)

            every {
                countryPayScheduleConfigRepository.getStateCodesForCountry(CountryCode.USA)
            } returns listOf("CA", "NY", "TX")

            every {
                countryPayScheduleConfigRepository.getAllBillingFrequenciesByCountryAndState(
                    country = CountryCode.USA,
                    state = "CA",
                )
            } returns expectedBillingFrequencies

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertEquals(expectedBillingFrequencies, result)
        }
    }

    @Nested
    @DisplayName("Additional Component Tests")
    inner class AdditionalComponentTests {
        @Test
        @DisplayName("Should return eligible pay schedules for additional components")
        fun shouldReturnEligiblePaySchedulesForAdditionalComponents() {
            // Given
            val request = createPayScheduleRequest(
                billingFrequency = BillingFrequency.MONTHLY,
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )
            val mockPaySchedules = listOf(createMockPaySchedule())

            every {
                payScheduleRepository.findAllEligiblePaySchedules(
                    request = request,
                    scope = ConfigurationScope.COUNTRY,
                    frequencies = listOf(PayScheduleFrequency.MONTHLY),
                    isInstallment = null,
                    excludeInactiveSchedules = true,
                )
            } returns mockPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(mockPaySchedules, result)
        }

        @Test
        @DisplayName("Should return billing frequencies for additional components")
        fun shouldReturnBillingFrequenciesForAdditionalComponents() {
            // Given
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertFalse(result.isEmpty())
            assertTrue(result.contains(BillingFrequency.MONTHLY))
            assertTrue(result.contains(BillingFrequency.ANNUALLY))
        }

        @Test
        @DisplayName("Should handle different gross salary frequencies for additional components")
        fun shouldHandleDifferentGrossSalaryFrequencies() {
            // Given - Test with BI_WEEKLY gross salary frequency
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.BI_WEEKLY,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertFalse(result.isEmpty())
            assertTrue(result.contains(BillingFrequency.BIWEEKLY))
            assertTrue(result.contains(BillingFrequency.MONTHLY))
        }
    }

    @Nested
    @DisplayName("Validation Tests")
    inner class ValidationTests {
        @Test
        @DisplayName("Should validate successfully for primary salary components")
        fun shouldValidateSuccessfullyForPrimarySalaryComponents() {
            // Given
            val payScheduleRequest = createPayScheduleRequest(
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val billingFrequenciesRequest = createBillingFrequenciesRequest(
                compensationCategory = "CONTRACT_BASE_PAY",
            )

            // When & Then - Should not throw exception
            strategy.validateRequest(payScheduleRequest)
            strategy.validateBillingFrequenciesRequest(billingFrequenciesRequest)
        }

        @Test
        @DisplayName("Should throw exception when gross salary frequency is null for additional components")
        fun shouldThrowExceptionWhenGrossSalaryFrequencyIsNullForAdditionalComponents() {
            // Given
            val payScheduleRequest = createPayScheduleRequest(
                grossSalaryPayScheduleFrequency = null,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )
            val billingFrequenciesRequest = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = null,
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When & Then
            assertThrows<InvalidArgumentException> {
                strategy.validateRequest(payScheduleRequest)
            }
            assertThrows<InvalidArgumentException> {
                strategy.validateBillingFrequenciesRequest(billingFrequenciesRequest)
            }
        }

        @Test
        @DisplayName("Should throw exception when gross salary frequency is invalid for additional components")
        fun shouldThrowExceptionWhenGrossSalaryFrequencyIsInvalidForAdditionalComponents() {
            // Given - Use a frequency not in the EOR mapping
            val payScheduleRequest = createPayScheduleRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.ANNUALLY, // Not in EOR mapping
                compensationCategory = "ADDITIONAL_COMPONENT",
            )
            val billingFrequenciesRequest = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.ANNUALLY, // Not in EOR mapping
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When & Then
            assertThrows<InvalidArgumentException> {
                strategy.validateRequest(payScheduleRequest)
            }
            assertThrows<InvalidArgumentException> {
                strategy.validateBillingFrequenciesRequest(billingFrequenciesRequest)
            }
        }
    }

    @Nested
    @DisplayName("Configuration Scope Tests")
    inner class ConfigurationScopeTests {
        @Test
        @DisplayName("Should use COUNTRY scope for EOR offering")
        fun shouldUseCountryScopeForEorOffering() {
            // Given
            val request = createPayScheduleRequest(
                offeringCode = OfferingCode.EOR,
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val mockPaySchedules = listOf(createMockPaySchedule())

            every {
                countryPayScheduleConfigRepository.getAllPayScheduleFrequenciesByCountryStateAndBillingFrequency(
                    any(),
                    any(),
                    any(),
                )
            } returns listOf(PayScheduleFrequency.MONTHLY)

            every {
                countryPayScheduleConfigRepository.getStateCodesForCountry(any())
            } returns emptyList()

            every {
                payScheduleRepository.findAllEligiblePaySchedules(
                    request = request,
                    scope = ConfigurationScope.COUNTRY, // Should use COUNTRY scope
                    frequencies = any(),
                    isInstallment = false,
                    excludeInactiveSchedules = true,
                )
            } returns mockPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(mockPaySchedules, result)
            verify {
                payScheduleRepository.findAllEligiblePaySchedules(
                    request = request,
                    scope = ConfigurationScope.COUNTRY,
                    frequencies = any(),
                    isInstallment = false,
                    excludeInactiveSchedules = true,
                )
            }
        }

        @Test
        @DisplayName("Should use COMPANY scope for non-EOR offering")
        fun shouldUseCompanyScopeForNonEorOffering() {
            // Given
            val request = createPayScheduleRequest(
                offeringCode = OfferingCode.GLOBAL_PAYROLL,
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val mockPaySchedules = listOf(createMockPaySchedule())

            every {
                countryPayScheduleConfigRepository.getAllPayScheduleFrequenciesByCountryStateAndBillingFrequency(
                    any(),
                    any(),
                    any(),
                )
            } returns listOf(PayScheduleFrequency.MONTHLY)

            every {
                countryPayScheduleConfigRepository.getStateCodesForCountry(any())
            } returns emptyList()

            every {
                payScheduleRepository.findAllEligiblePaySchedules(
                    request = request,
                    scope = ConfigurationScope.COMPANY, // Should use COMPANY scope
                    frequencies = any(),
                    isInstallment = false,
                    excludeInactiveSchedules = true,
                )
            } returns mockPaySchedules

            // When
            val result = strategy.getEligiblePaySchedules(request)

            // Then
            assertEquals(mockPaySchedules, result)
            verify {
                payScheduleRepository.findAllEligiblePaySchedules(
                    request = request,
                    scope = ConfigurationScope.COMPANY,
                    frequencies = any(),
                    isInstallment = false,
                    excludeInactiveSchedules = true,
                )
            }
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    inner class EdgeCases {
        @Test
        @DisplayName("Should handle null country code gracefully")
        fun shouldHandleNullCountryCodeGracefully() {
            // Given
            val request = createBillingFrequenciesRequest(
                countryCode = null,
                compensationCategory = "CONTRACT_BASE_PAY",
            )

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertTrue(result.isEmpty())
        }

        @Test
        @DisplayName("Should handle invalid state code gracefully")
        fun shouldHandleInvalidStateCodeGracefully() {
            // Given
            val request = createBillingFrequenciesRequest(
                state = "INVALID_STATE",
                compensationCategory = "CONTRACT_BASE_PAY",
            )
            val expectedBillingFrequencies = listOf(BillingFrequency.MONTHLY)

            every {
                countryPayScheduleConfigRepository.getStateCodesForCountry(CountryCode.USA)
            } returns listOf("CA", "NY", "TX") // INVALID_STATE not in list

            every {
                countryPayScheduleConfigRepository.getAllBillingFrequenciesByCountryAndState(
                    country = CountryCode.USA,
                    state = null, // Should be null due to invalid state
                )
            } returns expectedBillingFrequencies

            // When
            val result = strategy.getEligibleBillingFrequencies(request)

            // Then
            assertEquals(expectedBillingFrequencies, result)
        }

        @Test
        @DisplayName("Should return empty list for unsupported additional component gross salary frequency")
        fun shouldReturnEmptyListForUnsupportedAdditionalComponentGrossSalaryFrequency() {
            // Given
            val request = createBillingFrequenciesRequest(
                grossSalaryPayScheduleFrequency = PayScheduleFrequency.ANNUALLY, // Not in EOR mapping
                compensationCategory = "ADDITIONAL_COMPONENT",
            )

            // When & Then - Should throw exception during validation
            assertThrows<InvalidArgumentException> {
                strategy.validateBillingFrequenciesRequest(request)
            }
        }
    }

    // Helper methods for creating test data
    private fun createPayScheduleRequest(
        entityId: Long = 123L,
        countryCode: CountryCode = CountryCode.USA,
        offeringCode: OfferingCode = OfferingCode.EOR,
        state: String? = null,
        billingFrequency: BillingFrequency? = BillingFrequency.MONTHLY,
        grossSalaryPayScheduleFrequency: PayScheduleFrequency? = null,
        compensationCategory: String = "CONTRACT_BASE_PAY",
    ) = GetEligiblePayScheduleRequest(
        entityId = entityId,
        countryCode = countryCode,
        offeringCode = offeringCode,
        state = state,
        billingFrequency = billingFrequency,
        grossSalaryPayScheduleFrequency = grossSalaryPayScheduleFrequency,
        compensationCategory = compensationCategory,
    )

    private fun createBillingFrequenciesRequest(
        entityId: Long = 123L,
        countryCode: CountryCode? = CountryCode.USA,
        offeringCode: OfferingCode = OfferingCode.EOR,
        state: String? = null,
        grossSalaryPayScheduleFrequency: PayScheduleFrequency? = null,
        compensationCategory: String? = "CONTRACT_BASE_PAY",
    ) = GetEligibleBillingFrequenciesRequest(
        entityId = entityId,
        countryCode = countryCode,
        offeringCode = offeringCode,
        state = state,
        grossSalaryPayScheduleFrequency = grossSalaryPayScheduleFrequency,
        compensationCategory = compensationCategory,
    )

    private fun createMockPaySchedule(
        id: UUID = UUID.randomUUID(),
        frequency: PayScheduleFrequency = PayScheduleFrequency.MONTHLY,
    ): PaySchedule = mockk {
        every { <EMAIL> } returns id
        every { <EMAIL> } returns frequency
    }
}
