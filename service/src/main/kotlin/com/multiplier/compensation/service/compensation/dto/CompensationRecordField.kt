package com.multiplier.compensation.service.compensation.dto

enum class CompensationR<PERSON>ord<PERSON>ield(
    val id: String,
) {
    ID("ID"),
    COMPANY_ID("COMPANY_ID"),
    ENTITY_ID("ENTITY_ID"),
    CONTRACT_ID("CONTRACT_ID"),
    SCHEMA_ITEM_ID("SCHEMA_ITEM_ID"),
    CATEGORY("CATEGORY"),
    IS_PART_OF_BASE_PAY("IS_PART_OF_BASE_PAY"),
    CURRENCY("CURRENCY"),
    BILLING_RATE_TYPE("BILLING_RATE_TYPE"),
    BILLING_RATE("BILLING_RATE"),
    BILLING_FREQUENCY("BILLING_FREQUENCY"),
    PAY_SCHEDULE_ID("PAY_SCHEDULE_ID"),
    START_DATE("START_DATE"),
    END_DATE("END_DATE"),
    IS_INSTALLMENT("IS_INSTALLMENT"),
    NO_OF_INSTALLMENTS("NO_OF_INSTALLMENTS"),
    PREVIOUS_ID("PREVIOUS_ID"),
    GENERATED_INSTALLMENTS("GENERATED_INSTALLMENTS"),
    PROCESSED_UNTIL_DATE("PROCESSED_UNTIL_DATE"),
    STATUS("STATUS"),
    CREATED_ON("CREATED_ON"),
    CREATED_BY("CREATED_BY"),
    UPDATED_ON("UPDATED_ON"),
    UPDATED_BY("UPDATED_BY"),
}
