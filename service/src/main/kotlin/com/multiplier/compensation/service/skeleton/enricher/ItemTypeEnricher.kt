package com.multiplier.compensation.service.skeleton.enricher

import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.skeleton.enums.SkeletonType
import com.multiplier.compensation.service.common.bulkplatform.BulkJsonCustomParamsUtil
import com.multiplier.compensation.service.common.enums.bulkcustomparams.OfferingType
import com.multiplier.compensation.service.skeleton.structure.SkeletonStructureData
import org.springframework.stereotype.Component

@Component
class ItemTypeEnricher : Enricher {
    override fun enrich(
        skeletonType: SkeletonType,
        entityId: Long,
        customParams: Map<String, String>,
        skeletonData: SkeletonStructureData,
    ): List<String> {
        val offeringType = BulkJsonCustomParamsUtil.getOfferingType(customParams)

        return when (offeringType) {
            OfferingType.EOR -> {
                // For EOR offering type, allow all item types
                ItemType.entries.map { it.name }
            }
            else -> {
                // For other offering types (like GLOBAL_PAYROLL), allow only INPUT type
                listOf(ItemType.INPUT.name)
            }
        }
    }
}
