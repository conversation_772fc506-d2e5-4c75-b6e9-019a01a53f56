package com.multiplier.compensation.domain.payschedule

import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class GetEligibleBillingFrequenciesRequestTest {
    @Test
    fun `should create GetEligibleBillingFrequenciesRequest with correct properties`() {
        val entityId = 123L
        val countryCode = CountryCode.USA
        val stateCode = "CA"

        val request = GetEligibleBillingFrequenciesRequest(
            entityId = entityId,
            countryCode = countryCode,
            state = stateCode,
            offeringCode = OfferingCode.GLOBAL_PAYROLL,
            grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
            compensationCategory = "CONTRACT_BASE_PAY",
        )

        assertEquals(entityId, request.entityId)
        assertEquals(countryCode, request.countryCode)
        assertEquals(stateCode, request.state)
        assertEquals(OfferingCode.GLOBAL_PAYROLL, request.offeringCode)
        assertEquals(PayScheduleFrequency.MONTHLY, request.grossSalaryPayScheduleFrequency)
        assertEquals("CONTRACT_BASE_PAY", request.compensationCategory)
    }
}
