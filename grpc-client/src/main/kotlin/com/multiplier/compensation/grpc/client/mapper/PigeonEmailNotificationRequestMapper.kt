package com.multiplier.compensation.grpc.client.mapper

import com.multiplier.compensation.domain.notification.EmailAttachment
import com.multiplier.compensation.domain.notification.PigeonEmailNotificationRequest
import com.multiplier.compensation.grpc.client.utils.maskEmail
import com.multiplier.pigeonservice.dto.Attachment
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody

fun PigeonEmailNotificationRequest.toStringWithEmailsMasked(): String {
    val maskedTo: String = to.maskEmail()
    val maskedCc: List<String> = cc.map { cc -> cc.maskEmail() }
    val maskedBcc: List<String> = bcc.map { bcc -> bcc.maskEmail() }

    return """Email {
        type='${type.name}', 
        from='$from', 
        to='$maskedTo', 
        cc='$maskedCc', 
        bcc='$maskedBcc', 
        subject='$subject', 
        data='$data', 
        attachments.size='${attachments.size}'
    }""".lines().joinToString(" ") {
        it.trim()
    }
}

fun PigeonEmailNotificationRequest.toEmailBody(): EmailNotificationBody = EmailNotificationBody.newBuilder()
    .setFrom(from)
    .setTo(to)
    .setSubject(subject)
    .setTemplateType(type.name)
    .addAllCc(cc)
    .addAllBcc(bcc)
    .putAllContentVariables(data)
    .build()

fun PigeonEmailNotificationRequest.toEmailAttachments(): List<Attachment> = this.attachments
    .map { at ->
        when (at) {
            is EmailAttachment.Id -> Attachment(at.id.toString())
            is EmailAttachment.File -> Attachment(at.name, at.contentType, at.blob)
        }
    }
