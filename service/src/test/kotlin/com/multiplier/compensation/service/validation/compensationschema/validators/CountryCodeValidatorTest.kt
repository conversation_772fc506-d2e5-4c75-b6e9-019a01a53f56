package com.multiplier.compensation.service.validation.compensationschema.validators

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.payschedule.dto.PayScheduleDraft
import com.multiplier.compensation.service.payschedule.dto.PayScheduleValidatorContext
import com.multiplier.compensation.service.payschedule.mapper.toPayScheduleDraft
import com.multiplier.compensation.service.payschedule.validation.validators.PayScheduleCountryCodeValidator
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CountryCodeValidatorTest {
    private lateinit var validator: PayScheduleCountryCodeValidator
    private lateinit var context: PayScheduleValidatorContext
    private lateinit var collector: ValidationDataCollector<PayScheduleDraft>

    @BeforeEach
    fun setUp() {
        validator = PayScheduleCountryCodeValidator()

        context = PayScheduleValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
        )

        collector = ValidationDataCollector(inputPlusDerivedRows = emptyMap())
    }

    @Test
    fun `should return false when country code is null`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.COUNTRY_CODE.id,
                    value = null,
                ),
            ),
        )
        collector.drafts[input.id] = input.toPayScheduleDraft(context.entityId)

        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CommonSkeletonField.COUNTRY_CODE.name &&
                    it.message == "Country Code is missing/invalid"
            }
        }
    }

    @Test
    fun `should return false when country code is invalid`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.COUNTRY_CODE.id,
                    value = "LKR",
                ),
            ),
        )
        collector.drafts[input.id] = input.toPayScheduleDraft(context.entityId)

        assertFalse { validator.validate(input, context, collector) }
        assertTrue {
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CommonSkeletonField.COUNTRY_CODE.name &&
                    it.message == "Country Code is missing/invalid"
            }
        }
    }

    @Test
    fun `should return true when country code is valid`() {
        val input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                KeyValuePair(
                    key = CommonSkeletonField.COUNTRY_CODE.id,
                    value = "LKA",
                ),
            ),
        )
        collector.drafts[input.id] = input.toPayScheduleDraft(context.entityId)

        assertTrue { validator.validate(input, context, collector) }
        assertTrue { collector.rowValidationResult[input.id].isNullOrEmpty() }
    }
}
