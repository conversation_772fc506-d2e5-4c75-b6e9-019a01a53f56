package com.multiplier.compensation.database.repository.payschedule

import assertk.all
import assertk.assertThat
import assertk.assertions.hasSize
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import com.multiplier.compensation.database.mapper.common.toDatabase
import com.multiplier.compensation.database.mapper.payschedule.common.toDatabase
import com.multiplier.compensation.database.mapper.payschedule.toDatabase
import com.multiplier.compensation.database.tables.records.PayScheduleRecord
import com.multiplier.compensation.database.tables.references.PAY_SCHEDULE
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.ConfigurationScopeConstants.SCOPE_COUNTRY_DUMMY_ENTITY_ID
import com.multiplier.compensation.domain.common.CountryCode
import com.multiplier.compensation.domain.common.OfferingCode
import com.multiplier.compensation.domain.common.PageRequest
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.payschedule.GetEligiblePayScheduleRequest
import com.multiplier.compensation.domain.payschedule.PaySchedule
import com.multiplier.compensation.domain.payschedule.PayScheduleRequest
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import com.multiplier.compensation.domain.payschedule.enums.PayScheduleFrequency
import com.multiplier.transaction.database.jooq.audit.Audit
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.RecordMapper
import org.jooq.Result
import org.jooq.SelectConditionStep
import org.jooq.SelectLimitAfterOffsetStep
import org.jooq.SelectLimitPercentAfterOffsetStep
import org.jooq.SelectWhereStep
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockKExtension::class)
class PayScheduleRepositoryTest {
    private lateinit var audit: Audit
    private lateinit var dsl: DSLContext
    private lateinit var transactionContext: TransactionContext
    private lateinit var repository: PayScheduleRepository

    @BeforeEach
    fun setUp() {
        audit = mockk()
        dsl = mockk()
        transactionContext = mockk()
        repository = PayScheduleRepository(dsl)
        every { transactionContext.trx } returns dsl
        every { transactionContext.audit } returns audit
        every { audit.userId } returns -1
        every { audit.revision } returns UUID.randomUUID()
        every { audit.time } returns LocalDateTime.now()
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return PaySchedule when findById is called`() {
        val payScheduleId = UUID.randomUUID()
        val expectedPaySchedule = mockedPaySchedule()

        val mockSelectWhereStep = mockk<SelectWhereStep<PayScheduleRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        every { dsl.selectFrom(PAY_SCHEDULE) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(PAY_SCHEDULE.ID.eq(payScheduleId)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetchSingle() } returns expectedPaySchedule.toDatabase()

        val result = repository.findById(payScheduleId)

        verify {
            dsl.selectFrom(PAY_SCHEDULE)
            mockSelectWhereStep.where(PAY_SCHEDULE.ID.eq(payScheduleId))
            mockSelectConditionStep.fetchSingle()
        }

        verifyResult(result, expectedPaySchedule)
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return PaySchedule when findAllByEntityId is called`() {
        val entityId = 1L
        val expectedPaySchedules = listOf(mockedPaySchedule())

        val mockSelectWhereStep = mockk<SelectWhereStep<PayScheduleRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        val expectedPayScheduleResult: Result<PayScheduleRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(PAY_SCHEDULE)
        expectedPayScheduleResult.addAll(expectedPaySchedules.map { it.toDatabase() })

        every { dsl.selectFrom(PAY_SCHEDULE) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(PAY_SCHEDULE.ENTITY_ID.eq(entityId)) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns expectedPayScheduleResult

        val result = repository.findAllByEntityId(
            entityId = entityId,
            excludeInactiveSchedules = false,
        )

        verify {
            dsl.selectFrom(PAY_SCHEDULE)
            mockSelectWhereStep.where(PAY_SCHEDULE.ENTITY_ID.eq(entityId))
            mockSelectConditionStep.fetch()
        }

        assertThat(result).hasSize(1)
        verifyResult(result.toList()[0], expectedPaySchedules[0])
    }

    @Suppress("IgnoredReturnValue")
    @Test
    fun `should return PaySchedule when findByEntityAndName is called`() {
        val expectedPaySchedule = mockedPaySchedule()
        val entityId = 1L
        val payScheduleName = "Monthly-05"

        val mockSelectWhereStep = mockk<SelectWhereStep<PayScheduleRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        every { dsl.selectFrom(PAY_SCHEDULE) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(PAY_SCHEDULE.ENTITY_ID.eq(entityId)) } returns mockSelectConditionStep
        every {
            mockSelectConditionStep.and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.eq(payScheduleName))
        } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetchOne() } returns expectedPaySchedule.toDatabase()

        val result = repository.findByEntityAndName(entityId, "Monthly-05")

        verify {
            dsl.selectFrom(PAY_SCHEDULE)
            mockSelectWhereStep.where(PAY_SCHEDULE.ENTITY_ID.eq(entityId))
            mockSelectConditionStep.and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.eq(payScheduleName))
            mockSelectConditionStep.fetchOne()
        }

        verifyResult(result, expectedPaySchedule)
    }

    @Test
    fun `should save all entries for entity level schedules creation`() {
        val entityId = 1L
        val inputPaySchedules = mockedPaySchedule()

        every {
            transactionContext.trx.batchInsert(any<List<PayScheduleRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        every {
            transactionContext.trx.selectFrom(PAY_SCHEDULE)
                .where(PAY_SCHEDULE.ENTITY_ID.eq(SCOPE_COUNTRY_DUMMY_ENTITY_ID))
                .and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.`in`("Monthly-05"))
                .and(PAY_SCHEDULE.COUNTRY.`in`(CountryCode.USA.toDatabase()))
        } returns mockSelectConditionStep

        val expectedConflictingSchedulesResult: Result<PayScheduleRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(PAY_SCHEDULE)
        expectedConflictingSchedulesResult.addAll(emptyList())

        every { mockSelectConditionStep.fetch() } returns expectedConflictingSchedulesResult

        repository.saveAllEnforcingConstraints(
            listOf(inputPaySchedules),
            transactionContext,
        )

        verify(exactly = 2) {
            transactionContext.audit
        }

        verify(exactly = 3) {
            transactionContext.trx
        }

        verify {
            transactionContext.trx.batchInsert(any<List<PayScheduleRecord>>()).execute()
        }
    }

    @Test
    fun `should fail during entity level schedules creation if conflicting schedules preexist`() {
        val entityId = 1L
        val inputPaySchedules = mockedPaySchedule()

        every {
            transactionContext.trx.batchInsert(any<List<PayScheduleRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        every {
            transactionContext.trx.selectFrom(PAY_SCHEDULE)
                .where(PAY_SCHEDULE.ENTITY_ID.eq(SCOPE_COUNTRY_DUMMY_ENTITY_ID))
                .and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.`in`("Monthly-05"))
                .and(PAY_SCHEDULE.COUNTRY.`in`(CountryCode.USA.toDatabase()))
        } returns mockSelectConditionStep

        val expectedConflictingSchedulesResult: Result<PayScheduleRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(PAY_SCHEDULE)
        expectedConflictingSchedulesResult.addAll(
            setOf(
                mockedPaySchedule(entityId = SCOPE_COUNTRY_DUMMY_ENTITY_ID).toDatabase(),
            ),
        )

        every { mockSelectConditionStep.fetch() } returns expectedConflictingSchedulesResult

        assertThrows<InvalidArgumentException> {
            repository.saveAllEnforcingConstraints(
                listOf(inputPaySchedules),
                transactionContext,
            )
        }
    }

    @Test
    fun `should save all entries for default country level schedules creation`() {
        val defaultEntity = SCOPE_COUNTRY_DUMMY_ENTITY_ID
        val inputPaySchedules = mockedPaySchedule(
            entityId = SCOPE_COUNTRY_DUMMY_ENTITY_ID,
            companyId = -1,
        )
        every {
            transactionContext.trx.batchInsert(any<List<PayScheduleRecord>>()).execute()
        } returns intArrayOf(defaultEntity.toInt())

        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        every {
            transactionContext.trx.selectFrom(PAY_SCHEDULE)
                .where(PAY_SCHEDULE.ENTITY_ID.notEqual(SCOPE_COUNTRY_DUMMY_ENTITY_ID))
                .and(PAY_SCHEDULE.PAY_SCHEDULE_NAME.`in`("Monthly-05"))
                .and(PAY_SCHEDULE.COUNTRY.`in`(CountryCode.USA.toDatabase()))
        } returns mockSelectConditionStep

        val expectedConflictingSchedulesResult: Result<PayScheduleRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(PAY_SCHEDULE)
        expectedConflictingSchedulesResult.addAll(emptyList())

        every { mockSelectConditionStep.fetch() } returns expectedConflictingSchedulesResult

        repository.saveAllEnforcingConstraints(
            listOf(inputPaySchedules),
            transactionContext,
        )

        verify(exactly = 2) {
            transactionContext.audit
        }

        verify(exactly = 3) {
            transactionContext.trx
        }

        verify {
            transactionContext.trx.batchInsert(any<List<PayScheduleRecord>>()).execute()
        }
    }

    @Test
    fun `test findAllByPayScheduleRequestWithPagination with valid request`() {
        val payScheduleRequest = PayScheduleRequest(
            companyIds = listOf(1L, 2L),
            entityIds = listOf(3L, 4L),
            referenceStartDate = null,
            referenceEndDate = null,
            offeringType = null,
            country = null,
        )
        val pageRequest = PageRequest(pageNumber = 1, pageSize = 10)
        val mockPaySchedules = listOf(mockedPaySchedule())
        val totalRecords = 1L

        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()
        val mockResult: Result<PayScheduleRecord> = mockk()
        val mockOffsetStep = mockk<SelectLimitAfterOffsetStep<PayScheduleRecord>>()
        val mockLimitStep = mockk<SelectLimitPercentAfterOffsetStep<PayScheduleRecord>>()

        every { dsl.selectFrom(PAY_SCHEDULE).where(any<List<Condition>>()) } returns mockSelectConditionStep
        every { dsl.fetchCount(mockSelectConditionStep) } returns totalRecords.toInt()
        every { mockSelectConditionStep.fetch() } returns mockResult
        every { mockSelectConditionStep.offset(any<Number>()) } returns mockOffsetStep
        every { mockOffsetStep.limit(any<Number>()) } returns mockLimitStep
        every { mockLimitStep.fetch() } returns mockResult
        every { mockResult.map(any<RecordMapper<PayScheduleRecord, PaySchedule>>()) } returns mockPaySchedules

        val result = repository.findAllByPayScheduleRequestWithPagination(payScheduleRequest, pageRequest)

        verify {
            dsl.fetchCount(mockSelectConditionStep)
            mockLimitStep.fetch()
        }

        assertEquals(mockPaySchedules, result.first)
        assertEquals(totalRecords, result.second)
    }

    @Test
    fun `should return pay schedules when findAllByCountryCodeAndConfigurationScope is called`() {
        val countryCode = CountryCode.USA
        val configurationScope = ConfigurationScope.COUNTRY
        val excludeInactiveSchedules = true
        val expectedPaySchedules = listOf(
            mockedPaySchedule().copy(
                configurationScope = ConfigurationScope.COUNTRY,
            ),
        )

        val mockSelectWhereStep = mockk<SelectWhereStep<PayScheduleRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        val expectedPayScheduleResult: Result<PayScheduleRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(PAY_SCHEDULE)
        expectedPayScheduleResult.addAll(expectedPaySchedules.map { it.toDatabase() })

        every { dsl.selectFrom(PAY_SCHEDULE) } returns mockSelectWhereStep
        every {
            mockSelectWhereStep.where(PAY_SCHEDULE.CONFIGURATION_SCOPE.eq(configurationScope.toDatabase()))
        } returns mockSelectConditionStep
        every {
            mockSelectConditionStep.and(PAY_SCHEDULE.COUNTRY.eq(countryCode.toDatabase()))
        } returns mockSelectConditionStep
        every {
            mockSelectConditionStep.and(PAY_SCHEDULE.IS_ACTIVE.eq(true))
        } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns expectedPayScheduleResult

        val result = repository.findAllByCountryCodeAndConfigurationScope(
            countryCode = countryCode,
            configurationScope = configurationScope,
            excludeInactiveSchedules = excludeInactiveSchedules,
        )

        assertThat(result).hasSize(1)
        verifyResult(result.toList()[0], expectedPaySchedules[0])
    }

    @Test
    fun `should return eligible pay schedules when findAllEligiblePaySchedules is called`() {
        val request = GetEligiblePayScheduleRequest(
            entityId = 1L,
            countryCode = CountryCode.USA,
            offeringCode = OfferingCode.EOR,
            state = null,
            grossSalaryPayScheduleFrequency = PayScheduleFrequency.MONTHLY,
            billingFrequency = BillingFrequency.MONTHLY,
            compensationCategory = null,
        )
        val scope = ConfigurationScope.COMPANY
        val frequencies = listOf(PayScheduleFrequency.MONTHLY, PayScheduleFrequency.WEEKLY)
        val excludeInactiveSchedules = true
        val expectedPaySchedules = listOf(
            mockedPaySchedule().copy(
                frequency = PayScheduleFrequency.MONTHLY,
                configurationScope = scope,
            ),
        )

        // Expected conditions as in the repository logic
        val expectedConditions = mutableListOf<Condition>(
            PAY_SCHEDULE.CONFIGURATION_SCOPE.eq(scope.toDatabase()),
            PAY_SCHEDULE.FREQUENCY.`in`(frequencies.map { it.toDatabase() }),
            PAY_SCHEDULE.COUNTRY.eq(request.countryCode!!.toDatabase()),
            PAY_SCHEDULE.IS_ACTIVE.eq(true),
        )

        val mockSelectWhereStep = mockk<SelectWhereStep<PayScheduleRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        val expectedPayScheduleResult: Result<PayScheduleRecord> = DSL.using(DefaultConfiguration())
            .newResult(PAY_SCHEDULE).apply {
                addAll(expectedPaySchedules.map { it.toDatabase() })
            }

        every { dsl.selectFrom(PAY_SCHEDULE) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(expectedConditions) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns expectedPayScheduleResult

        val result = repository.findAllEligiblePaySchedules(
            request = request,
            scope = scope,
            frequencies = frequencies,
            isInstallment = null,
            excludeInactiveSchedules = excludeInactiveSchedules,
        )

        assertThat(result).hasSize(1)
        verifyResult(result.first(), expectedPaySchedules.first())
    }

    @Test
    fun `should return pay schedules when findAllByEntityScopeAndFrequencies is called`() {
        val entityId = 1L
        val scope = ConfigurationScope.COMPANY
        val frequencies = listOf(PayScheduleFrequency.MONTHLY, PayScheduleFrequency.WEEKLY)
        val expectedPaySchedules = listOf(
            mockedPaySchedule().copy(
                frequency = PayScheduleFrequency.MONTHLY,
                configurationScope = scope,
            ),
            mockedPaySchedule().copy(
                frequency = PayScheduleFrequency.WEEKLY,
                configurationScope = scope,
            ),
        )

        // Create expected condition list (must match repository logic exactly)
        val expectedConditions = listOf(
            PAY_SCHEDULE.ENTITY_ID.eq(entityId),
            PAY_SCHEDULE.CONFIGURATION_SCOPE.eq(scope.toDatabase()),
            PAY_SCHEDULE.FREQUENCY.`in`(frequencies.map { it.toDatabase() }),
        )

        // Mock JOOQ steps
        val mockSelectWhereStep = mockk<SelectWhereStep<PayScheduleRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        val expectedResult: Result<PayScheduleRecord> = DSL.using(DefaultConfiguration())
            .newResult(PAY_SCHEDULE).apply {
                addAll(expectedPaySchedules.map { it.toDatabase() })
            }

        every { dsl.selectFrom(PAY_SCHEDULE) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(expectedConditions) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns expectedResult

        // Act
        val result = repository.findAllByEntityAndScopeAndFrequencies(entityId, scope, frequencies, null)

        // Assert
        assertThat(result).hasSize(2)
        verifyResult(result.toList()[0], expectedPaySchedules[0])
        verifyResult(result.toList()[1], expectedPaySchedules[1])
    }

    @Test
    fun `should return pay schedules when findAllByEntityScopeAndFrequencies is called with isInstallment false`() {
        // Arrange
        val entityId = 1L
        val scope = ConfigurationScope.COMPANY
        val frequencies = listOf(PayScheduleFrequency.MONTHLY, PayScheduleFrequency.WEEKLY)
        val isInstallment = false
        val expectedPaySchedules = listOf(
            mockedPaySchedule().copy(
                frequency = PayScheduleFrequency.MONTHLY,
                configurationScope = scope,
                isInstallment = false,
            ),
        )

        val expectedConditions = listOf(
            PAY_SCHEDULE.ENTITY_ID.eq(entityId),
            PAY_SCHEDULE.CONFIGURATION_SCOPE.eq(scope.toDatabase()),
            PAY_SCHEDULE.FREQUENCY.`in`(frequencies.map { it.toDatabase() }),
            PAY_SCHEDULE.IS_INSTALLMENT.eq(isInstallment),
        )

        val mockSelectWhereStep = mockk<SelectWhereStep<PayScheduleRecord>>()
        val mockSelectConditionStep = mockk<SelectConditionStep<PayScheduleRecord>>()

        val expectedResult: Result<PayScheduleRecord> = DSL.using(DefaultConfiguration())
            .newResult(PAY_SCHEDULE).apply {
                addAll(expectedPaySchedules.map { it.toDatabase() })
            }

        every { dsl.selectFrom(PAY_SCHEDULE) } returns mockSelectWhereStep
        every { mockSelectWhereStep.where(expectedConditions) } returns mockSelectConditionStep
        every { mockSelectConditionStep.fetch() } returns expectedResult

        // Act
        val result = repository.findAllByEntityAndScopeAndFrequencies(entityId, scope, frequencies, isInstallment)

        // Assert
        assertThat(result).hasSize(1)
        verifyResult(result.first(), expectedPaySchedules.first())
    }

    private fun mockedPaySchedule(
        entityId: Long = 1L,
        companyId: Long = 1L,
    ) = PaySchedule(
        id = UUID.randomUUID(),
        entityId = entityId,
        companyId = companyId,
        name = "Monthly-05",
        frequency = PayScheduleFrequency.MONTHLY,
        configurationScope = ConfigurationScope.COMPANY,
        country = CountryCode.USA,
        startDateReference = LocalDate.now().minusMonths(1),
        endDateReference = LocalDate.now(),
        payDateReferenceType = PayDateReference.PAY_SCHEDULE_END_DATE,
        relativePayDays = -1L,
        isInstallment = false,
        isActive = true,
        createdOn = LocalDateTime.now(),
        createdBy = -1,
        updatedOn = LocalDateTime.now(),
        updatedBy = -1,
        label = "Monthly Pay Schedule",
    )

    private fun verifyResult(
        result: PaySchedule?,
        expectedPaySchedule: PaySchedule,
    ) = assertThat(result).isNotNull().all {
        transform { it.id }.isEqualTo(expectedPaySchedule.id)
        transform { it.entityId }.isEqualTo(expectedPaySchedule.entityId)
        transform { it.companyId }.isEqualTo(expectedPaySchedule.companyId)
        transform { it.name }.isEqualTo(expectedPaySchedule.name)
        transform { it.frequency }.isEqualTo(expectedPaySchedule.frequency)
        transform { it.startDateReference }.isEqualTo(expectedPaySchedule.startDateReference)
        transform { it.endDateReference }.isEqualTo(expectedPaySchedule.endDateReference)
        transform { it.payDateReferenceType }.isEqualTo(expectedPaySchedule.payDateReferenceType)
        transform { it.relativePayDays }.isEqualTo(expectedPaySchedule.relativePayDays)
        transform { it.isInstallment }.isEqualTo(expectedPaySchedule.isInstallment)
        transform { it.isActive }.isEqualTo(expectedPaySchedule.isActive)
        transform { it.createdOn }.isEqualTo(expectedPaySchedule.createdOn)
        transform { it.createdBy }.isEqualTo(expectedPaySchedule.createdBy)
        transform { it.updatedOn }.isEqualTo(expectedPaySchedule.updatedOn)
        transform { it.updatedBy }.isEqualTo(expectedPaySchedule.updatedBy)
    }
}
