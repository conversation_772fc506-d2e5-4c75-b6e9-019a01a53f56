package com.multiplier.compensation.service.compensation.validation.validators

import com.multiplier.compensation.domain.common.contract.ContractStatus
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import org.springframework.stereotype.Component

@Component
class BackFillContractValidator : Validator<CompensationValidatorContext, CompensationDraft>() {
    private val contractStatusesAllowedForBackFill = setOf(
        ContractStatus.ACTIVE,
        ContractStatus.OFFBOARDING,
        ContractStatus.ENDED,
    )

    private val contractStatusesAllowedForRevisionBackFill = setOf(
        ContractStatus.ACTIVE,
        ContractStatus.OFFBOARDING,
        ContractStatus.ENDED,
        ContractStatus.ONBOARDING,
    )

    override fun validate(
        input: ValidationInputItem,
        context: CompensationValidatorContext,
        collector: ValidationDataCollector<CompensationDraft>,
    ): Boolean {
        val contractId = collector.drafts[input.id]?.contractId ?: return false
        val contractContext = context.contracts[contractId] ?: return false
        val contractStatus = contractContext.status
        val requestType = context.contextSource

        return when {
            validContractStatusForBackFill(requestType, contractStatus) -> true
            validContractStatusForRevisionBackFill(requestType, contractStatus) -> true
            else -> {
                addValidationResult(
                    input = input,
                    field = CommonSkeletonField.CONTRACT_ID,
                    resultType = ValidationResultType.ERROR,
                    message = "Contract status [${contractContext.status}] is not allowed for use-case [$requestType]",
                    collector = collector,
                )
                false
            }
        }
    }

    private fun validContractStatusForRevisionBackFill(
        requestType: RequestType,
        contractStatus: ContractStatus?,
    ) = requestType == RequestType.COMPENSATION_REVISION_BACKFILL &&
        contractStatus in contractStatusesAllowedForRevisionBackFill

    private fun validContractStatusForBackFill(
        requestType: RequestType,
        contractStatus: ContractStatus?,
    ) = requestType == RequestType.COMPENSATION_BACKFILL && contractStatus in contractStatusesAllowedForBackFill
}
