package com.multiplier.compensation.service.compensationitem.update.adjusters

import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.enums.CompensationItemStatus
import com.multiplier.compensation.service.compensationitem.generation.CompensationItemGenerationManager
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationItemBuilder
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.managers.CancellationArrearManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemConsolidationManager
import com.multiplier.compensation.service.compensationitem.update.managers.CompensationItemUpdateManager
import com.multiplier.compensation.service.compensationitem.update.managers.SpillOverItemManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class ImplicitBillingRateAdjusterForInstallmentTest {
    private companion object TestData {
        val dependentCompensation = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withBillingRate(10.0)
            .withBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE)
            .withIsInstallment(true)
            .withNoOfInstallments(3)
            .withGeneratedInstallments(3)
            .withProcessedUntilDate(LocalDate.of(2024, 3, 31))
            .build()
        val item1 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCurrentInstallment(1)
            .build()
        val item2 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCurrentInstallment(2)
            .build()
        val item3 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 3, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withCurrentInstallment(3)
            .build()
        val existingItems = listOf(item1, item2, item3)
        val parentCompensationOldImage = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withBillingRate(100.00)
            .build()
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 2, 14),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 2, 15))
            .withBillingRate(200.00)
            .build()

        fun itemBuilder() = TestCompensationItemBuilder()
            .withCompensation(dependentCompensation)
    }

    private lateinit var itemGenerationManager: CompensationItemGenerationManager
    private lateinit var itemUpdateManager: CompensationItemUpdateManager
    private lateinit var cancellationArrearManager: CancellationArrearManager
    private lateinit var spillOverItemManager: SpillOverItemManager
    private lateinit var consolidationManager: CompensationItemConsolidationManager
    private val generationPeriodInDays = 60L

    private lateinit var implicitBillingRateUpdateAdjuster: ImplicitBillingRateUpdateAdjuster

    @BeforeEach
    fun setup() {
        itemGenerationManager = mockk()
        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns emptyList()

        itemUpdateManager = CompensationItemUpdateManager(
            compensationItemRepository = mockk(),
            transactional = mockk(),
        )
        cancellationArrearManager = CancellationArrearManager()
        spillOverItemManager = SpillOverItemManager()
        consolidationManager = CompensationItemConsolidationManager()

        implicitBillingRateUpdateAdjuster = ImplicitBillingRateUpdateAdjuster(
            itemGenerationManager,
            itemUpdateManager,
            cancellationArrearManager,
            spillOverItemManager,
            consolidationManager,
            generationPeriodInDays,
        )
    }

    @Test
    fun `should not adjust when dependent compensation has no valid items(a future start date)`() {
        val futureStartDate = LocalDate.now().plusDays(generationPeriodInDays + 1)
        val dependentCompensation = TestCompensationBuilder()
            .withStartDate(futureStartDate)
            .withEndDate(futureStartDate.plusMonths(3))
            .withBillingRate(10.0)
            .withBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE)
            .withIsInstallment(true)
            .withNoOfInstallments(3)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(emptyList())
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns emptyList()

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
    }

    @Test
    fun `should not adjust when dependent compensation has no valid items(invalidated items)`() {
        val dependentCompensation = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withBillingRate(10.0)
            .withBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE)
            .withIsInstallment(true)
            .withNoOfInstallments(3)
            .build()
        val item1 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 1, 31))
            .withCutOffDate(LocalDate.of(2024, 1, 15))
            .withCurrentInstallment(1)
            .build()
        val item1Arrear = item1.copy(
            billingRate = 0.00,
            isArrear = true,
            arrearOf = item1.id,
            arrearTriggerReference = UUID.randomUUID().toString(),
        )
        val item2 = itemBuilder()
            .withStartDate(LocalDate.of(2024, 2, 1))
            .withEndDate(LocalDate.of(2024, 2, 29))
            .withCurrentInstallment(2)
            .withStatus(CompensationItemStatus.ABORTED)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item1Arrear, item2))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns emptyList()

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }
    }

    @Test
    fun `should not adjust when dependent compensation is not impacted (parent new record starts after the dependent compensation's end date)`() {
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 5, 31),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 6, 1))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(existingItems)
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns emptyList()

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isEmpty() }
    }

    @Test
    fun `should adjust when dependent compensation is entirely impacted`() {
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            status = CompensationStatus.ABORTED,
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(existingItems)
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item1, item2, item3)

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isNotEmpty() }
        assertTrue { updatedContext.adjustedItems.newRecordItems.isEmpty() }

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(
                    dependentCompensation.copy(
                        generatedInstallments = null,
                        processedUntilDate = null,
                    ),
                ),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }
        val oldImageItems = updatedContext.adjustedItems.oldImageItems
        val abortedItems = oldImageItems.filter { it.status == CompensationItemStatus.ABORTED }
        assertEquals(3, abortedItems.count())
        val abortedItem1 = oldImageItems.firstOrNull { it.id == item1.id }
        val abortedItem2 = oldImageItems.firstOrNull { it.id == item2.id }
        val abortedItem3 = oldImageItems.firstOrNull { it.id == item3.id }
        assertNotNull(abortedItem1)
        assertNotNull(abortedItem2)
        assertNotNull(abortedItem3)

        assertEquals(3, updatedContext.adjustedItems.newImageItems.count())
    }

    @Test
    fun `should adjust when processed lastValidItem ends before the parentNewRecordStartDate`() {
        val dependentCompensation = dependentCompensation.copy(
            generatedInstallments = 2,
            processedUntilDate = LocalDate.of(2024, 2, 29),
        )
        val item1 = item1.copy(
            cutOffDate = LocalDate.of(2024, 1, 15),
        )
        val item2 = item2.copy(
            cutOffDate = LocalDate.of(2024, 2, 15),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 4, 15),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 4, 16))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item2))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isNotEmpty() }

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(dependentCompensation),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }
    }

    @Test
    fun `should adjust when processed lastValidItem ends on the parentNewRecordStartDate`() {
        val item1 = item1.copy(
            cutOffDate = LocalDate.of(2024, 1, 15),
        )
        val item2 = item2.copy(
            cutOffDate = LocalDate.of(2024, 2, 15),
        )
        val item3 = item3.copy(
            cutOffDate = LocalDate.of(2024, 3, 15),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 3, 30),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 3, 31))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item2, item3))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertEquals(1, updatedContext.adjustedItems.newImageItems.count())

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(
                    dependentCompensation.copy(
                        generatedInstallments = 2,
                        processedUntilDate = item2.endDate,
                    ),
                ),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }
        val adjustmentArrears = updatedContext.adjustedItems.newImageItems.filter { it.isArrear }
        assertTrue { adjustmentArrears.isNotEmpty() }
        assertEquals(1, adjustmentArrears.count())
    }

    @Test
    fun `should adjust when processed lastValidItem ends after the parentNewRecordStartDate`() {
        val item1 = item1.copy(
            cutOffDate = LocalDate.of(2024, 1, 15),
        )
        val item2 = item2.copy(
            cutOffDate = LocalDate.of(2024, 2, 15),
        )
        val item3 = item3.copy(
            cutOffDate = LocalDate.of(2024, 3, 15),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 3, 15),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 3, 16))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item2, item3))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertEquals(1, updatedContext.adjustedItems.newImageItems.count())

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(
                    dependentCompensation.copy(
                        generatedInstallments = 2,
                        processedUntilDate = item2.endDate,
                    ),
                ),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }

        val adjustmentArrears = updatedContext.adjustedItems.newImageItems.filter { it.isArrear }
        assertTrue { adjustmentArrears.isNotEmpty() }
        assertEquals(1, adjustmentArrears.count())
    }

    @Test
    fun `should adjust when unprocessed lastValidItem ends before the parentNewRecordStartDate`() {
        val dependentCompensation = dependentCompensation.copy(
            generatedInstallments = 2,
            processedUntilDate = LocalDate.of(2024, 2, 29),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 4, 15),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 4, 16))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item2))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertTrue { updatedContext.adjustedItems.oldImageItems.isEmpty() }
        assertTrue { updatedContext.adjustedItems.newImageItems.isNotEmpty() }

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(dependentCompensation),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }
    }

    @Test
    fun `should adjust when unprocessed lastValidItem ends on the parentNewRecordStartDate`() {
        val item1 = item1.copy(
            cutOffDate = LocalDate.of(2024, 1, 15),
        )
        val item2 = item2.copy(
            cutOffDate = LocalDate.of(2024, 2, 15),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 3, 30),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 3, 31))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item2, item3))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertEquals(1, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(1, updatedContext.adjustedItems.newImageItems.count())

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(
                    dependentCompensation.copy(
                        generatedInstallments = 2,
                        processedUntilDate = item2.endDate,
                    ),
                ),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }
    }

    @Test
    fun `should adjust when unprocessed lastValidItem ends after the parentNewRecordStartDate`() {
        val item1 = item1.copy(
            cutOffDate = LocalDate.of(2024, 1, 15),
        )
        val item2 = item2.copy(
            cutOffDate = LocalDate.of(2024, 2, 15),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 3, 15),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 3, 16))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item2, item3))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val updatedContext = implicitBillingRateUpdateAdjuster.adjust(initialContext)

        assertEquals(1, updatedContext.adjustedItems.oldImageItems.count())
        assertEquals(1, updatedContext.adjustedItems.newImageItems.count())

        verify(exactly = 1) {
            itemGenerationManager.generateItems(
                eq(
                    dependentCompensation.copy(
                        generatedInstallments = 2,
                        processedUntilDate = item2.endDate,
                    ),
                ),
                eq(LocalDate.now().plusDays(generationPeriodInDays)),
            )
        }
    }

    @Test
    fun `should fail when processed installment lastValidItem is partially covered by arrears at the start`() {
        val dependentCompensation = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withBillingRate(10.0)
            .withBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE)
            .withIsInstallment(true)
            .withNoOfInstallments(3)
            .withGeneratedInstallments(1)
            .withProcessedUntilDate(LocalDate.of(2024, 1, 31))
            .build()
        val item1 = item1.copy(
            cutOffDate = LocalDate.of(2024, 1, 15),
        )
        val item1PartialArrear = item1.copy(
            startDate = LocalDate.of(2024, 1, 1),
            endDate = LocalDate.of(2024, 1, 15),
            billingRate = 0.00,
            isArrear = true,
            arrearOf = item1.id,
            arrearTriggerReference = UUID.randomUUID().toString(),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 3, 15),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 3, 16))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item1PartialArrear))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val exception = assertThrows<InvalidArgumentException> {
            implicitBillingRateUpdateAdjuster.adjust(initialContext)
        }
        assertEquals("Partial cancellation arrears are not applicable for installment types.", exception.message)
        assertEquals(ValidationErrorCode.InvalidExistingInstallmentArrear, exception.errorCode)
    }

    @Test
    fun `should fail when processed installment lastValidItem is partially covered by arrears at the end`() {
        val dependentCompensation = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 1, 1))
            .withEndDate(LocalDate.of(2024, 3, 31))
            .withBillingRate(10.0)
            .withBillingRateType(BillingRateType.BASE_PAY_PERCENTAGE)
            .withIsInstallment(true)
            .withNoOfInstallments(3)
            .withGeneratedInstallments(1)
            .withProcessedUntilDate(LocalDate.of(2024, 1, 31))
            .build()
        val item1 = item1.copy(
            cutOffDate = LocalDate.of(2024, 1, 15),
        )
        val item1PartialArrear = item1.copy(
            startDate = LocalDate.of(2024, 1, 16),
            endDate = LocalDate.of(2024, 1, 31),
            billingRate = 0.00,
            isArrear = true,
            arrearOf = item1.id,
            arrearTriggerReference = UUID.randomUUID().toString(),
        )
        val parentCompensationNewImage = parentCompensationOldImage.copy(
            endDate = LocalDate.of(2024, 3, 15),
        )
        val parentCompensationNewRecord = TestCompensationBuilder()
            .withStartDate(LocalDate.of(2024, 3, 16))
            .withBillingRate(200.00)
            .build()
        val initialContext = TestCompensationUpdateContextBuilder()
            .withOldImage(dependentCompensation)
            .withNewImage(dependentCompensation)
            .withExistingItems(listOf(item1, item1PartialArrear))
            .withParentUpdateContext(
                TestCompensationUpdateContextBuilder()
                    .withOldImage(parentCompensationOldImage)
                    .withNewImage(parentCompensationNewImage)
                    .withNewRecord(parentCompensationNewRecord)
                    .build(),
            )
            .build()

        every {
            itemGenerationManager.generateItems(any<Compensation>(), any<LocalDate>())
        } returns listOf(item3)

        val exception = assertThrows<InvalidArgumentException> {
            implicitBillingRateUpdateAdjuster.adjust(initialContext)
        }
        assertEquals("Partial cancellation arrears are not applicable for installment types.", exception.message)
        assertEquals(ValidationErrorCode.InvalidExistingInstallmentArrear, exception.errorCode)
    }
}
