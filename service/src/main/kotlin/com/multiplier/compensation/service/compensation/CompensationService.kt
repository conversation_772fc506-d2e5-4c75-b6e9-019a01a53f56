package com.multiplier.compensation.service.compensation

import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.domain.common.PageRequest
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.common.exception.requireCondition
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.CompensationEnriched
import com.multiplier.compensation.domain.compensation.CompensationRecordType
import com.multiplier.compensation.domain.compensation.CompensationRecordsFilter
import com.multiplier.compensation.domain.compensation.CompensationRecordsGrpcFilter
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import com.multiplier.compensation.domain.compensationinput.CompensationInputFilter
import com.multiplier.compensation.domain.compensationinput.enums.CompensationInputStatus
import com.multiplier.compensation.service.common.mapper.mapToCompensationEnriched
import com.multiplier.compensation.service.compensationinput.CompensationInputService
import com.multiplier.graph.types.LatestEndedCompensationRecordsFilter
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.util.UUID

private val log = KotlinLogging.logger {}

@Service
class CompensationService(
    private val compensationRepository: CompensationRepository,
    private val compensationInputService: CompensationInputService,
) {
    fun getCompensationWithPagination(
        filter: CompensationRecordsFilter,
        pageRequest: PageRequest,
    ): Pair<List<CompensationEnriched>, Long> {
        log.info { "Getting the compensation records with filter and pagination" }
        validateCompensationRecordsFilter(filter)
        return when (val recordTypes = filter.recordTypes) {
            listOf(CompensationRecordType.APPLIED, CompensationRecordType.DRAFT),
            listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED),
            -> {
                val inputFilter = buildCompensationInputFilter(filter)
                val inputRecords = compensationInputService
                    .getCompensationInputEnrichedDataWithPagination(inputFilter, pageRequest)
                    .let { (list, count) ->
                        Pair(list.map { it.mapToCompensationEnriched() }, count)
                    }
                val coreRecords = compensationRepository.findCompensationRecordsWithPagination(filter, pageRequest)
                Pair(inputRecords.first + coreRecords.first, inputRecords.second + coreRecords.second)
            }

            null, listOf(CompensationRecordType.APPLIED) -> {
                compensationRepository.findCompensationRecordsWithPagination(filter, pageRequest)
            }

            listOf(CompensationRecordType.DRAFT) -> {
                val inputFilter = buildCompensationInputFilter(filter)
                compensationInputService
                    .getCompensationInputEnrichedDataWithPagination(inputFilter, pageRequest)
                    .let { (list, count) ->
                        Pair(list.map { it.mapToCompensationEnriched() }, count)
                    }
            }

            else -> {
                throw InvalidArgumentException(
                    errorCode = ValidationErrorCode.InvalidCompensationRecordsFilter,
                    message = "Invalid record types: [$recordTypes]",
                )
            }
        }
    }

    fun getCompensationsWithFilters(filter: CompensationRecordsFilter): List<CompensationEnriched> {
        log.info { "Getting the compensation records with filter [$filter]" }
        validateCompensationRecordsFilter(filter)
        return when (val recordTypes = filter.recordTypes) {
            listOf(CompensationRecordType.APPLIED, CompensationRecordType.DRAFT),
            listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED),
            -> {
                val inputFilter = buildCompensationInputFilter(filter)
                val draftCompensationRecords = compensationInputService
                    .getCompensationInputEnrichedData(inputFilter)
                    .map { it.mapToCompensationEnriched() }
                val coreCompensationRecords = compensationRepository.findCompensationRecordsWithFilters(filter)

                draftCompensationRecords + coreCompensationRecords
            }

            null, listOf(CompensationRecordType.APPLIED) -> {
                compensationRepository.findCompensationRecordsWithFilters(filter)
            }

            listOf(CompensationRecordType.DRAFT) -> {
                val inputFilter = buildCompensationInputFilter(filter)
                compensationInputService
                    .getCompensationInputEnrichedData(inputFilter)
                    .map { it.mapToCompensationEnriched() }
            }

            else -> {
                throw InvalidArgumentException(
                    errorCode = ValidationErrorCode.InvalidCompensationRecordsFilter,
                    message = "Invalid record types: [$recordTypes]",
                )
            }
        }
    }

    fun getCompensationsByContractIds(contractIds: List<Long>): List<CompensationEnriched> {
        log.info { "Getting the compensation records by contract ids" }
        return compensationRepository.getEnrichedCompensationRecordsByContractIds(contractIds)
    }

    fun getEnrichedCompensationRecordsWithFilters(filter: CompensationRecordsGrpcFilter): List<CompensationEnriched> {
        log.info { "Starting getEnrichedCompensationRecordsWithFilters with filter [$filter]" }

        val recordTypes = filter.recordTypes ?: listOfNotNull(filter.recordType)
        val results = when {
            recordTypes.containsAll(listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED)) -> {
                getDraftCompensationRecords(filter) + getCoreCompensationRecords(filter)
            }
            recordTypes.contains(CompensationRecordType.DRAFT) -> getDraftCompensationRecords(filter)
            else -> getCoreCompensationRecords(filter)
        }

        log.info {
            "Completed getEnrichedCompensationRecordsWithFilters with" +
                " [${results.size}] records for contractIds ${filter.contractIds}"
        }
        return results
    }

    fun getLatestEndedCompensationRecords(filter: LatestEndedCompensationRecordsFilter): List<CompensationEnriched> {
        log.info { "Getting the latest ended compensation records with filter [$filter]" }
        require(filter.categories.isNotEmpty()) { "Categories should be provided" }
        return compensationRepository.getLatestEndedCompensationRecords(
            CompensationRecordsFilter(
                contractIds = filter.contractIds,
                categories = filter.categories,
            ),
        )
    }

    fun getCompensationsByIds(compensationIds: List<UUID>): List<Compensation> {
        log.info { "Getting the compensation records by ids $compensationIds" }
        return compensationRepository.findForIds(compensationIds, null)
    }

    private fun buildCompensationInputFilter(filter: CompensationRecordsFilter): CompensationInputFilter =
        CompensationInputFilter(
            entityId = filter.entityId,
            contractIds = filter.contractIds.orEmpty().toSet(),
            statuses = setOf(CompensationInputStatus.ONBOARDING_DRAFT),
        )

    private fun getCoreCompensationRecords(filter: CompensationRecordsGrpcFilter): List<CompensationEnriched> {
        log.info { "Getting compensation records with filter [$filter]" }
        validateCompensationRecordsFilter(filter)
        val coreCompensationRecords = compensationRepository.getEnrichedCompensationRecordsWithFilters(filter)
        log.info { "Fetched [${coreCompensationRecords.size}] records" }
        return coreCompensationRecords
    }

    private fun getDraftCompensationRecords(filter: CompensationRecordsGrpcFilter): List<CompensationEnriched> {
        val onboardingCompFilter = CompensationInputFilter(
            entityId = filter.entityId,
            contractIds = filter.contractIds.toSet(),
            statuses = setOf(CompensationInputStatus.ONBOARDING_DRAFT), // Currently only onboarding draft is supported
            includedCategories = filter.categories.toSet(),
        )
        log.info { "Fetching compensation draft records with filter [$onboardingCompFilter]" }
        val onboardingCompensationRecords = compensationInputService
            .getCompensationInputEnrichedData(onboardingCompFilter)
            .map { it.mapToCompensationEnriched() }
        log.info { "Fetched [${onboardingCompensationRecords.size}] draft records" }
        return onboardingCompensationRecords
    }

    private fun validateCompensationRecordsFilter(filter: CompensationRecordsFilter?) {
        val (isValid, message) = isValidCondition(filter ?: return)
        requireCondition(
            condition = isValid,
            errorCode = ValidationErrorCode.InvalidCompensationRecordsFilter,
            message = message,
            context = mapOf(
                "state" to (filter.state ?: "null"),
                "startDate" to (filter.startDate ?: "null"),
                "endDate" to (filter.endDate ?: "null"),
                "activeAsOn" to (filter.activeAsOn ?: "null"),
            ),
        )
    }

    private fun validateCompensationRecordsFilter(filter: CompensationRecordsGrpcFilter?) {
        requireCondition(
            condition = isValidCondition(filter ?: return),
            errorCode = ValidationErrorCode.InvalidCompensationRecordsFilter,
            message = "Invalid filter: activeAsOn can only be provided if state is ACTIVE.",
            context = mapOf(
                "state" to (filter.state ?: "null"),
                "activeAsOn" to (filter.activeAsOn ?: "null"),
            ),
        )
    }

    private fun isValidCondition(filter: CompensationRecordsFilter): Pair<Boolean, String> {
        // Either of companyId or contract Ids should be provided
        if (filter.companyId == null && filter.contractIds.isNullOrEmpty()) {
            return Pair(false, "Either company ID or contract IDs should be provided")
        }

        // If state is provided, startDate and endDate should not be provided
        if (filter.state != null && ((filter.startDate != null) || filter.endDate != null)) {
            return Pair(false, "State cannot be provided with startDate or endDate")
        }
        // startDate and endDate should be provided together
        if ((filter.startDate != null && filter.endDate == null) ||
            (filter.startDate == null && filter.endDate != null)
        ) {
            return Pair(false, "startDate and endDate should be provided if any one is not null")
        }
        // If state is Active, activeAsOn can be provided; otherwise, it should not be provided
        if (filter.activeAsOn != null && filter.state != CompensationState.ACTIVE) {
            return Pair(false, "activeAsOn can only be provided if state is ACTIVE")
        }
        return Pair(true, "")
    }

    private fun isValidCondition(filter: CompensationRecordsGrpcFilter): Boolean {
        // If state is Active, activeAsOn can be provided; otherwise, it should not be provided
        return !(filter.activeAsOn != null && filter.state != CompensationState.ACTIVE)
    }
}
