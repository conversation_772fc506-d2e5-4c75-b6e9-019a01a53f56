plugins {
    alias(kt.plugins.jvm)
    alias(kt.plugins.spring)
    alias(spring.plugins.boot) apply false
}

dependencies {
    // Project
    implementation(projects.app)
    implementation(projects.database)
    implementation(projects.grpcClient)
    implementation(projects.grpcServer)
    implementation(projects.service)

    // Multiplier
    implementation(multiplier.grpc.common)
    implementation(multiplier.grpc.bulkupload)
    implementation(multiplier.spring.messaging.kafka.starter)
    implementation(multiplier.transaction.coroutine.spring)
    implementation(multiplier.platform.spring.starter) {
        exclude(group = "com.multiplier.platform", module = "spring-messaging-kafka-starter")
    }
    implementation(multiplier.pigeon.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.pigeon.service.client) { excludeGrpcDependencies() }

    implementation(multiplier.country.service.schema) { excludeGrpcDependencies() }
    implementation(multiplier.payroll.schema.service.grpc.schema) { excludeGrpcDependencies() }
    implementation(multiplier.company.service.schema)
    implementation(multiplier.contract.service.schema)
    implementation(multiplier.contract.offboarding.service.schema)
    implementation(multiplier.org.management.service.schema)
    implementation(projects.grpcClient)
    implementation(projects.grpcSchema)
    implementation(projects.grpcServer)
    // Kotlin
    implementation(kt.coroutines.core)

    // Spring
    implementation(spring.boot.starter.web)

    // GRPC
    implementation(libs.grpc.spring.boot.starter) {
        exclude(spring.boot.starter.asProvider().get().group)
    }
    implementation(libs.grpc.kotlin.stub)
    implementation(libs.protobuf.kotlin)

    // Serialization
    implementation(libs.java.uuid.generator)

    // Shedlock
    implementation(libs.shedlock.spring)
    implementation(libs.shedlock.provider.jooq)

    // Logging
    implementation(kt.logging)
    testRuntimeOnly(libs.logback.classic)

    // Test
    implementation(test.mockk)
    testImplementation(spring.boot.starter.test)
    implementation("com.opencsv:opencsv:5.7.1")
    implementation(kt.test.junit5)
    testImplementation(test.assertk)
    testRuntimeOnly(test.h2)
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.2")
}

fun ExternalModuleDependency.excludeGrpcDependencies() {
    exclude(libs.grpc.spring.boot.starter.get().group)
    exclude(libs.grpc.stub.get().group)
    exclude(libs.protobuf.kotlin.get().group)
}

tasks.register<Test>("integrationTest") {
    description = "Runs the integration tests."
    group = "verification"

    useJUnitPlatform {
        includeTags("integration-test") // Include tests tagged as "integration-test"
    }
    extensions.configure(JacocoTaskExtension::class) {
        isEnabled = true
    }
}
