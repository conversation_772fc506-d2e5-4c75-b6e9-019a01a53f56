package com.multiplier.compensation.service.compensation.dto

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class CompensationRecordsEnrichedFieldsTest {
    @Test
    fun `should map all enum constants to correct field names`() {
        val expectedMappings = mapOf(
            "ID" to CompensationRecordsEnrichedFields.ID,
            "COMPANY_ID" to CompensationRecordsEnrichedFields.COMPANY_ID,
            "ENTITY_ID" to CompensationRecordsEnrichedFields.ENTITY_ID,
            "CONTRACT_ID" to CompensationRecordsEnrichedFields.CONTRACT_ID,
            "CATEGORY" to CompensationRecordsEnrichedFields.CATEGORY,
            "SCHEMA_COMPONENT_NAME" to CompensationRecordsEnrichedFields.SCHEMA_COMPONENT_NAME,
            "CURRENCY" to CompensationRecordsEnrichedFields.CURRENCY,
            "BILLING_RATE_TYPE" to CompensationRecordsEnrichedFields.BILLING_RATE_TYPE,
            "BILLING_RATE" to CompensationRecordsEnrichedFields.BILLING_RATE,
            "BILLING_FREQUENCY" to CompensationRecordsEnrichedFields.BILLING_FREQUENCY,
            "PAY_SCHEDULE_NAME" to CompensationRecordsEnrichedFields.PAY_SCHEDULE_NAME,
            "PAY_SCHEDULE_FREQUENCY" to CompensationRecordsEnrichedFields.PAY_SCHEDULE_FREQUENCY,
            "START_DATE" to CompensationRecordsEnrichedFields.START_DATE,
            "END_DATE" to CompensationRecordsEnrichedFields.END_DATE,
            "IS_INSTALLMENT" to CompensationRecordsEnrichedFields.IS_INSTALLMENT,
            "NO_OF_INSTALLMENTS" to CompensationRecordsEnrichedFields.NO_OF_INSTALLMENTS,
            "GENERATED_INSTALLMENTS" to CompensationRecordsEnrichedFields.GENERATED_INSTALLMENTS,
            "IS_TAXABLE" to CompensationRecordsEnrichedFields.IS_TAXABLE,
            "IS_FIXED" to CompensationRecordsEnrichedFields.IS_FIXED,
            "IS_PRORATED" to CompensationRecordsEnrichedFields.IS_PRORATED,
            "IS_MANDATORY" to CompensationRecordsEnrichedFields.IS_MANDATORY,
            "IS_PART_OF_BASE_PAY" to CompensationRecordsEnrichedFields.IS_PART_OF_BASE_PAY,
            "STATUS" to CompensationRecordsEnrichedFields.STATUS,
            "NOTES" to CompensationRecordsEnrichedFields.NOTES,
            "CREATED_ON" to CompensationRecordsEnrichedFields.CREATED_ON,
            "CREATED_BY" to CompensationRecordsEnrichedFields.CREATED_BY,
            "UPDATED_ON" to CompensationRecordsEnrichedFields.UPDATED_ON,
            "UPDATED_BY" to CompensationRecordsEnrichedFields.UPDATED_BY,
        )

        expectedMappings.forEach { (fieldName, enumConstant) ->
            assertEquals(fieldName, enumConstant.fieldName)
        }
    }
}
