package com.multiplier.compensation.service.payschedule.mapper

import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.dto.RowItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.payschedule.dto.PayScheduleBulkCreationInput
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PayScheduleBulkValidationMapperTest {
    @Test
    fun `should correctly map PayScheduleBulkCreationInput to PayScheduleBulkValidationInput`() {
        val rowItem1 = RowItem(
            id = "1",
            keyValuePairs = listOf(
                KeyValuePair("key1", "value1"),
                KeyValuePair("key2", "value2"),
            ),
        )
        val rowItem2 = RowItem(
            id = "2",
            keyValuePairs = listOf(
                KeyValuePair("keyA", "valueA"),
            ),
        )

        val input = PayScheduleBulkCreationInput(
            entityId = 123L,
            commitPartially = true,
            rowItems = listOf(rowItem1, rowItem2),
            customParams = emptyMap(),
        )

        val expectedItems = listOf(
            ValidationInputItem(
                id = "1",
                fieldKeyValuePairs = listOf(
                    KeyValuePair("key1", "value1"),
                    KeyValuePair("key2", "value2"),
                ),
            ),
            ValidationInputItem(
                id = "2",
                fieldKeyValuePairs = listOf(
                    KeyValuePair("keyA", "valueA"),
                ),
            ),
        )

        val result = input.toValidationInput()

        assertEquals(123L, result.entityId)
        assertEquals(expectedItems, result.items)
    }

    @Test
    fun `should return empty list when rowItems is empty`() {
        val input = PayScheduleBulkCreationInput(
            entityId = 456L,
            commitPartially = false,
            rowItems = emptyList(),
            customParams = emptyMap(),
        )

        val result = input.toValidationInput()

        assertEquals(456L, result.entityId)
        assertEquals(emptyList<ValidationInputItem>(), result.items)
    }

    @Test
    fun `should handle rowItems with empty keyValuePairs`() {
        val rowItem = RowItem(
            id = "3",
            keyValuePairs = emptyList(),
        )

        val input = PayScheduleBulkCreationInput(
            entityId = 789L,
            commitPartially = true,
            rowItems = listOf(rowItem),
            customParams = emptyMap(),
        )

        val expectedItems = listOf(
            ValidationInputItem(
                id = "3",
                fieldKeyValuePairs = emptyList(),
            ),
        )

        val result = input.toValidationInput()

        assertEquals(789L, result.entityId)
        assertEquals(expectedItems, result.items)
    }
}
