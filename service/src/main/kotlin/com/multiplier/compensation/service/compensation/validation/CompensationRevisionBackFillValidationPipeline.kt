package com.multiplier.compensation.service.compensation.validation

import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.common.validators.SkeletonValidator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.stages.CompensationRevisionBackFillGroupValidationStage
import com.multiplier.compensation.service.compensation.validation.stages.CompensationRevisionBackFillPrimaryValidationStage
import com.multiplier.compensation.service.compensation.validation.stages.CompensationRevisionBackFillSecondaryValidationStage
import org.springframework.stereotype.Component

/**
 * This class would be used to validate back-filled compensations for compensation revision backfill use-case.
 * This can be deprecated after GP and EOR migration of compensations is complete.
 */
@Component
class CompensationRevisionBackFillValidationPipeline(
    groupValidationStage: CompensationRevisionBackFillGroupValidationStage,
    primaryValidationStage: CompensationRevisionBackFillPrimaryValidationStage,
    secondaryValidationStage: CompensationRevisionBackFillSecondaryValidationStage,
    skeletonValidator: SkeletonValidator,
) : BaseCompensationValidationPipeline(
        groupValidationStage,
        listOf(
            primaryValidationStage,
            secondaryValidationStage,
        ),
        skeletonValidator,
    ) {
    override fun buildDraftItem(
        item: ValidationInputItem,
        context: CompensationValidatorContext,
    ) = item.toCompensationDraft(
        context,
    )
}
