package com.multiplier.compensation.service.compensationitem.update.handlers

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.service.compensationitem.testdatagenerator.TestCompensationUpdateContextBuilder
import com.multiplier.compensation.service.compensationitem.update.CompensationUpdateContext
import com.multiplier.compensation.service.compensationitem.update.processors.CompensationUpdateProcessor
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompensationValueUpdateHandlerTest {
    private val valueUpdateProcessor: CompensationUpdateProcessor = mockk()
    private val handler = CompensationValueUpdateHandler(valueUpdateProcessor)

    @Test
    fun `processors should contain the provided processor`() {
        assertEquals(1, handler.processors.size)
        assertTrue(handler.processors.contains(valueUpdateProcessor))
    }

    @Test
    fun `validateContext should pass when context has non-null newImage, oldImage and newRecord`() {
        val validContext = TestCompensationUpdateContextBuilder()
            .withOldImage(mockk())
            .withNewImage(mockk())
            .withNewRecord(mockk())
            .build()

        assertDoesNotThrow { handler.validateContext(validContext) }
    }

    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("provideTestCasesForInvalidContext")
    fun `validateContext should throw exception for invalid scenarios`(
        testName: String,
        context: CompensationUpdateContext,
    ) {
        val exception = assertThrows<InvalidArgumentException> {
            handler.validateContext(context)
        }

        assertEquals(ValidationErrorCode.InvalidItemAdjustmentRequest, exception.errorCode)
        assertEquals(
            "Incompatible update context for CompensationValueUpdateHandler." +
                "newImage, oldImage and newRecord should be non-null.",
            exception.message,
        )
    }

    fun provideTestCasesForInvalidContext(): List<Arguments> = listOf(
        Arguments.of(
            "Should fail when newImage is null",
            TestCompensationUpdateContextBuilder()
                .withOldImage(mockk())
                .withNewRecord(mockk())
                .build(),
        ),
        Arguments.of(
            "Should fail when oldImage is null",
            TestCompensationUpdateContextBuilder()
                .withNewImage(mockk())
                .withNewRecord(mockk())
                .build(),
        ),
        Arguments.of(
            "Should fail when newRecord is null",
            TestCompensationUpdateContextBuilder()
                .withOldImage(mockk())
                .withNewImage(mockk())
                .build(),
        ),
        Arguments.of(
            "Should fail when parentUpdateContext is non-null",
            TestCompensationUpdateContextBuilder()
                .withOldImage(mockk())
                .withNewImage(mockk())
                .withNewRecord(mockk())
                .withParentUpdateContext(mockk())
                .build(),
        ),
    )
}
