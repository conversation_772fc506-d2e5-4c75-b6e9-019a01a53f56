package com.multiplier.compensation.grpc.server.mapper.bulkupload.compensation

import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.common.exception.config.ValidationErrorCode
import com.multiplier.compensation.domain.compensation.CompensationRecordType
import com.multiplier.compensation.domain.compensation.enums.CompensationState
import com.multiplier.compensation.grpc.schema.CompensationRecordsFilter
import com.multiplier.compensation.grpc.server.mapper.compensation.toDomain
import com.multiplier.compensation.grpc.server.mapper.compensation.toDto
import com.multiplier.grpc.common.toDateTime
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import java.util.UUID
import com.multiplier.compensation.domain.compensation.CompensationRecordsGrpcFilter as DomainRecordsFilter
import com.multiplier.compensation.grpc.schema.Common.CompensationState as GrpcCompensationState
import com.multiplier.compensation.grpc.schema.CompensationRecordType as GrpcCompensationRecordType

class CompensationRecordsFilterMapperTest {
    @Test
    fun `toDto maps all fields correctly with single record type`() {
        // Given
        val id1 = UUID.randomUUID().toString()
        val id2 = UUID.randomUUID().toString()

        val grpcFilter = CompensationRecordsFilter.newBuilder()
            .addAllIds(listOf(id1, id2))
            .setEntityId(100L)
            .addAllContractIds(listOf(200L, 300L))
            .addAllCategories(listOf("Category1", "Category2"))
            .setActiveFrom(LocalDate.of(2025, 1, 1).atStartOfDay().toDateTime())
            .setActiveTo(LocalDate.of(2025, 12, 31).atStartOfDay().toDateTime())
            .setRecordType(GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_APPLIED)
            .setActiveAsOn(LocalDate.of(2025, 6, 15).atStartOfDay().toDateTime())
            .setState(GrpcCompensationState.COMPENSATION_STATE_ACTIVE)
            .build()

        // When
        val result = grpcFilter.toDto()

        // Then
        val expectedDto = DomainRecordsFilter(
            ids = listOf(id1, id2),
            entityId = 100L,
            contractIds = listOf(200L, 300L),
            categories = listOf("Category1", "Category2"),
            activeFrom = LocalDate.of(2025, 1, 1),
            activeTo = LocalDate.of(2025, 12, 31),
            recordType = CompensationRecordType.APPLIED,
            recordTypes = listOf(CompensationRecordType.APPLIED),
            activeAsOn = LocalDate.of(2025, 6, 15),
            state = CompensationState.ACTIVE,
        )

        assertEquals(expectedDto, result, "The toDto function did not correctly map all fields.")
    }

    @Test
    fun `toDto maps multiple record types correctly`() {
        // Given
        val grpcFilter = CompensationRecordsFilter.newBuilder()
            .setEntityId(100L)
            .addAllRecordTypes(
                listOf(
                    GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_DRAFT,
                    GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_APPLIED,
                ),
            )
            .build()

        // When
        val result = grpcFilter.toDto()

        // Then
        assertEquals(100L, result.entityId)
        assertEquals(
            listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED),
            result.recordTypes,
        )
    }

    @Test
    fun `toDto prioritizes record_types over record_type when both are present`() {
        // Given
        val grpcFilter = CompensationRecordsFilter.newBuilder()
            .setEntityId(100L)
            .setRecordType(GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_APPLIED)
            .addAllRecordTypes(
                listOf(
                    GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_DRAFT,
                    GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_APPLIED,
                ),
            )
            .build()

        // When
        val result = grpcFilter.toDto()

        // Then
        assertEquals(CompensationRecordType.APPLIED, result.recordType)
        assertEquals(
            listOf(CompensationRecordType.DRAFT, CompensationRecordType.APPLIED),
            result.recordTypes,
        )
    }

    @Test
    fun `toDto handles missing optional fields`() {
        // Given
        val grpcFilter = CompensationRecordsFilter.newBuilder()
            .setEntityId(100L)
            .build()

        // When
        val result = grpcFilter.toDto()

        // Then
        assertEquals(100L, result.entityId)
        assertTrue(result.ids.isEmpty())
        assertTrue(result.contractIds.isEmpty())
        assertTrue(result.categories.isEmpty())
        assertNull(result.activeFrom)
        assertNull(result.activeTo)
        assertNull(result.recordType)
        assertNull(result.recordTypes)
        assertNull(result.activeAsOn)
        assertNull(result.state)
    }

    @Test
    fun `toDomain correctly maps valid CompensationRecordType enums`() {
        assertEquals(CompensationRecordType.DRAFT, GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_DRAFT.toDomain())
        assertEquals(
            CompensationRecordType.APPLIED,
            GrpcCompensationRecordType.COMPENSATION_RECORD_TYPE_APPLIED.toDomain(),
        )
    }

    @Test
    fun `toDomain throws for unknown CompensationRecordType`() {
        // Given
        val invalidType = GrpcCompensationRecordType.UNRECOGNIZED

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            invalidType.toDomain()
        }
        assertTrue(exception.message!!.contains("Unknown CompensationRecordType"))
    }

    @Test
    fun `toDomain correctly maps valid CompensationState enums`() {
        assertEquals(CompensationState.ACTIVE, GrpcCompensationState.COMPENSATION_STATE_ACTIVE.toDomain())
        assertEquals(CompensationState.UPCOMING, GrpcCompensationState.COMPENSATION_STATE_UPCOMING.toDomain())
        assertEquals(CompensationState.COMPLETED, GrpcCompensationState.COMPENSATION_STATE_COMPLETED.toDomain())
        assertEquals(CompensationState.PROCESSING, GrpcCompensationState.COMPENSATION_STATE_PROCESSING.toDomain())
    }

    @Test
    fun `toDomain throws for unknown CompensationState`() {
        // Given
        val invalidState = GrpcCompensationState.UNRECOGNIZED

        // When & Then
        val exception = assertThrows<InvalidArgumentException> {
            invalidState.toDomain()
        }
        assertEquals(ValidationErrorCode.InvalidCompensationState, exception.errorCode)
        assertTrue(exception.message.contains("Unknown CompensationState"))
    }
}
