package com.multiplier.compensation.service.compensationschema.validation.validators

import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.compensationschema.globalPayrollTags
import com.multiplier.compensation.service.common.validationpipeline.Validator
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaDraft
import com.multiplier.compensation.service.compensationschema.dto.CompensationSchemaValidationContext
import com.multiplier.compensation.service.compensationschema.mapper.CompensationSchemaSkeletonField
import org.springframework.stereotype.Component

@Component
class SchemaIsFixedValidator : Validator<CompensationSchemaValidationContext, CompensationSchemaDraft>() {
    override fun validate(
        input: ValidationInputItem,
        context: CompensationSchemaValidationContext,
        collector: ValidationDataCollector<CompensationSchemaDraft>,
    ): Boolean {
        collector.drafts[input.id] ?: return false

        val schemaCategory = input.fieldKeyValuePairs.find {
            it.key == CompensationSchemaSkeletonField.CATEGORY.id
        }?.value
        val isFixed = input.fieldKeyValuePairs.find { it.key == CompensationSchemaSkeletonField.IS_FIXED.id }?.value
        val tags = input.fieldKeyValuePairs.find { it.key == CompensationSchemaSkeletonField.TAGS.id }?.value

        if (isTagsGlobalPayroll(tags)) {
            if (isFixed.equals("No")) {
                addValidationResult(
                    input = input,
                    field = CompensationSchemaSkeletonField.IS_FIXED,
                    resultType = ValidationResultType.ERROR,
                    message = "Variable type is not supported for this category.",
                    collector = collector,
                )
                return false
            }
        } else {
            if (schemaCategory != CategoryConstants.CATEGORY_CONTRACT_ALLOWANCE && isFixed.equals("No")) {
                addValidationResult(
                    input = input,
                    field = CompensationSchemaSkeletonField.IS_FIXED,
                    resultType = ValidationResultType.ERROR,
                    message = "Variable type is not supported for this category.",
                    collector = collector,
                )
                return false
            }
        }
        return true
    }

    private fun isTagsGlobalPayroll(tags: String?): Boolean = tags?.uppercase() in globalPayrollTags
}
