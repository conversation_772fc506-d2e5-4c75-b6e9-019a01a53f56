plugins {
    alias(kt.plugins.jvm)
    alias(kt.plugins.spring)
}

dependencies {
    // Multiplier
    implementation(multiplier.platform.spring.starter) {
        exclude(group = "com.multiplier.platform", module = "spring-messaging-kafka-starter")
    }

    // Spring
    implementation(spring.boot)

    // Logging
    implementation(kt.logging)
    testRuntimeOnly(libs.logback.classic)

    // Test
    testImplementation(kt.test.junit5)
    testImplementation(test.assertk)
    testImplementation(test.mockk)

    implementation(libs.java.uuid.generator)
}
