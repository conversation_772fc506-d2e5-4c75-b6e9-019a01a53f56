import com.multiplier.compensation.database.repository.compensation.CompensationRepository
import com.multiplier.compensation.database.repository.compensationitem.CompensationItemRepository
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.compensation.Compensation
import com.multiplier.compensation.domain.compensation.enums.CompensationStatus
import com.multiplier.compensation.domain.compensationitem.CompensationItem
import com.multiplier.compensation.service.compensation.CompensationStatusUpdateService
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import com.multiplier.transaction.database.jooq.transaction.Transactional
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals

class CompensationStatusUpdateServiceTest {
    private lateinit var compensationStatusUpdateService: CompensationStatusUpdateService
    private lateinit var compensationRepository: CompensationRepository
    private lateinit var compensationItemRepository: CompensationItemRepository
    private lateinit var transactional: Transactional

    @BeforeEach
    fun setup() {
        compensationRepository = mockk()
        compensationItemRepository = mockk()
        transactional = mockk()
        compensationStatusUpdateService = CompensationStatusUpdateService(
            compensationItemRepository,
            compensationRepository,
            transactional,
        )
    }

    @Test
    fun `throw InvalidArgumentException when compensation not found`() {
        val compensationId = UUID.randomUUID()
        every { compensationRepository.findById(compensationId, any()) } returns null
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }

        val exception = assertThrows<InvalidArgumentException> {
            compensationStatusUpdateService.refreshStatus(compensationId)
        }

        assertEquals("Compensation with Id [$compensationId] not found.", exception.message)
        verify(exactly = 0) { compensationRepository.saveAll(any(), any()) }
    }

    @Test
    fun `throw InvalidArgumentException when compensation status is invalid`() {
        val compensation = buildCompensation(
            startDate = LocalDate.now().minusMonths(3).withDayOfMonth(1),
            endDate = null,
            status = CompensationStatus.ABORTED,
        )
        every { compensationRepository.findById(compensation.id, any()) } returns compensation
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }

        val exception = assertThrows<InvalidArgumentException> {
            compensationStatusUpdateService.refreshStatus(compensation.id)
        }

        assertEquals(
            "Compensation with Id [${compensation.id}] has invalid status [${CompensationStatus.ABORTED}]",
            exception.message,
        )
        verify(exactly = 0) { compensationRepository.saveAll(any(), any()) }
    }

    @Test
    fun `update processing from and to when last item is not generated and no compensation item is processing`() {
        val compensation = buildCompensation(
            startDate = LocalDate.now().minusMonths(3).withDayOfMonth(1),
            endDate = null,
        )
        val earliestCutoffDateInFuture = LocalDate.now().plusDays(10)

        val expectedUpdatedCompensation = compensation.copy(
            processingFrom = earliestCutoffDateInFuture,
            processingTo = null,
        )

        every { compensationRepository.findById(compensation.id, any()) } returns compensation
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }

        every {
            compensationItemRepository.findEarliestCutoffForCompensation(compensation.id, any())
        } returns earliestCutoffDateInFuture
        every { compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any()) } just Runs

        compensationStatusUpdateService.refreshStatus(compensation.id)

        verify {
            compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any())
        }
    }

    @Test
    fun `update processing from and to when last item is not generated and at least one compensation item is processing`() {
        val compensation = buildCompensation(
            startDate = LocalDate.now().minusMonths(3).withDayOfMonth(1),
            endDate = null,
        )
        val earliestCutoffDateInPast = LocalDate.now().minusDays(1)

        val expectedUpdatedCompensation = compensation.copy(
            processingFrom = earliestCutoffDateInPast,
            processingTo = null,
        )

        every { compensationRepository.findById(compensation.id, any()) } returns compensation
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }

        every {
            compensationItemRepository.findEarliestCutoffForCompensation(compensation.id, any())
        } returns earliestCutoffDateInPast
        every { compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any()) } just Runs

        compensationStatusUpdateService.refreshStatus(compensation.id)

        verify {
            compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any())
        }
    }

    @Test
    fun `update processing from and to when last item is generated and no compensation item is processing`() {
        val compensationStartDate = LocalDate.now()
        val compensationEndDate = LocalDate.now().plusMonths(3).minusDays(1)
        val compensation = buildCompensation(
            startDate = compensationStartDate,
            endDate = compensationEndDate,
        )
        val earliestCutoffDateInFuture = LocalDate.now().plusDays(10)
        val lastPayDateUnassigned = null

        val expectedUpdatedCompensation = compensation.copy(
            processingFrom = earliestCutoffDateInFuture,
            processingTo = lastPayDateUnassigned,
        )

        every { compensationRepository.findById(compensation.id, any()) } returns compensation
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }
        every {
            compensationItemRepository.findEarliestCutoffForCompensation(compensation.id, any())
        } returns earliestCutoffDateInFuture
        every {
            compensationItemRepository.findLastCompensationItem(compensation.id, any())
        } returns mockk<CompensationItem> {
            every { endDate } returns compensationEndDate
        }
        every {
            compensationItemRepository.findLastPayDateForCompensation(compensation.id, any())
        } returns lastPayDateUnassigned
        every { compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any()) } just Runs

        compensationStatusUpdateService.refreshStatus(compensation.id)

        verify {
            compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any())
        }
    }

    @Test
    fun `update processing from and to when last item is generated and at least one compensation item is processing`() {
        val compensationStartDate = LocalDate.now().minusMonths(1)
        val compensationEndDate = LocalDate.now().plusMonths(3).minusDays(1)
        val compensation = buildCompensation(
            startDate = compensationStartDate,
            endDate = compensationEndDate,
        )
        val earliestCutoffDateInPast = LocalDate.now().minusDays(10)
        val lastPayDateInUnassigned = null

        val expectedUpdatedCompensation = compensation.copy(
            processingFrom = earliestCutoffDateInPast,
            processingTo = lastPayDateInUnassigned,
        )

        every { compensationRepository.findById(compensation.id, any()) } returns compensation
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }
        every {
            compensationItemRepository.findEarliestCutoffForCompensation(compensation.id, any())
        } returns earliestCutoffDateInPast
        every {
            compensationItemRepository.findLastCompensationItem(compensation.id, any())
        } returns mockk<CompensationItem> {
            every { endDate } returns compensationEndDate
        }
        every {
            compensationItemRepository.findLastPayDateForCompensation(compensation.id, any())
        } returns lastPayDateInUnassigned
        every { compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any()) } just Runs

        compensationStatusUpdateService.refreshStatus(compensation.id)

        verify {
            compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any())
        }
    }

    @Test
    fun `update processing from and to when last item is generated and last compensation item is processing`() {
        val compensationStartDate = LocalDate.now().minusMonths(1)
        val compensationEndDate = LocalDate.now()
        val compensation = buildCompensation(
            startDate = compensationStartDate,
            endDate = compensationEndDate,
        )
        val earliestCutoffDateInPast = LocalDate.now().minusDays(15)
        val lastPayDateInFuture = LocalDate.now().plusDays(5)

        val expectedUpdatedCompensation = compensation.copy(
            processingFrom = earliestCutoffDateInPast,
            processingTo = lastPayDateInFuture,
        )

        every { compensationRepository.findById(compensation.id, any()) } returns compensation
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }
        every {
            compensationItemRepository.findEarliestCutoffForCompensation(compensation.id, any())
        } returns earliestCutoffDateInPast
        every {
            compensationItemRepository.findLastCompensationItem(compensation.id, any())
        } returns mockk<CompensationItem> {
            every { endDate } returns compensationEndDate
        }
        every {
            compensationItemRepository.findLastPayDateForCompensation(compensation.id, any())
        } returns lastPayDateInFuture
        every { compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any()) } just Runs

        compensationStatusUpdateService.refreshStatus(compensation.id)

        verify {
            compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any())
        }
    }

    @Test
    fun `update processing from and to when last item is generated and all compensation items are processed`() {
        val compensationStartDate = LocalDate.now().minusMonths(3)
        val compensationEndDate = LocalDate.now().minusMonths(1)
        val compensation = buildCompensation(
            startDate = compensationStartDate,
            endDate = compensationEndDate,
        )
        val earliestCutoffDateInPast = LocalDate.now().minusDays(75)
        val lastPayDateInPast = LocalDate.now().minusDays(25)

        val expectedUpdatedCompensation = compensation.copy(
            processingFrom = earliestCutoffDateInPast,
            processingTo = lastPayDateInPast,
        )

        every { compensationRepository.findById(compensation.id, any()) } returns compensation
        every { transactional.invoke(any<TransactionContext.() -> Unit>()) } answers {
            firstArg<TransactionContext.() -> Unit>().invoke(mockk<TransactionContext>())
        }
        every {
            compensationItemRepository.findEarliestCutoffForCompensation(compensation.id, any())
        } returns earliestCutoffDateInPast
        every {
            compensationItemRepository.findLastCompensationItem(compensation.id, any())
        } returns mockk<CompensationItem> {
            every { endDate } returns compensationEndDate
        }
        every {
            compensationItemRepository.findLastPayDateForCompensation(compensation.id, any())
        } returns lastPayDateInPast
        every { compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any()) } just Runs

        compensationStatusUpdateService.refreshStatus(compensation.id)

        verify {
            compensationRepository.updateAll(listOf(expectedUpdatedCompensation), any())
        }
    }

    private fun buildCompensation(
        startDate: LocalDate,
        endDate: LocalDate?,
        status: CompensationStatus = CompensationStatus.NEW,
    ) = Compensation(
        id = UUID.randomUUID(),
        companyId = 1000L,
        entityId = 1L,
        contractId = 1L,
        schemaItemId = UUID.randomUUID(),
        category = "category",
        currency = "USD",
        billingRateType = BillingRateType.VALUE,
        billingRate = 100.00,
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = UUID.randomUUID(),
        startDate = startDate,
        endDate = endDate,
        processingFrom = null,
        processingTo = null,
        isInstallment = false,
        noOfInstallments = null,
        status = status,
        generatedInstallments = null,
        processedUntilDate = LocalDate.now(),
        createdOn = LocalDateTime.now(),
        createdBy = -1,
        updatedOn = LocalDateTime.now(),
        updatedBy = -1,
    )
}
