package com.multiplier.compensation.service.validation.compensation.validators

import com.multiplier.compensation.domain.common.contract.ContractOffBoardingStatus
import com.multiplier.compensation.domain.common.contract.ContractOffboarding
import com.multiplier.compensation.domain.compensation.enums.RequestType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.enums.skeletionfield.CommonSkeletonField
import com.multiplier.compensation.service.common.mapper.SkeletonCompensationMapperUtils.toCompensationDraft
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationDataCollector
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.compensation.dto.CompensationDraft
import com.multiplier.compensation.service.compensation.dto.CompensationSkeletonField
import com.multiplier.compensation.service.compensation.dto.CompensationValidatorContext
import com.multiplier.compensation.service.compensation.validation.ContextUseCase
import com.multiplier.compensation.service.compensation.validation.validators.OffBoardingCompensationValidator
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationDraftFixture
import com.multiplier.compensation.service.validation.compensation.utils.fixtures.compensationValidatorContextFixture
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class OffBoardingCompensationValidatorTest {
    private lateinit var validator: OffBoardingCompensationValidator
    private lateinit var collector: ValidationDataCollector<CompensationDraft>
    private lateinit var input: ValidationInputItem

    private val contractId = 50000L
    private val contractIdKeyValuePair = KeyValuePair(
        key = CommonSkeletonField.CONTRACT_ID.id,
        value = contractId.toString(),
    )

    @BeforeEach
    fun setUp() {
        validator = OffBoardingCompensationValidator()

        input = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(contractIdKeyValuePair),
        )

        collector = ValidationDataCollector(
            drafts = mutableMapOf(input.id to input.toCompensationDraft(compensationValidatorContextFixture())),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )
    }

    @Test
    fun `should return false when draft is missing`() {
        val context = compensationValidatorContextFixture()
        val input = ValidationInputItem(id = "1", fieldKeyValuePairs = listOf())
        assertFalse {
            validator.validate(
                input,
                context,
                ValidationDataCollector(inputPlusDerivedRows = emptyMap()),
            )
        }
    }

    @Test
    fun `should return false when contractId is missing`() {
        val context = compensationValidatorContextFixture()
        val input = ValidationInputItem(id = "1", fieldKeyValuePairs = listOf())
        collector.drafts[input.id] = compensationDraftFixture().copy(contractId = null)
        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `should return false when contractOffBoardingContext is null`() {
        val context = compensationValidatorContextFixture()
        collector.drafts[input.id] = compensationDraftFixture(contractId = contractId)
        assertFalse { validator.validate(input, context, collector) }
    }

    @Test
    fun `should return false when offBoarding is already completed`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.COMPLETED,
                    lastWorkingDay = LocalDate.now(),
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val result = validator.validate(input, context, collector)

        assertFalse(result)
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CommonSkeletonField.CONTRACT_ID.id &&
                    it.message == "Contract off boarding is already completed"
            },
        )
    }

    @Test
    fun `should return false when last working day is invalid`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.FNF_PENDING,
                    lastWorkingDay = null,
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val result = validator.validate(input, context, collector)

        assertFalse(result)
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CommonSkeletonField.CONTRACT_ID.id &&
                    it.message == "Contract last working day could not be found"
            },
        )
    }

    @Test
    fun `should return false when installments are more than 1`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.FNF_PENDING,
                    lastWorkingDay = LocalDate.of(2025, 5, 1),
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val inputWithWrongInstallments = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "2",
                ),
                KeyValuePair(
                    CompensationSkeletonField.END_DATE.id,
                    "2025-04-30",
                ),
            ),
        )

        collector = ValidationDataCollector(
            drafts = mutableMapOf(
                inputWithWrongInstallments.id to
                    inputWithWrongInstallments.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        val result = validator.validate(inputWithWrongInstallments, context, collector)

        assertFalse(result)
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id &&
                    it.message == "Number of installments should be 1"
            },
        )
    }

    @Test
    fun `should return false for installment when start date is after last working day of contract`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.FNF_PENDING,
                    lastWorkingDay = LocalDate.of(2025, 5, 1),
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val inputWithWrongInstallments = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "Yes",
                ),
                KeyValuePair(
                    CompensationSkeletonField.NUMBER_OF_INSTALLMENTS.id,
                    "1",
                ),
                KeyValuePair(
                    CompensationSkeletonField.START_DATE.id,
                    "2025-06-30",
                ),
            ),
        )

        collector = ValidationDataCollector(
            drafts = mutableMapOf(
                inputWithWrongInstallments.id to
                    inputWithWrongInstallments.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        val result = validator.validate(inputWithWrongInstallments, context, collector)

        assertFalse(result)
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CompensationSkeletonField.START_DATE.id &&
                    it.message == "Start date should be before last working day"
            },
        )
    }

    @Test
    fun `should return false for non installment when end date is after last working day of contract`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.FNF_PENDING,
                    lastWorkingDay = LocalDate.of(2025, 5, 1),
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val inputWithWrongInstallments = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "No",
                ),
                KeyValuePair(
                    CompensationSkeletonField.END_DATE.id,
                    "2025-02-30",
                ),
                KeyValuePair(
                    CompensationSkeletonField.END_DATE.id,
                    "2025-06-30",
                ),
            ),
        )

        collector = ValidationDataCollector(
            drafts = mutableMapOf(
                inputWithWrongInstallments.id to
                    inputWithWrongInstallments.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        val result = validator.validate(inputWithWrongInstallments, context, collector)

        assertFalse(result)
        assertTrue(
            collector.rowValidationResult[input.id]!!.any {
                it.field.key == CompensationSkeletonField.END_DATE.id &&
                    it.message == "End date should be before last working day"
            },
        )
    }

    @Test
    fun `should return true for non installment when all data is correct`() {
        val context = CompensationValidatorContext(
            entityId = 1L,
            skeleton = mockk(),
            schemaItems = emptyMap(),
            paySchedules = emptyMap(),
            contracts = emptyMap(),
            offBoardingContracts = mapOf(
                contractId to ContractOffboarding(
                    offboardingId = contractId,
                    contractId = contractId,
                    offBoardingStatus = ContractOffBoardingStatus.FNF_PENDING,
                    lastWorkingDay = LocalDate.of(2025, 5, 1),
                ),
            ),
            activeCompContractIds = emptySet(),
            contextUseCase = ContextUseCase.VALIDATE,
            contextSource = RequestType.PAY_SUPPLEMENT,
        )

        val inputWithWrongInstallments = ValidationInputItem(
            id = "1",
            fieldKeyValuePairs = listOf(
                contractIdKeyValuePair,
                KeyValuePair(
                    CompensationSkeletonField.IS_INSTALLMENT.id,
                    "No",
                ),
                KeyValuePair(
                    CompensationSkeletonField.END_DATE.id,
                    "2025-02-30",
                ),
                KeyValuePair(
                    CompensationSkeletonField.END_DATE.id,
                    "2025-04-30",
                ),
            ),
        )

        collector = ValidationDataCollector(
            drafts = mutableMapOf(
                inputWithWrongInstallments.id to
                    inputWithWrongInstallments.toCompensationDraft(compensationValidatorContextFixture()),
            ),
            rowValidationResult = mutableMapOf(),
            inputPlusDerivedRows = emptyMap(),
        )

        val result = validator.validate(inputWithWrongInstallments, context, collector)

        assertTrue(result)
    }
}
