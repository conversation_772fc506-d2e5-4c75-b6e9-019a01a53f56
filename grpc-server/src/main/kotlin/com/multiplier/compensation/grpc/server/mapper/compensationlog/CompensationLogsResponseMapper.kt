package com.multiplier.compensation.grpc.server.mapper.compensationlog

import com.multiplier.compensation.domain.compensationlog.CompensationLogEnriched
import com.multiplier.compensation.grpc.schema.CompensationLog
import com.multiplier.compensation.grpc.schema.CompensationLogsResponse
import com.multiplier.compensation.grpc.server.mapper.common.toGrpc
import com.multiplier.grpc.common.toDate
import com.multiplier.grpc.common.toTimestamp
import com.multiplier.grpc.common.v1.toGrpc

fun List<CompensationLogEnriched>.toGrpc(): CompensationLogsResponse = CompensationLogsResponse.newBuilder().apply {
    addAllItems(<EMAIL> { it.toGrpc() })
}.build()

fun CompensationLogEnriched.toGrpc(): CompensationLog = CompensationLog.newBuilder().apply {
    id = <EMAIL>()
    entityId = <EMAIL>
    contractId = <EMAIL>
    compensationName = <EMAIL>
    compensationCategory = <EMAIL>
    currency = <EMAIL>
    billingRateType = <EMAIL>()
    billingRate = <EMAIL> ?: 0.0
    billingFrequency = <EMAIL>()
    payScheduleName = <EMAIL>
    isInstallment = <EMAIL>
    startDate = <EMAIL>()
    <EMAIL>?.let { endDate = it.toDate() }
    <EMAIL>?.let { numberOfInstallments = it }
    requestType = <EMAIL>()
    requestId = <EMAIL>
    isTaxable = <EMAIL>
    isFixed = <EMAIL>
    isProrated = <EMAIL>
    isMandatory = <EMAIL>
    isPartOfBasePay = <EMAIL>
    createdOn = <EMAIL>()
    createdBy = <EMAIL>
    updatedOn = <EMAIL>()
    updatedBy = <EMAIL>
}.build()
