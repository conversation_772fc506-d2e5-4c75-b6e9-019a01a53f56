package com.multiplier.compensation.database.repository.compensationschema

import com.multiplier.compensation.database.enums.CountryCode
import com.multiplier.compensation.database.mapper.common.toDatabase
import com.multiplier.compensation.database.mapper.common.toDomain
import com.multiplier.compensation.database.mapper.compensationschema.toDatabase
import com.multiplier.compensation.database.tables.records.CompensationSchemaRecord
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA
import com.multiplier.compensation.database.tables.references.COMPENSATION_SCHEMA_ITEM
import com.multiplier.compensation.domain.common.BillingFrequency
import com.multiplier.compensation.domain.common.BillingRateType
import com.multiplier.compensation.domain.common.CategoryConstants
import com.multiplier.compensation.domain.common.ConfigurationScope
import com.multiplier.compensation.domain.common.ConfigurationScopeConstants.SCOPE_COUNTRY_DUMMY_ENTITY_ID
import com.multiplier.compensation.domain.common.ItemType
import com.multiplier.compensation.domain.common.exception.InvalidArgumentException
import com.multiplier.compensation.domain.compensationschema.CompensationSchema
import com.multiplier.compensation.domain.compensationschema.CompensationSchemaItem
import com.multiplier.transaction.database.jooq.audit.Audit
import com.multiplier.transaction.database.jooq.transaction.TransactionContext
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.jooq.DSLContext
import org.jooq.JSONB
import org.jooq.RecordMapper
import org.jooq.Result
import org.jooq.SelectConditionStep
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDateTime
import java.util.UUID

@ExtendWith(MockKExtension::class)
class CompensationSchemaRepositoryTest {
    private lateinit var audit: Audit
    private lateinit var dsl: DSLContext
    private lateinit var transactionContext: TransactionContext
    private lateinit var repository: CompensationSchemaRepository
    private lateinit var compensationSchemaItemRepository: CompensationSchemaItemRepository

    @BeforeEach
    fun setUp() {
        audit = mockk()
        dsl = mockk()
        transactionContext = mockk()
        compensationSchemaItemRepository = mockk()
        repository = CompensationSchemaRepository(dsl, compensationSchemaItemRepository)
        every { transactionContext.trx } returns dsl
        every { transactionContext.audit } returns audit
        every { audit.userId } returns -1
        every { audit.revision } returns UUID.randomUUID()
        every { audit.time } returns LocalDateTime.now()
    }

    @Test
    fun `should save all entries for entity level schemas creation`() {
        val entityId = 1L
        every {
            transactionContext.trx.batchInsert(any<Collection<CompensationSchemaRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        val schemas: Collection<CompensationSchema> = listOf(mockedCompensationSchema())
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationSchemaRecord>>()

        every {
            transactionContext.trx.selectFrom(COMPENSATION_SCHEMA)
                .where(
                    COMPENSATION_SCHEMA.ENTITY_ID.eq(SCOPE_COUNTRY_DUMMY_ENTITY_ID)
                        .and(COMPENSATION_SCHEMA.NAME.`in`("name"))
                        .and(COMPENSATION_SCHEMA.COUNTRY.`in`(CountryCode.USA)),
                )
        } returns mockSelectConditionStep

        val expectedConflictingSchemasResult: Result<CompensationSchemaRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(COMPENSATION_SCHEMA)
        expectedConflictingSchemasResult.addAll(emptyList())

        every { mockSelectConditionStep.fetch() } returns expectedConflictingSchemasResult

        repository.saveAllEnforcingConstraints(
            schemas,
            transactionContext,
        )

        verify(exactly = 2) {
            transactionContext.audit
        }

        verify(exactly = 3) {
            transactionContext.trx
        }

        verify {
            transactionContext.trx.batchInsert(any<Collection<CompensationSchemaRecord>>()).execute()
        }
    }

    @Test
    fun `should fail during entity level schemas creation if conflicting schemas preexist`() {
        val entityId = 1L
        every {
            transactionContext.trx.batchInsert(any<Collection<CompensationSchemaRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        val schemas: Collection<CompensationSchema> = listOf(mockedCompensationSchema())
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationSchemaRecord>>()

        every {
            transactionContext.trx.selectFrom(COMPENSATION_SCHEMA)
                .where(
                    COMPENSATION_SCHEMA.ENTITY_ID.eq(SCOPE_COUNTRY_DUMMY_ENTITY_ID)
                        .and(COMPENSATION_SCHEMA.NAME.`in`("name"))
                        .and(COMPENSATION_SCHEMA.COUNTRY.`in`(CountryCode.USA)),
                )
        } returns mockSelectConditionStep

        val expectedConflictingSchemasResult: Result<CompensationSchemaRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(COMPENSATION_SCHEMA)
        expectedConflictingSchemasResult.addAll(
            setOf(mockedCompensationSchema(entityId = SCOPE_COUNTRY_DUMMY_ENTITY_ID).toDatabase()),
        )

        every { mockSelectConditionStep.fetch() } returns expectedConflictingSchemasResult

        assertThrows<InvalidArgumentException> {
            repository.saveAllEnforcingConstraints(
                schemas,
                transactionContext,
            )
        }
    }

    @Test
    fun `should save all entries for default country level schemas creation`() {
        val defaultEntityId = SCOPE_COUNTRY_DUMMY_ENTITY_ID
        every {
            transactionContext.trx.batchInsert(any<Collection<CompensationSchemaRecord>>()).execute()
        } returns intArrayOf(defaultEntityId.toInt())

        val schemas: Collection<CompensationSchema> = listOf(
            mockedCompensationSchema(
                entityId = defaultEntityId,
            ),
        )
        val mockSelectConditionStep = mockk<SelectConditionStep<CompensationSchemaRecord>>()

        every {
            transactionContext.trx.selectFrom(COMPENSATION_SCHEMA)
                .where(
                    COMPENSATION_SCHEMA.ENTITY_ID.notEqual(SCOPE_COUNTRY_DUMMY_ENTITY_ID)
                        .and(COMPENSATION_SCHEMA.NAME.`in`("name"))
                        .and(COMPENSATION_SCHEMA.COUNTRY.`in`(CountryCode.USA)),
                )
        } returns mockSelectConditionStep

        val expectedConflictingSchemasResult: Result<CompensationSchemaRecord> = DSL.using(
            DefaultConfiguration(),
        ).newResult(COMPENSATION_SCHEMA)
        expectedConflictingSchemasResult.addAll(emptyList())

        every { mockSelectConditionStep.fetch() } returns expectedConflictingSchemasResult

        repository.saveAllEnforcingConstraints(
            schemas,
            transactionContext,
        )

        verify(exactly = 2) {
            transactionContext.audit
        }

        verify(exactly = 3) {
            transactionContext.trx
        }

        verify {
            transactionContext.trx.batchInsert(any<Collection<CompensationSchemaRecord>>()).execute()
        }
    }

    @Test
    fun `should update all entries`() {
        val entityId = 1L
        every {
            transactionContext.trx.batchUpdate(any<List<CompensationSchemaRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        every {
            transactionContext.trx.batchInsert(any<List<CompensationSchemaRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        val schemas: Collection<CompensationSchema> = listOf(mockedCompensationSchema())

        repository.updateAll(
            schemas,
            transactionContext,
        )

        verify { transactionContext.trx.batchUpdate(any<List<CompensationSchemaRecord>>()).execute() }
    }

    @Test
    fun `should delete all entries`() {
        val entityId = 1L
        every {
            transactionContext.trx.batchUpdate(any<List<CompensationSchemaRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        every {
            transactionContext.trx.batchInsert(any<List<CompensationSchemaRecord>>()).execute()
        } returns intArrayOf(entityId.toInt())

        val schemas: Collection<CompensationSchema> = listOf(mockedCompensationSchema())

        repository.deleteAll(
            schemas,
            transactionContext,
        )

        verify { transactionContext.trx.batchUpdate(any<List<CompensationSchemaRecord>>()).execute() }
        verify { transactionContext.trx.batchInsert(any<List<CompensationSchemaRecord>>()).execute() }
    }

    @Test
    fun `should find by entity ID and name`() {
        val entityId = 1L
        val schemaName = "DefaultSchema"
        val schemaId = UUID.randomUUID()

        val schemaRecord = CompensationSchemaRecord(
            schemaId,
            entityId,
            CountryCode.USA,
            1,
            true,
            schemaName,
            true,
            LocalDateTime.now(),
            -1,
            LocalDateTime.now(),
            -1,
            JSONB.valueOf("""["GLOBAL_PAYROLL"]"""),
            ConfigurationScope.COMPANY.toDatabase(),
        )

        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(COMPENSATION_SCHEMA.ENTITY_ID.eq(entityId))
                .and(COMPENSATION_SCHEMA.COUNTRY.eq(CountryCode.USA))
                .and(COMPENSATION_SCHEMA.NAME.eq(schemaName))
                .and(COMPENSATION_SCHEMA.IS_ACTIVE.isTrue)
                .fetchOne()
        } returns schemaRecord

        every { compensationSchemaItemRepository.findAllBySchemaId(schemaId, true) } returns emptyList()

        val result = repository.findByEntityIdAndCountryCodeAndName(entityId, schemaName, CountryCode.USA, true)

        assert(result != null)
        assertEquals("DefaultSchema", result!!.name)
    }

    @Test
    fun `should return null if schema with given name not found`() {
        val entityId = 1L
        val schemaName = "NonExistentSchema"

        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(COMPENSATION_SCHEMA.ENTITY_ID.eq(entityId))
                .and(COMPENSATION_SCHEMA.COUNTRY.eq(CountryCode.USA))
                .and(COMPENSATION_SCHEMA.NAME.eq(schemaName))
                .and(COMPENSATION_SCHEMA.IS_ACTIVE.isTrue)
                .fetchOne()
        } returns null

        val result = repository.findByEntityIdAndCountryCodeAndName(entityId, schemaName, CountryCode.USA, true)

        assertNull(result)
    }

    @Test
    fun `should find schemas by entity ID and country code`() {
        val entityId = 1L
        val countryCode = CountryCode.USA
        val schemaId = UUID.randomUUID()
        val schemaItem = mockedCompensationSchemaItem(schemaId)
        val schema = mockedCompensationSchema(
            schemaID = schemaId,
            schemaItems = listOf(schemaItem),
        )
        val mockFetchResult: Result<CompensationSchemaRecord> = mockk()
        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(
                    COMPENSATION_SCHEMA.ENTITY_ID.eq(entityId)
                        .and(COMPENSATION_SCHEMA.COUNTRY.eq(countryCode))
                        .and(COMPENSATION_SCHEMA.IS_ACTIVE.isTrue),
                )
                .fetch()
        } returns mockFetchResult
        every {
            mockFetchResult.map(any<RecordMapper<CompensationSchemaRecord, CompensationSchema>>())
        } answers {
            listOf(schema)
        }

        every { compensationSchemaItemRepository.findAllBySchemaId(any(), any()) } returns
            listOf(schemaItem)

        val result = repository.findSchemasByEntityCountry(entityId, countryCode)

        assert(result.isNotEmpty())
        assertEquals(1, result.size)
        assertEquals("name", result.first().name)
        assertEquals(1, result.first().schemaItems.size)
    }

    @Test
    fun `should return schemas for valid schemaItemIds`() {
        val schemaItemId = UUID.randomUUID()
        val schemaId = UUID.randomUUID()

        val schema = mockedCompensationSchema(
            schemaID = schemaId,
        )

        val schemaIds = listOf(schemaId)

        every {
            dsl.selectDistinct(COMPENSATION_SCHEMA_ITEM.SCHEMA_ID)
                .from(COMPENSATION_SCHEMA_ITEM)
                .where(COMPENSATION_SCHEMA_ITEM.ID.`in`(listOf(schemaItemId)))
                .fetch(COMPENSATION_SCHEMA_ITEM.SCHEMA_ID)
        } returns schemaIds

        val mockFetchResult: Result<CompensationSchemaRecord> = mockk()

        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(COMPENSATION_SCHEMA.ID.`in`(schemaIds))
                .fetch()
        } returns mockFetchResult
        every {
            mockFetchResult.map(any<RecordMapper<CompensationSchemaRecord, CompensationSchema>>())
        } answers {
            listOf(schema)
        }

        every { compensationSchemaItemRepository.findAllBySchemaId(schemaId) } returns emptyList()

        val result = repository.findSchemasBySchemaItemIds(listOf(schemaItemId))

        assertEquals(1, result.size)
        assertEquals(schemaId, result.first().id)
    }

    private fun mockedCompensationSchema(
        entityId: Long = 1L,
        companyId: Long = 1L,
        schemaID: UUID = UUID.randomUUID(),
        schemaItems: List<CompensationSchemaItem> = emptyList(),
    ) = CompensationSchema(
        id = schemaID,
        entityId = entityId,
        country = CountryCode.USA.toDomain(),
        companyId = companyId,
        isDefault = true,
        name = "name",
        isActive = true,
        createdOn = LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1L,
        schemaItems = schemaItems,
        tags = listOf("GLOBAL_PAYROLL"),
        configurationScope = ConfigurationScope.COMPANY,
        description = "Test schema description",
    )

    @Test
    fun `should return empty list when no schema IDs are found`() {
        val schemaItemId = UUID.randomUUID()

        every {
            dsl.selectDistinct(COMPENSATION_SCHEMA_ITEM.SCHEMA_ID)
                .from(COMPENSATION_SCHEMA_ITEM)
                .where(COMPENSATION_SCHEMA_ITEM.ID.`in`(listOf(schemaItemId)))
                .fetch(COMPENSATION_SCHEMA_ITEM.SCHEMA_ID)
        } returns emptyList()

        val result = repository.findSchemasBySchemaItemIds(listOf(schemaItemId))

        assertTrue(result.isEmpty())
    }

    @Test
    fun `findSchemaById should return schema when schema exists`() {
        // Given
        val schemaId = UUID.randomUUID()
        val schemaRecord = CompensationSchemaRecord(
            schemaId,
            1L,
            CountryCode.USA,
            1,
            true,
            "TestSchema",
            true,
            LocalDateTime.now(),
            -1,
            LocalDateTime.now(),
            -1,
            JSONB.valueOf("""["GLOBAL_PAYROLL"]"""),
            ConfigurationScope.COMPANY.toDatabase(),
        )

        // Mock the query building
        val mockQuery = mockk<org.jooq.SelectConditionStep<CompensationSchemaRecord>>()
        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(COMPENSATION_SCHEMA.ID.eq(schemaId))
        } returns mockQuery

        every { mockQuery.and(COMPENSATION_SCHEMA.IS_ACTIVE.isTrue) } returns mockQuery
        every { mockQuery.fetchSingle() } returns schemaRecord

        every { compensationSchemaItemRepository.findAllBySchemaId(schemaId, true) } returns emptyList()

        // When
        val result = repository.findSchemaById(schemaId)

        // Then
        assertNotNull(result)
        assertEquals(schemaId, result.id)
        assertEquals("TestSchema", result.name)
        assertEquals(CountryCode.USA.toDomain(), result.country)
    }

    @Test
    fun `findSchemaById should not filter inactive items when excludeInactiveItems is false`() {
        // Given
        val schemaId = UUID.randomUUID()
        val schemaRecord = CompensationSchemaRecord(
            schemaId,
            1L,
            CountryCode.USA,
            1,
            true,
            "TestSchema",
            false, // Inactive schema
            LocalDateTime.now(),
            -1,
            LocalDateTime.now(),
            -1,
            JSONB.valueOf("""["GLOBAL_PAYROLL"]"""),
            ConfigurationScope.COMPANY.toDatabase(),
        )

        // Mock the query building
        val mockQuery = mockk<org.jooq.SelectConditionStep<CompensationSchemaRecord>>()
        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(COMPENSATION_SCHEMA.ID.eq(schemaId))
        } returns mockQuery

        // No IS_ACTIVE filter should be applied
        every { mockQuery.fetchSingle() } returns schemaRecord

        every { compensationSchemaItemRepository.findAllBySchemaId(schemaId, false) } returns emptyList()

        // When
        val result = repository.findSchemaById(schemaId, excludeInactiveItems = false)

        // Then
        assertNotNull(result)
        assertEquals(schemaId, result.id)
        assertEquals("TestSchema", result.name)
        assertEquals(CountryCode.USA.toDomain(), result.country)
        assertFalse(result.isActive)
    }

    @Test
    fun `should find schema by ID`() {
        val schemaId = UUID.randomUUID()

        val schemaItem = mockedCompensationSchemaItem(schemaId)
        val expectedSchema = mockedCompensationSchema(
            schemaID = schemaId,
            schemaItems = listOf(schemaItem),
        )

        val record: CompensationSchemaRecord = expectedSchema.toDatabase()

        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(COMPENSATION_SCHEMA.ID.eq(schemaId))
                .fetchOne()
        } returns record

        every {
            compensationSchemaItemRepository.findAllBySchemaId(schemaId, true)
        } returns listOf(schemaItem)

        val result = repository.findById(schemaId)

        assertNotNull(result)
        assertEquals(expectedSchema.id, result!!.id)
        assertEquals(expectedSchema.name, result.name)
        assertEquals(expectedSchema.entityId, result.entityId)
        assertEquals(expectedSchema.country, result.country)
        assertEquals(expectedSchema.schemaItems.size, result.schemaItems.size)
    }

    @Test
    fun `should return null when schema ID is unknown`() {
        val unknownId = UUID.randomUUID()

        every {
            dsl.selectFrom(COMPENSATION_SCHEMA)
                .where(COMPENSATION_SCHEMA.ID.eq(unknownId))
                .fetchOne()
        } returns null

        val result = repository.findById(unknownId)

        assertNull(result)
    }

    private fun mockedCompensationSchemaItem(schemaID: UUID = UUID.randomUUID()) = CompensationSchemaItem(
        id = UUID.randomUUID(),
        schemaId = schemaID,
        componentName = "Base Salary",
        category = CategoryConstants.CATEGORY_CONTRACT_BASE_PAY,
        isTaxable = true,
        isProrated = false,
        isFixed = true,
        isActive = true,
        isMandatory = false,
        isPartOfBasePay = true,
        isPartOfCtc = true,
        createdOn = LocalDateTime.now(),
        createdBy = 1L,
        updatedOn = LocalDateTime.now(),
        updatedBy = 1L,
        label = "Base Salary",
        itemType = ItemType.INPUT,
        validation = null,
        calculation = null,
        billingRateType = BillingRateType.VALUE,
        isOvertimeEligible = false,
        description = "Test component description",
        billingFrequency = BillingFrequency.MONTHLY,
        payScheduleId = null,
        currency = "USD",
    )
}
