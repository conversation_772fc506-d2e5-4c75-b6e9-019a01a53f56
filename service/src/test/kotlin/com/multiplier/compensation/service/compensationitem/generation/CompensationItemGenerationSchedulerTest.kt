package com.multiplier.compensation.service.compensationitem.generation

import com.multiplier.compensation.service.compensationitem.dto.CompensationItemGenerationRequest
import com.multiplier.compensation.service.compensationitem.facades.CompensationItemGenerationFacade
import io.mockk.Called
import io.mockk.Runs
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestCoroutineScheduler
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Duration
import java.time.LocalDate

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CompensationItemGenerationSchedulerTest {
    private val generationFacade: CompensationItemGenerationFacade = mockk(relaxed = true)
    private val testScheduler = TestCoroutineScheduler()
    private val dispatcher = StandardTestDispatcher(testScheduler)

    private lateinit var scheduler: CompensationItemGenerationScheduler

    @BeforeEach
    fun setup() {
        scheduler = CompensationItemGenerationScheduler(
            generationFacade = generationFacade,
            jobName = "TestJob",
            enabled = true,
            generationPeriodInDays = 30,
            lockAtMostFor = "PT2M",
            dispatcher = dispatcher,
        )
    }

    @AfterEach
    fun teardown() {
        clearMocks(generationFacade)
    }

    @Test
    fun `should not execute job when scheduler is disabled`() {
        scheduler = CompensationItemGenerationScheduler(
            generationFacade = generationFacade,
            jobName = "TestJob",
            enabled = false,
            generationPeriodInDays = 30,
            lockAtMostFor = "PT2M",
            dispatcher = dispatcher,
        )

        scheduler.generateCompensationItems()

        verify { generationFacade wasNot Called }
    }

    @Test
    fun `should execute job and call generation facade`() = runTest {
        scheduler.executeJob()

        coVerify {
            generationFacade.generateCompensationItems(
                match { it.toDate == LocalDate.now().plusDays(30) && it.shouldPersist },
                any(),
            )
        }
    }

    @Test
    fun `should build request with correct date and persistence`() = runTest {
        val requestSlot = slot<CompensationItemGenerationRequest>()

        coEvery { generationFacade.generateCompensationItems(capture(requestSlot), any()) } just Runs

        scheduler.executeJob()

        val capturedRequest = requestSlot.captured
        Assertions.assertEquals(LocalDate.now().plusDays(30), capturedRequest.toDate)
        assertTrue(capturedRequest.shouldPersist)
    }

    @Test
    fun `should handle timeout and invoke exceptionHandler`() = runTest {
        val lockTimeoutMillis = Duration.parse("PT2S").toMillis()

        scheduler = CompensationItemGenerationScheduler(
            generationFacade = generationFacade,
            jobName = "TestJob",
            enabled = true,
            generationPeriodInDays = 30,
            lockAtMostFor = "PT2S",
            dispatcher = dispatcher,
        )

        coEvery {
            generationFacade.generateCompensationItems(any(), any())
        } coAnswers {
            delay(lockTimeoutMillis + 500)
        }

        var capturedException: Throwable? = null
        scheduler.exceptionHandler = { capturedException = it }
        scheduler.generateCompensationItems()

        coVerify {
            generationFacade.generateCompensationItems(any(), any())
        }
        assertNotNull(capturedException)
        assertTrue(capturedException is TimeoutCancellationException)
    }

    @Test
    fun `should handle timeout without exceptionHandler`() = runTest {
        val lockTimeoutMillis = Duration.parse("PT2S").toMillis()

        scheduler = CompensationItemGenerationScheduler(
            generationFacade = generationFacade,
            jobName = "TestJob",
            enabled = true,
            generationPeriodInDays = 30,
            lockAtMostFor = "PT2S",
            dispatcher = dispatcher,
        )

        coEvery {
            generationFacade.generateCompensationItems(any(), any())
        } coAnswers {
            delay(lockTimeoutMillis + 500)
        }

        scheduler.generateCompensationItems()

        coVerify { generationFacade.generateCompensationItems(any(), any()) }

        // No assertions for exceptionHandler since it's null
        // The test ensures no crash or behavior issue when exceptionHandler is null
    }

    @Test
    fun `should cancel job on shutdown`() {
        scheduler.onShutdown()

        assertTrue(scheduler.coroutineContext[Job]?.isCancelled == true)
    }
}
