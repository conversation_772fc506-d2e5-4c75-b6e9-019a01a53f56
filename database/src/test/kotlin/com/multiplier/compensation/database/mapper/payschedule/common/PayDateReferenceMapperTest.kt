package com.multiplier.compensation.database.mapper.payschedule.common

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.multiplier.compensation.domain.payschedule.enums.PayDateReference
import org.junit.jupiter.api.Test
import com.multiplier.compensation.database.enums.PayDateReference as DatabasePayDateReference

class PayDateReferenceMapperTest {
    @Test
    fun `should map PAY_SCHEDULE_START_DATE domain to database correctly`() {
        val domainEnum = PayDateReference.PAY_SCHEDULE_START_DATE
        val expectedDatabaseEnum = DatabasePayDateReference.PAY_SCHEDULE_START_DATE
        assertThat(domainEnum.toDatabase()).isEqualTo(expectedDatabaseEnum)
    }

    @Test
    fun `should map PAY_SCHEDULE_END_DATE domain to database correctly`() {
        val domainEnum = PayDateReference.PAY_SCHEDULE_END_DATE
        val expectedDatabaseEnum = DatabasePayDateReference.PAY_SCHEDULE_END_DATE
        assertThat(domainEnum.toDatabase()).isEqualTo(expectedDatabaseEnum)
    }

    @Test
    fun `should map COMPENSATION_START_DATE domain to database correctly`() {
        val domainEnum = PayDateReference.COMPENSATION_START_DATE
        val expectedDatabaseEnum = DatabasePayDateReference.COMPENSATION_START_DATE
        assertThat(domainEnum.toDatabase()).isEqualTo(expectedDatabaseEnum)
    }

    @Test
    fun `should map COMPENSATION_END_DATE domain to database correctly`() {
        val domainEnum = PayDateReference.COMPENSATION_END_DATE
        val expectedDatabaseEnum = DatabasePayDateReference.COMPENSATION_END_DATE
        assertThat(domainEnum.toDatabase()).isEqualTo(expectedDatabaseEnum)
    }

    @Test
    fun `should map PAY_SCHEDULE_START_DATE database to domain correctly`() {
        val databaseEnum = DatabasePayDateReference.PAY_SCHEDULE_START_DATE
        val expectedDomainEnum = PayDateReference.PAY_SCHEDULE_START_DATE
        assertThat(databaseEnum.toDomain()).isEqualTo(expectedDomainEnum)
    }

    @Test
    fun `should map PAY_SCHEDULE_END_DATE database to domain correctly`() {
        val databaseEnum = DatabasePayDateReference.PAY_SCHEDULE_END_DATE
        val expectedDomainEnum = PayDateReference.PAY_SCHEDULE_END_DATE
        assertThat(databaseEnum.toDomain()).isEqualTo(expectedDomainEnum)
    }

    @Test
    fun `should map COMPENSATION_START_DATE database to domain correctly`() {
        val databaseEnum = DatabasePayDateReference.COMPENSATION_START_DATE
        val expectedDomainEnum = PayDateReference.COMPENSATION_START_DATE
        assertThat(databaseEnum.toDomain()).isEqualTo(expectedDomainEnum)
    }

    @Test
    fun `should map COMPENSATION_END_DATE database to domain correctly`() {
        val databaseEnum = DatabasePayDateReference.COMPENSATION_END_DATE
        val expectedDomainEnum = PayDateReference.COMPENSATION_END_DATE
        assertThat(databaseEnum.toDomain()).isEqualTo(expectedDomainEnum)
    }
}
