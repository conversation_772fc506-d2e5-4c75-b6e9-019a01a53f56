package com.multiplier.compensation.service.common.validationpipeline.common.validators

import com.multiplier.compensation.domain.skeleton.Skeleton
import com.multiplier.compensation.domain.skeleton.SkeletonData
import com.multiplier.compensation.domain.skeleton.ValidationRegex
import com.multiplier.compensation.domain.skeleton.enums.ValueType
import com.multiplier.compensation.service.common.dto.KeyValuePair
import com.multiplier.compensation.service.common.utils.toTitleCase
import com.multiplier.compensation.service.common.validationpipeline.dto.CellValidationResult
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationInputItem
import com.multiplier.compensation.service.common.validationpipeline.dto.ValidationResultType
import org.springframework.stereotype.Component

@Component
class SkeletonValidator {
    fun validate(
        input: ValidationInputItem,
        skeleton: Skeleton,
    ): MutableList<CellValidationResult> {
        val fieldPossibleValueMap = getPossibleValueMap(skeleton.data)
        val requiredPlatformKeys = skeleton.keys.map { it.name }
        val fieldDataTypeMap = getFieldDataTypeMap(skeleton.data)
        val fieldRegexMap = getFieldRegexMap(skeleton.data)
        val fieldMandatoryStatusMap = getFieldMandatoryStatusMap(skeleton.data)
        val cellValidationResults = mutableListOf<CellValidationResult>()
        val skeletonFieldDataMap = skeleton.data.associateBy { it.fieldId }

        validateMandatorySkeletonKeysPresent(
            fieldMandatoryStatusMap = fieldMandatoryStatusMap,
            input = input,
            requiredPlatformKeys = requiredPlatformKeys,
            cellValidationResults = cellValidationResults,
            skeletonFieldDataMap = skeletonFieldDataMap,
        )

        for (fieldValuePair in input.fieldKeyValuePairs) {
            if (
                validateSkeletonKeys(
                    fieldValuePair = fieldValuePair,
                    fieldPossibleValueMap = fieldPossibleValueMap,
                    platformKeys = requiredPlatformKeys,
                )
            ) {
                continue
            }

            validateDataTypeFormat(
                fieldDataTypeMap = fieldDataTypeMap,
                fieldValuePair = fieldValuePair,
                cellValidationResults = cellValidationResults,
                skeletonFieldDataMap = skeletonFieldDataMap,
            )
            validatePossibleValues(
                fieldMandatoryStatusMap = fieldMandatoryStatusMap,
                fieldPossibleValueMap = fieldPossibleValueMap,
                fieldValuePair = fieldValuePair,
                cellValidationResults = cellValidationResults,
                skeletonFieldDataMap = skeletonFieldDataMap,
            )
            validateRegex(
                fieldRegexMap = fieldRegexMap,
                fieldValuePair = fieldValuePair,
                cellValidationResults = cellValidationResults,
                skeletonFieldDataMap = skeletonFieldDataMap,
            )
            validateMandatoryValues(
                fieldMandatoryStatusMap = fieldMandatoryStatusMap,
                fieldValuePair = fieldValuePair,
                cellValidationResults = cellValidationResults,
                skeletonFieldDataMap = skeletonFieldDataMap,
            )
        }

        return cellValidationResults
    }

    private fun validateMandatorySkeletonKeysPresent(
        fieldMandatoryStatusMap: Map<String, Boolean>,
        input: ValidationInputItem,
        requiredPlatformKeys: List<String>,
        cellValidationResults: MutableList<CellValidationResult>,
        skeletonFieldDataMap: Map<String, SkeletonData>,
    ) {
        fieldMandatoryStatusMap.filter { it.value }.keys.plus(requiredPlatformKeys).map { skeletonKey ->
            if (skeletonKey !in input.fieldKeyValuePairs.map { it.key }) {
                val fieldLabel = getFieldLabelByKey(skeletonFieldDataMap, skeletonKey)
                cellValidationResults.add(
                    CellValidationResult(
                        field = KeyValuePair(skeletonKey, null),
                        type = ValidationResultType.ERROR,
                        message = "$fieldLabel is missing",
                    ),
                )
            }
        }
    }

    private fun validateSkeletonKeys(
        fieldValuePair: KeyValuePair,
        fieldPossibleValueMap: Map<String, List<String>?>,
        platformKeys: List<String>,
    ): Boolean = fieldValuePair.key !in fieldPossibleValueMap.keys.plus(platformKeys)

    private fun validatePossibleValues(
        fieldMandatoryStatusMap: Map<String, Boolean>,
        fieldPossibleValueMap: Map<String, List<String>?>,
        fieldValuePair: KeyValuePair,
        cellValidationResults: MutableList<CellValidationResult>,
        skeletonFieldDataMap: Map<String, SkeletonData>,
    ) {
        val isMandatory = fieldMandatoryStatusMap[fieldValuePair.key] == true
        val possibleValues = fieldPossibleValueMap[fieldValuePair.key]
        val isValuePresent = possibleValues.orEmpty().contains(fieldValuePair.value)
        if (possibleValues != null && !isValuePresent) {
            if (isMandatory || (fieldValuePair.value != null)) {
                val fieldLabel = getFieldLabelByKey(skeletonFieldDataMap, fieldValuePair.key)
                cellValidationResults.add(
                    CellValidationResult(
                        field = fieldValuePair,
                        type = ValidationResultType.ERROR,
                        message = "${fieldValuePair.value} is not a valid value for $fieldLabel",
                    ),
                )
            }
        }
    }

    private fun validateDataTypeFormat(
        fieldDataTypeMap: Map<String, DataTypeFormat>,
        fieldValuePair: KeyValuePair,
        cellValidationResults: MutableList<CellValidationResult>,
        skeletonFieldDataMap: Map<String, SkeletonData>,
    ) {
        fieldDataTypeMap[fieldValuePair.key]?.let { dataTypeFormat ->
            if (
                fieldValuePair.value != null &&
                !regexMatch(
                    regex = dataTypeFormat.regex,
                    value = fieldValuePair.value,
                )
            ) {
                val fieldLabel = getFieldLabelByKey(skeletonFieldDataMap, fieldValuePair.key)
                cellValidationResults.add(
                    CellValidationResult(
                        field = fieldValuePair,
                        type = ValidationResultType.ERROR,
                        message = "${fieldValuePair.value} is not a valid data type format for $fieldLabel. " +
                            "Expected format: ${dataTypeFormat.description}",
                    ),
                )
            }
        }
    }

    private fun validateRegex(
        fieldRegexMap: Map<String, ValidationRegex?>,
        fieldValuePair: KeyValuePair,
        cellValidationResults: MutableList<CellValidationResult>,
        skeletonFieldDataMap: Map<String, SkeletonData>,
    ) {
        if (
            fieldRegexMap[fieldValuePair.key] != null &&
            fieldValuePair.value != null &&
            !regexMatch(
                regex = fieldRegexMap[fieldValuePair.key]?.regex.orEmpty(),
                value = fieldValuePair.value,
            )
        ) {
            val fieldLabel = getFieldLabelByKey(skeletonFieldDataMap, fieldValuePair.key)
            cellValidationResults.add(
                CellValidationResult(
                    field = fieldValuePair,
                    type = ValidationResultType.ERROR,
                    message = "${fieldValuePair.value} is not a valid value for $fieldLabel. " +
                        fieldRegexMap[fieldValuePair.key]?.errorMessage.orEmpty(),
                ),
            )
        }
    }

    private fun validateMandatoryValues(
        fieldMandatoryStatusMap: Map<String, Boolean>,
        fieldValuePair: KeyValuePair,
        cellValidationResults: MutableList<CellValidationResult>,
        skeletonFieldDataMap: Map<String, SkeletonData>,
    ) {
        if (
            fieldMandatoryStatusMap[fieldValuePair.key] == true &&
            fieldValuePair.value == null
        ) {
            val fieldLabel = getFieldLabelByKey(skeletonFieldDataMap, fieldValuePair.key)
            cellValidationResults.add(
                CellValidationResult(
                    field = fieldValuePair,
                    type = ValidationResultType.ERROR,
                    message = "$fieldLabel is a mandatory field and should not be null.",
                ),
            )
        }
    }

    private fun getFieldLabelByKey(
        skeletonFieldDataMap: Map<String, SkeletonData>,
        key: String,
    ) = (skeletonFieldDataMap[key]?.fieldName ?: key).toTitleCase()

    private fun getPossibleValueMap(skeletonData: List<SkeletonData>) = skeletonData.associate {
        it.fieldId to it.possibleValues
    }

    private fun getFieldDataTypeMap(skeletonData: List<SkeletonData>) = skeletonData.associate {
        it.fieldId to it.valueType.dataTypeValidationFormats()
    }

    private fun getFieldRegexMap(skeletonData: List<SkeletonData>) = skeletonData.associate {
        it.fieldId to it.validationRegex
    }

    private fun getFieldMandatoryStatusMap(skeletonData: List<SkeletonData>) = skeletonData.associate {
        it.fieldId to it.mandatory
    }
}

data class DataTypeFormat(
    val regex: String,
    val description: String,
)

private fun ValueType.dataTypeValidationFormats() = when (this) {
    ValueType.DATE -> DataTypeFormat(
        regex = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$",
        description = "YYYY-MM-DD",
    )

    ValueType.BOOLEAN -> DataTypeFormat(
        regex = "^(true|false|Yes|No)$",
        description = "true/false or Yes/No",
    )

    ValueType.INTEGER -> DataTypeFormat(
        regex = "^-?\\d+$",
        description = "any whole number",
    )

    ValueType.DOUBLE -> DataTypeFormat(
        regex = "^-?\\d+(\\.\\d+)?([eE][-+]?\\d+)?$",
        description = "any decimal number including scientific notation",
    )

    else -> DataTypeFormat(
        regex = "[\\s\\S]*",
        description = "any text",
    )
}

private fun regexMatch(
    regex: String,
    value: String,
): Boolean = Regex(regex).matches(value)
