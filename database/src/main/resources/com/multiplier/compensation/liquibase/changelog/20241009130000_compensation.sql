-- liquibase formatted sql

-- changeset Ujwal S:20241009130000_compensation

CREATE TABLE compensation.compensation (
    id                                  UUID                                PRIMARY KEY NOT NULL,
    company_id                          BIGINT                              NOT NULL,
    entity_id                           BIGINT                              NOT NULL,
    contract_id                         BIGINT                              NOT NULL,
    schema_item_id                      UUID                                NOT NULL,
    category                            VARCHAR(255)                        ,
    currency                            VARCHAR(3)                          NOT NULL,
    billing_rate_type                   compensation.billing_rate_type      NOT NULL,
    billing_rate                        DOUBLE PRECISION                    NOT NULL,
    billing_frequency                   compensation.billing_frequency      NOT NULL,
    pay_schedule_id                     UUID                                NOT NULL,
    start_date                          DATE                                NOT NULL,
    end_date                            DATE                                ,
    is_installment                      BOOLEAN                             NOT NULL,
    no_of_installments                  INTEGER                             ,
    previous_id                         UUID                                ,
    status                              compensation.compensation_status    NOT NULL,
    generated_installments              INTEGER                             ,
    processed_until_date                DATE                                ,
    created_on                          TIMESTAMP                           NOT NULL,
    created_by                          BIGINT                              NOT NULL,
    updated_on                          TIMESTAMP                           NOT NULL,
    updated_by                          BIGINT                              NOT NULL
);

CREATE INDEX compensation_contract_id_idx ON compensation.compensation (contract_id);

CREATE INDEX compensation_entity_id_idx ON compensation.compensation (entity_id);

CREATE INDEX compensation_status_start_date_processed_until_date_idx
    ON compensation.compensation (status, start_date, processed_until_date, is_installment, generated_installments, no_of_installments);

CREATE INDEX compensation_status_start_date_end_date_idx
    ON compensation.compensation (status, start_date, processed_until_date, is_installment, end_date);
