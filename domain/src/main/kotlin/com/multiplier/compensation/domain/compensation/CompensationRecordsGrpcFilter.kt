package com.multiplier.compensation.domain.compensation

import com.multiplier.compensation.domain.compensation.enums.CompensationState
import java.time.LocalDate

data class CompensationRecordsGrpcFilter(
    override val state: CompensationState? = null,
    override val activeAsOn: LocalDate? = null,
    val ids: List<String> = emptyList(),
    val entityId: Long? = null,
    val contractIds: List<Long> = emptyList(),
    val categories: List<String> = emptyList(),
    val activeFrom: LocalDate? = null,
    val activeTo: LocalDate? = null,
    val recordType: CompensationRecordType? = null,
    val recordTypes: List<CompensationRecordType>? = null,
) : CompensationFilter

enum class CompensationRecordType {
    DRAFT,
    APPLIED,
}
